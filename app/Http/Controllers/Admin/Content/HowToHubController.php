<?php

namespace App\Http\Controllers\Admin\Content;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreHubRequest;
use App\Http\Requests\Admin\UpdateHubRequest;
use App\Models\HowToHub;
use App\Models\HowToHubFile;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use App\Services\Content\HowToHubService;
use Illuminate\Http\JsonResponse;
use Yajra\DataTables\Facades\DataTables;

/**
 * Class HowToHubController
 *
 * Handles CRUD operations for the HowToHub model.
 *
 * @package App\Http\Controllers\Admin
 */
class HowToHubController extends Controller
{
    /**
     * How To Hub service instance.
     *
     * @var HowToHubService
     */
    public $service;

    /**
     * Instantiate a new controller instance.
     *
     * @param HowToHubService $service The service layer for managing How To Hubs.
     * @return void
     */
    public function __construct(HowToHubService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of HowToHub entries.
     *
     * @return View|JsonResponse The view displaying the list of hubs.
     */
    public function index(Request $request): View|JsonResponse
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        return view('admin.contents.howtohubs.index');
    }

    /**
     * Return a JSON response with the list of pages for DataTables.
     *
     * @param Request $request The current HTTP request instance.
     * @return JsonResponse The DataTable JSON response.
     */
    protected function list(Request $request): JsonResponse
    {
        $hubs = $this->service->getListForAdmin(true);
        $statuses = $this->service->getStatuses();

        return DataTables::of($hubs)
            ->addColumn('created_at', function ($hub) {
                return $hub->created_at;
            })
            ->addColumn('created_by', function ($hub) {
                return $hub->creator ? $hub->creator->name : 'N/A';
            })
            ->addColumn('updated_at', function ($hub) {
                return $hub->updated_at ? $hub->updated_at->format('Y-m-d H:i:s') : 'N/A';
            })
            ->addColumn('updated_by', function ($hub) {
                return $hub->updater ? $hub->updater->name : 'N/A';
            })
            
            ->addColumn('files', function ($hub) {
                $files = '<ul class="list-unstyled">';

                foreach ($hub->files as $file) {
                    $files .= '<li>
                        <a href="' . asset($file->file_url) . '" target="_blank">
                            <i class="fa fa-file-alt"></i>
                            ' . $file->file . '
                        </a>
                    </li>';
                }

                $files .= '</ul>';

                return $files;
            })
            ->addColumn('status', function ($hub) {
                $status = '';
                if ($hub->status == HowToHub::DRAFTED) {
                    $status = '<div style="background-color:#d35757;"  class="custom-rounded">Drafted</div>';
                } else {
                    $status = '<div style="background-color:#358b00;" class="custom-rounded">Published</div>';
                }

                return $status;
            })
            ->addColumn('action', function ($hub) use ($statuses) {
                $action = '';
                $action .= '<a href="' . route('admin.contents.howtohubs.edit', $hub) . '" class="btn btn-primary"><i class="fa fa-edit"></i> Edit</a> ';

                $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                Action
            </button>
            <ul class="dropdown-menu" style="">';

                foreach ($statuses as $name => $value) {
                    $disabled = ($hub->status == $value) ? 'disabled' : '';

                    $action .= '<li class="dropdown-item">';
                    $action .= '    <button class="btn btn-sm btn-primary change-status-btn ' . $disabled . '" data-id="' . $hub->id . '" ' . $disabled . ' data-statusname="' . $name . '" data-status="' . $value . '">' . $name . '</button>';
                    $action .= '</li>';
                }


                $action .= '<li class="dropdown-item">';
                $action .= '<a href="javascript:void(0);" class="btn btn-danger delete-btn" data-url="' . route('admin.contents.howtohubs.destroy', $hub) . '"><i class="fa fa-trash-alt"></i> Delete</a> ';
                $action .= '</li>';
                $action .= '</ul>';

                return $action;
            })
            ->rawColumns(['files', 'status', 'action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new HowToHub entry.
     *
     * @return View The view displaying the creation form.
     */
    public function create(): View
    {
        $statuses = $this->service->getStatuses();

        return view('admin.contents.howtohubs.create', compact('statuses'));
    }

    /**
     * Store a newly created HowToHub entry in storage.
     *
     * @param StoreHubRequest $request The incoming request containing form data.
     * @return RedirectResponse A redirect response back to the index page with a success message.
     */
    public function store(StoreHubRequest $request): RedirectResponse
    {
        $data = [
            'title'=>$request->title,
            'section' => $request->section,
            'status' => $request->status,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        $hub = $this->service->create($data);

        if ($request->has('files')) {
            $this->service->addFiles($hub, $request);
        }

        return redirect()->route('admin.contents.howtohubs.index')
            ->with('success', 'How To Hub entry created successfully.');
    }

    /**
     * Show the form for editing the specified HowToHub entry.
     *
     * @param HowToHub $howtohub The hub to edit.
     * @return View The view displaying the edit form.
     */
    public function edit(HowToHub $howtohub): View
    {
        $statuses = $this->service->getStatuses();

        return view('admin.contents.howtohubs.edit', compact('howtohub', 'statuses'));
    }

    /**
     * Update the specified HowToHub entry in storage.
     *
     * @param UpdateHubRequest $request The incoming request containing form data.
     * @param HowToHub $howtohub The hub to update.
     * @return RedirectResponse A redirect response back to the index page with a success message.
     */
    public function update(UpdateHubRequest $request, HowToHub $howtohub): RedirectResponse
    {
        $data = [
            'title' => $request->title,
            'section' => $request->section,
            'status' => $request->status,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'updated_by' => auth()->id(),
        ];

        $howtohub = $this->service->update($howtohub->id, $data);

        if ($request->has('files')) {
            $this->service->addFiles($howtohub, $request);
        }

        return redirect()->route('admin.contents.howtohubs.index')->with('success', 'How To Hub updated successfully.');
    }

    /**
     * Remove the specified HowToHub entry from storage.
     *
     * @param HowToHub $howtohub The hub to delete.
     * @return JsonResponse
     */
    public function destroy(HowToHub $howtohub): JsonResponse
    {
        $howtohub->files()->delete();
        $howtohub->delete();

        return response()->json(['message' => 'Deleted successfully']);
    }

    /**
     * Remove the specified HowToHub entry from storage.
     *
     * @param HowToHub $howtohub The hub to delete.
     * @return JsonResponse
     */
    public function destroyFile(Request $request): JsonResponse
    {
        $id = $request->id;

        $this->service->deleteFile($id);

        return response()->json(['message' => 'Deleted successfully']);
    }
    
    /**
     * changeStatus
     *
     * @param  mixed $request
     * @return void
     */
    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;
        $record = $this->service->find($id);

        if (!$record) {
            return response()->json(['message' => 'No Record Found']);
        }

        $this->service->update($id, ['status' => $status]);
            
        return response()->json(['message' => 'Status changed successfully']);
    }
}
