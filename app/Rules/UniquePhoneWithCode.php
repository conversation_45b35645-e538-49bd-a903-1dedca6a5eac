<?php

namespace App\Rules;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Session;

class UniquePhoneWithCode implements Rule
{
    protected $code;

    public function __construct($code) // Default to GB
    {
        $this->code = $code;
    }

    public function passes($attribute, $value)
    {
        if(!env('UNIQUE_PHONE')){
            return true;
        }
        $exists = User::where(['phone' => $this->code.$value])->first();

        if($exists){
            return auth()->check() ? $exists->id == auth()->user()->id : $exists->id == (int)Session::get('registeredUserId');
        }else{
            return true;
        }
        
    }

    public function message()
    {
        return 'This number is already in use. Please enter a different phone number.';
    }
}
