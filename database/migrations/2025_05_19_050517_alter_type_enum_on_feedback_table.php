<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTypeEnumOnFeedbackTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Change enum values and set default to 'like'
        DB::statement("ALTER TABLE feedback MODIFY COLUMN type ENUM('like', 'dislike', 'question') NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Revert to original enum values
        DB::statement("ALTER TABLE feedback MODIFY COLUMN type ENUM('like', 'dislike') NULL");
    }
}