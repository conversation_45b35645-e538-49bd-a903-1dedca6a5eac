<?php

namespace App\Http\Controllers\Frontend\ConnectHub;

use App\Events\UserToUserChatEvent;
use App\Http\Controllers\Controller;
use App\Models\EntityBlock;
use App\Models\UserChatDeletion;
use App\Models\UserConversation;
use App\Models\UserMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserToUserChatController extends Controller
{
    /**
     * Send a user-to-user message.
     */
    public function sendUserToUserMessage(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,id',
            'message' => 'required|string|max:255',
        ]);

        $userOne = min(auth()->id(), $request->receiver_id);
        $userTwo = max(auth()->id(), $request->receiver_id);

        $conversation = UserConversation::firstOrCreate([
            'user_one_id' => $userOne,
            'user_two_id' => $userTwo,
        ]);

        UserChatDeletion::where('conversation_id', $conversation->id)
            ->delete();

        $authId = Auth::id();
        $receiverId = $conversation->user_one_id == $authId ? $conversation->user_two_id : $conversation->user_one_id;

        // Check if sender is blocked by receiver
        $isBlocked = EntityBlock::where([
            'blocker_id' => $receiverId,
            'blocked_id' => $authId,
        ])->exists();


        $message = $conversation->messages()->create([
            'sender_id' => $authId,
            'message' => $request->message,
            'is_blocked' => $isBlocked,
            'blocked_at' => $isBlocked ? now() : null,
        ]);
        if (!$isBlocked) {
            broadcast(new UserToUserChatEvent($message, $request->receiver_id));
        }

        return response()->json([
            'message' => [
                'id' => $message->id,
                'sender_id' => $message->sender_id,
                'message' => $message->message,
                'time' => $message->created_at->format('H:i - d M'),
                'conversation_id' => $message->conversation_id,
                'sender' => [
                    'id' => $message->sender->id,
                    'name' => $message->sender->name,
                    'email' => $message->sender->email,
                ],
            ]
        ]);
    }

    /**
     * Load chat messages between the current user and another user.
     */
    public function loadUserToUserChat($receiverId)
    {
        $authId = Auth::id();

        $userOne = min($authId, $receiverId);
        $userTwo = max($authId, $receiverId);

        $conversation = UserConversation::where('user_one_id', $userOne)
            ->where('user_two_id', $userTwo)
            ->first();

        if (!$conversation) {
            return response()->json(['messages' => []]);
        }

        $messages = $conversation->messages()
            ->orderBy('created_at')
            ->where('is_blocked', false)
            ->get()
            ->map(function ($msg) {
                return [
                    'id' => $msg->id,
                    'sender_id' => $msg->sender_id,
                    'message' => $msg->message,
                    'time' => $msg->created_at->format('H:i-d M'),
                    'conversation_id' => $msg->conversation_id,
                ];
            });

        return response()->json(['messages' => $messages]);
    }
}
