<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class MapboxService
{
    protected string $baseUrl = 'https://api.mapbox.com/geocoding/v5/mapbox.places/';

    public function getCitySuggestions(string $query): array
    {
        $response = Http::get($this->baseUrl . urlencode($query) . '.json', [
            'access_token' => config('services.mapbox.token'),
            'types' => 'place',
            'limit' => 5,
        ]);

        if (!$response->successful()) {
            return [];
        }

        $features = $response->json()['features'] ?? [];

        return array_map(function ($feature) {
            $country = collect($feature['context'] ?? [])
                ->first(fn ($c) => str_starts_with($c['id'], 'country'));

            return [
                'city' => $feature['text'],
                'country_code' => strtoupper($country['short_code'] ?? ''),
                'country_name' => $country['text'] ?? '',
                'latitude' => $feature['center'][1],
                'longitude' => $feature['center'][0],
            ];
        }, $features);
    }
}
