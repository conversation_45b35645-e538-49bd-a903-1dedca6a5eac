<?php

namespace App\Repositories;
use App\Models\SubscriptionActive;

class SubscriptionRepository extends BaseRepository
{
    /**
     * The SubscriptionActive model instance.
     *
     * @var \App\Models\SubscriptionActive
     */
    protected $model;

    /**
     * CompanyRepository constructor.
     *
     * @param \App\Models\SubscriptionActive $subscription
     */
    public function __construct(SubscriptionActive $subscription)
    {
        $this->model = $subscription;
    }


    /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {
        return [
            'Pending' => $this->model::STATUS_PENDING,
            'Active' => $this->model::STATUS_ACTIVE,
            'Approve' => $this->model::STATUS_APPROVED,
            'Reject' => $this->model::STATUS_REJECTED,
        ];
    }



    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByMixedCondition(array $params)
    {
        $query = $this->model->select();

        /** filter by status */
        if (isset($params['status'])) {
            $query->where('paid_status', $params['status']);
        }
        return $query->with('company.pending_downgrade')->get();
    }
}