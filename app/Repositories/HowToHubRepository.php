<?php

namespace App\Repositories;

use App\Models\HowToHub;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class HowToHubRepository
 *
 * Handles data operations for the HowToHub model.
 */
class HowToHubRepository extends BaseRepository
{
    /**
     * The HowToHub model instance.
     *
     * @var \App\Models\HowToHub
     */
    protected $model;

    /**
     * HowToHubRepository constructor.
     *
     * @param \App\Models\HowToHub $howToHub
     */
    public function __construct(HowToHub $howToHub)
    {
        $this->model = $howToHub;
    }

    /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {        
       return [
            'Publish' => $this->model::PUBLISHED,
            'Draft' => $this->model::DRAFTED,
       ];
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return Collection
     */
    public function getByMixedCondition(array $params) : Collection
    {
        return $this->queryByMixedCondition($params)->get();
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return Builder
     */
    public function queryByMixedCondition(array $params) : Builder
    {
        $query = $this->model->select();

        return $query;
    }
}
