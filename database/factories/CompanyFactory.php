<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\OrganisationType;
use App\Models\OrganisationSize;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends Factory<Company>
 */
class CompanyFactory extends Factory
{
    protected $model = Company::class;

    public function definition(): array
    {
        $name = $this->faker->company;

        return [
            'organisation_type_id' => OrganisationType::factory(),
            'organisation_size_id' => OrganisationSize::factory(),
            'name' => $name,
            'registered_location' => 'IN',
            'slug' => Str::slug($name),
            'email' => $this->faker->unique()->companyEmail,
            'phone' => $this->faker->phoneNumber,
            'phone_prefix' => $this->faker->numerify('+###'),
            'year_founded' => $this->faker->optional()->year,
            'description' => $this->faker->optional()->paragraph,
            // 'logo' => $this->faker->optional()->imageUrl(200, 200, 'business', true, 'logo'),
            // 'cover_image' => $this->faker->optional()->imageUrl(800, 400, 'business', true, 'cover'),
            'website_url' => $this->faker->optional()->url,
            'workplace_verification' => $this->faker->boolean,
            'display_phone' => $this->faker->boolean,
            'phone_contact_title' => $this->faker->optional()->word,
            'chat_contact_title' => $this->faker->optional()->word,
            'compliance_summary' => $this->faker->optional()->text(200),
        ];
    }
}
