<?php

namespace App\Http\Controllers\Frontend\User;

use App\Http\Actions\CreateUserProfileAction;
use App\Http\Actions\UserProfileSettingsAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\AccountCloseReasonRequest;
use App\Http\Requests\Frontend\AccountCloseRequest;
use App\Http\Requests\Frontend\PasswordUpdateRequest;
use App\Http\Requests\Frontend\ProfileEmailUpdateRequest;
use App\Http\Requests\Frontend\ProfileMobileUpdateRequest;
use App\Http\Requests\Frontend\PublishProfileSettingRequest;
use App\Models\User;
use App\Services\TwilioOtpService;
use Illuminate\View\View;
use Exception;
use Illuminate\Foundation\Mix;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;

class UserProfileController extends Controller
{

    protected $profileSetting;
    protected $userProfile;
    protected $otpService;

    /**
     * CreateUserProfileController constructor.
     *
     * @param CreateUserProfileAction $userProfile
     */
    public function __construct(
        UserProfileSettingsAction $profileSetting,
        CreateUserProfileAction $userProfile,
        TwilioOtpService $otpService
    ) {
        $this->profileSetting = $profileSetting;
        $this->userProfile = $userProfile;
        $this->otpService = $otpService;
    }
    /**
     * Display the user profile settings view.
     */
    public function profileSetting()
    {
        if (Session::get('currentAccount') == 'supplier') {
            return Redirect::route('frontend.company.dashboard');
        }
        return view('frontend.user.user-profile-setting');
    }

    /**
     * Display the user profile settings view.
     */
    public function accountSetting()
    {
        if (Session::get('currentAccount') == 'supplier') {
            return Redirect::route('frontend.company.dashboard');
        }
        return view('frontend.user.account-setting');
    }

    public function publishProfileSetting(PublishProfileSettingRequest $request)
    {
        $data = $request->validated();
        try {
            //Updating the profile and  cover images
            $user = User::find(auth()->user()->id);
            //Updating profile photo
            if ($request->hasFile('org_profile_photo') && $request->hasFile('crp_profile_photo')) {
                $user->addMediaFromRequest('org_profile_photo')
                    ->toMediaCollection('org_profile_photo', 'public');
                $user->addMediaFromRequest('crp_profile_photo')
                    ->toMediaCollection('crp_profile_photo', 'public');
            }
            //Updating cover image 
            if ($request->hasFile('org_cover_image') && $request->hasFile('crp_cover_image')) {
                $user->addMediaFromRequest('org_cover_image')
                    ->toMediaCollection('org_cover_image', 'public');
                $user->addMediaFromRequest('crp_cover_image')
                    ->toMediaCollection('crp_cover_image', 'public');
            }

            //Updating basic details
            $this->userProfile->executeBasicDetails(
                [
                    'first_name' => $data['first_name'],
                    'last_name' => $data['last_name'],
                    'city' => $data['city'],
                    'country' => $data['country'],
                    'country_code' => $data['country_code'],
                    'latitude' => $data['latitude'],
                    'longitude' => $data['longitude'],
                ],
                auth()->user()
            );

            //Updating occupation details   
            $this->userProfile->executeOccupationDetails(
                [
                    'occupation' => isset($data['occupation']) ? $data['occupation'] : '',
                    'company_name' => $data['company_name'],
                    'categories' => isset($data['categories']) ? $data['categories'] : [],
                ],
                auth()->user()
            );

            //Updating user profile settings
            $this->profileSetting->executeProfileSettings(
                [
                    'about_me' => $data['about_me'],
                    'linkedin_link' => $data['linkedin_link'],
                    'facebook_link' => $data['facebook_link'],
                    'instagram_link' => $data['instagram_link'],
                    'public_profile_email' => isset($data['public_profile_email']) ?? 0,
                    'public_profile_number' => isset($data['public_profile_number']) ?? 0,
                    'preferred_search_location' => isset($data['preferred_search_location']) ?? 0
                ],
                auth()->user()->id
            );
            return response()->json([
                'status' => 'success',
                'redirect_url' => url()->previous(),  // Redirect back if needed
                'modalToShow' => config('settings.modal.confirmation_modal')   // The modal to show on the frontend
            ]);

        } catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }

    }

    public function profileEmailUpdate(ProfileEmailUpdateRequest $request)
    {
        $data = $request->validated();
        try {
            $this->profileSetting->sendUpdateEmailVerification($data);
            return response()->json(['message' => 'Code sent successfully.']);

        } catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }
    }

    public function profileMobileUpdate(ProfileMobileUpdateRequest $request)
    {
        $data = $request->validated();
        try {
            $phonePrefix = explode('@', $request->phone_prefix);
            $data['phone_prefix'] = $phonePrefix[1];
            $this->profileSetting->storeUpdateDetails($data);
            $this->otpService->sendOtp($request->phone_prefix . $request->number);
            return response()->json(['message' => 'OTP sent successfully.']);

        } catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }
    }

    public function sendVerificationLinkToOauthUser()
    {
        try {
            $this->profileSetting->sendLinkToOauthUser();
            return \Redirect::back()
                ->with('modalToShow', config('settings.modal.email_verification'));
        } catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()]);
        }
    }

    public function updatePassword(PasswordUpdateRequest $request)
    {
        $data = $request->validated();
        try {

            $user = auth()->user();
            $user->update([
                'password' => \Hash::make($data['password']),
            ]);

            return \Redirect::back()
                ->with('modalToShow', config('settings.modal.password_update'));

        } catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.change_password'));
        }
    }

    public function saveCloseAccountReason(AccountCloseReasonRequest $request)
    {
        $data = $request->validated();
        try {
            $this->profileSetting->storeCloseAccountReason($data);
            return \Redirect::back()
                ->with('modalToShow', $request->nextModalToDisplay());

        } catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }
    }

    public function closeUserAccount(AccountCloseRequest $request)
    {
        $data = $request->validated();
        try {
            $this->profileSetting->closeUserAccount($data);
            \Auth::logout();
            return redirect()->route('frontend.index')
                ->with('modalToShow', $request->nextModalToDisplay());

        } catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }
    }

    public function ajaxGetUserProfile($user_id)
    {
        try {
            $user = User::find($user_id);

            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Render the partial view with user data
            $html = view('frontend.supplier.partial.dynamic-profile', compact('user'))->render();

            return response()->json([
                'success' => true,
                'html' => $html,
            ]);
        } catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }
    }



}
