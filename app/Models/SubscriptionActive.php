<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubscriptionActive extends Model
{
    protected $table = 'subscription_active';
    protected $fillable = [
        'company_id',
        'stripe_subscription_id',
        'subscription_id',
        'stripe_customer_id',
        'user_id',
        'billing_cycle',
        'currency',
        'cost',
        'last_payment_status',
        'last_payment_date',
        'ended_at'
    ];
    
    /**
     * company
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }
    
    /**
     * subscription_plan
     *
     * @return BelongsTo
     */
    public function subscription_plan(): BelongsTo
    {
        return $this->belongsTo(StripeSubscription::class, 'stripe_subscription_id');
    }
    
    /**
     * user
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    
}
