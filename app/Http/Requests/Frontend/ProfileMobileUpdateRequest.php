<?php

namespace App\Http\Requests\Frontend;

use App\Rules\UniquePhoneWithCode;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use App\Rules\ValidPhoneNumberCode;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class ProfileMobileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone_prefix' => ['required', 'string'],
            'number' => [
                'required',
                new ValidPhoneNumberCode(request()->phone_prefix),
                new UniquePhoneWithCode(request()->phone_prefix)],
            'password' => ['required',
                'max:64',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
            ]
        ];
    }


    public function withValidator($validator)
    {
        if(auth()->check()){
            $validator->after(function ($validator) {
                if ($this->user()->phone === (request()->phone_prefix.request()->number) && !empty(auth()->user()->username)) {
                    $validator->errors()->add('number', 'The new mobile number must be different from your current mobile number.');
                }

                if (!Hash::check($this->password, $this->user()->password)) {
                    $validator->errors()->add('password', "Your password doesn’t match. Please try again");
                }
            });
        }
    }

}
