<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('service_region', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('service_id'); // Foreign key to services table
            $table->unsignedBigInteger('country_id'); // Foreign key to countries table
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
            $table->foreign('country_id')->references('id')->on('countries')->onDelete('cascade');

            // Unique constraint to prevent duplicate entries
            $table->unique(['service_id', 'country_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_region');
    }
};
