<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyBusinessSector
 *
 * Represents the relationship between companies and business sectors.
 *
 * @package App\Models
 */
class CompanyBusinessSector extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'business_sector_id',
    ];

    /**
     * Get the company associated with the company business sector.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the business sector associated with the company business sector.
     */
    public function businessSector()
    {
        return $this->belongsTo(BusinessSector::class);
    }
}
