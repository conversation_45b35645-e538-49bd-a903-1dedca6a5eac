<?php

namespace App\Http\Controllers\Admin\Supplier;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\CompanyRegistrationStatus;
use App\Notifications\SupplierRequestStatusChanged;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Services\CompanyService;

class BaseCompanyController extends Controller
{
    /**
     * CompanyService service
     *
     * @var CompanyService
     */
    public $service;

    /**
     * Role of the user
     *
     * string $role
     */
    public $role;

    /**
     * Page title based on the role of users
     *
     * string $title
     */
    public $title;

    /**
     * Route prefix based on the role of users
     *
     * @var $routePrefix
     */
    public $routePrefix;

    /**
     * Create a new controller instance.
     *
     * @param CompanyService $service
     * @param string $role
     * @param string $routePrefix
     * @param string $title
     * @return void
     */
    public function __construct(
        CompanyService $service,
        string $role,
        string $routePrefix,
        string $title
    ) {
        $this->service = $service;
        $this->role = $role;
        $this->routePrefix = $routePrefix;
        $this->title = $title;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        $user = $this->service->current();
        $status = $request->status ?? null;
        $role = $this->role;
        $title = $this->title;
        $routePrefix = $this->routePrefix;

        return view('admin.suppliers.index', compact('user', 'status', 'role', 'title', 'routePrefix'));
    }

    protected function list($request)
    {
        $status = $request->status;
        $company = $request->company??null;
        $user = $this->service->current();
        $entrys = $this->service->getListForAdmin($status,$company);

        return DataTables::of($entrys)
            ->addColumn('created_at', function ($entry) {
                return $entry->created_at;
            })
            ->addColumn('name', function ($entry) {
                return $entry->supplier->name;
            })
            ->addColumn('email', function ($entry) {
                return $entry->email;
            })
            ->addColumn('company', function ($entry) {
                return $entry->name;
            })
            ->addColumn('status', function ($entry) {
                $status = '';
                if ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_APPROVED) {
                    $status = '<div style="background-color:#358b00;" class="custom-rounded">Approve</div>';
                } elseif ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_CLOSED) {
                    $status = '<div style="background-color:#ff0404;" class="custom-rounded">Closed</div>';
                } elseif ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_FREEZED) {
                    $status = '<div style="background-color:#e0a800;" class="custom-rounded">Freezed</div>';
                }

                return $status;
            })
            ->addColumn('action', function ($entry) use ($user) {
                $action = '';
                if ($user->can('manage suppliers') || $user->can('view suppliers')) {
                    $action .= '<a href="' . route('admin.suppliers.show', $entry) . '" class="btn btn-primary">View</a> ';
                }

                if ($user->can('manage suppliers')) {
                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    Action
                                </button>
                                <ul class="dropdown-menu" style="">';


                    if ($entry->registrationStatus->status != CompanyRegistrationStatus::STATUS_CLOSED) {
                        $action .= '<li class="dropdown-item">';
                        $action .= '    <button class="btn btn-sm btn-warning change-status-btn" 
                                                            data-id="' . $entry->id . '" 
                                                            data-statusname="Freeze" 
                                                            data-status="' . CompanyRegistrationStatus::STATUS_FREEZED . '" '
                            . ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_FREEZED ? 'disabled' : '') . '>Freeze</button>';
                        $action .= '</li>';
                    }



                    $action .= '<li class="dropdown-item">';
                    $action .= '    <button class="btn btn-sm btn-danger change-status-btn" 
                                                        data-id="' . $entry->id . '" 
                                                        data-statusname="Close" 
                                                        data-status="' . CompanyRegistrationStatus::STATUS_CLOSED . '" '
                        . ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_CLOSED ? 'disabled' : '') . '>Close</button>';
                    $action .= '</li>';



                    $action .= '</ul>';
                }
                return $action;
            })
            ->addColumn('phone', function ($entry) {
                return $entry->supplier->full_phone;
            })
            ->addColumn('country', function ($entry) {
                $mainLocation = $entry->locations->where('is_main', 1)->first();
                return optional(optional($mainLocation)->country)->name ?? 'N/A';

            })
            ->rawColumns(['status', 'action'])
            ->toJson();
    }

    public function parseShow(Company $supplier)
    {
        $title = $this->title;
        $routePrefix = $this->routePrefix;
        $userListLink = route($routePrefix . 'index');

        return view('admin.suppliers.show', compact('supplier', 'userListLink', 'title', 'routePrefix'));
    }



    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;

        $company = $this->service->find($id);

        if (!$request->ajax()) {
            if (!$company) {
                return back()->withErrors('No company Found');
            }

            $company->registrationStatus->update([
                'status' => $status,
                'admin_comments' => $request->admin_comments,
                'reviewed_at' => \Carbon\Carbon::now(),
            ]);

            $company->supplier->notify(new SupplierRequestStatusChanged($status == CompanyRegistrationStatus::STATUS_APPROVED ? 'approved' : 'rejected'));
            return back()->with('status', 'Status changed successfully');
        }

        if (!$company) {
            return response()->json(['message' => 'No supplier Found']);
        }

        $company->registrationStatus->update([
            'status' => $status,
            'admin_comments' => $request->admin_comments,
            'reviewed_at' => \Carbon\Carbon::now(),
        ]);
        $company->supplier->notify(new SupplierRequestStatusChanged($status == CompanyRegistrationStatus::STATUS_APPROVED ? 'approved' : 'rejected'));
        return response()->json(['message' => 'Status changed successfully']);
    }
}
