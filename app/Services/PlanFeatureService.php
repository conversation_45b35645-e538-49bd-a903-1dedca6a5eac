<?php

namespace App\Services;



use App\Models\StripeSubscription;
use App\Models\SubscriptionFeature;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use App\Models\PlanFeature;


class PlanFeatureService
{


    /**
     * getPlanFeatures
     *
     * @return Collection
     */
    public function getPlanFeatures(?string $subscriptionName = null): Collection
    {
        $query = PlanFeature::where('feature_type', PlanFeature::FEATURE_TYPE_PLATFORM);
       
        if (!is_null($subscriptionName)) {
            $query->whereHas('subscriptionFeatures.subscription', function ($q) use ($subscriptionName) {
                $q->where('name', $subscriptionName);
            });
        }
        return $query->get();
    }

    /**
     * Get current loggedin user
     *
     * @return App\Models\User
     */
    public function current()
    {
        return Auth::user();
    }

    public function create(array $data): PlanFeature
    {
        return PlanFeature::create($data);
    }

    public function assign(PlanFeature $planFeature, array $data): void
    {
        $subscription = StripeSubscription::find($data['stripe_subscription_id']);

        if ($planFeature && $subscription) {
            $exists = SubscriptionFeature::where('feature_id', $planFeature->id)
                ->where('stripe_subscription_id', $subscription->id)
                ->exists();

            if (!$exists) {
                SubscriptionFeature::create([
                    'stripe_subscription_id' => $subscription->id,
                    'feature_id' => $planFeature->id,
                    'enabled' => $data['enabled'],
                    'limit' => $data['limit'] ?? null,
                ]);
            }
        }
    }

}