<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('plans');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->text('description')->nullable();
            $table->tinyInteger('billing_cycle')->default(1);
            $table->float('price')->default(0);
            $table->integer('duration')->default(0);
            $table->tinyInteger('has_trial')->default(0);
            $table->integer('trial_period_days')->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
        });
    }
};
