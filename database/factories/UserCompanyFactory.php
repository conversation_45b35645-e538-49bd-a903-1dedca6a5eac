<?php

namespace Database\Factories;

use App\Models\UserCompany;
use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<UserCompany>
 */
class UserCompanyFactory extends Factory
{
    protected $model = UserCompany::class;

    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'user_id' => User::factory(),
            'position' => $this->faker->jobTitle,
            'author' => $this->faker->boolean,
        ];
    }
}
