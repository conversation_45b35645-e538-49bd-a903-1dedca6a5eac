<?php

namespace App\Http\Controllers\Admin\Categories;

use App\Http\Requests\Admin\UpdateCategoryRequest;
use App\Models\Category;
use App\Services\CategoryService;

/**
 * Handles administrative operations for managing categories.
 */
class ServiceCategoryController extends BaseCategoryController
{
    /**
     * Create a new controller instance.
     *
     * @param CategoryService $service The service for managing category-related business logic.
     */
    public function __construct(CategoryService $service)
    {
        $type = Category::TYPE_SERVICE;
        $routePrefix = 'admin.service-categories.';
        $title = 'Service Category';
        
        parent::__construct($service, $type, $routePrefix, $title);       
    }

    /**
     * Display the specified category details.
     *
     * @param Category $category The category to display.
     * @return \Illuminate\View\View The view with category details.
     */
    public function show(Category $service_category)
    {
        return $this->parseShow($service_category);
    }

    /**
     * Show the form for editing the specified category.
     *
     * @param Category $service_category The category to edit.
     * @return \Illuminate\View\View The form view for editing the category.
     */
    public function edit(Category $service_category)
    {
        return $this->parseEdit($service_category);
    }

    /**
     * Update the specified category in storage.
     *
     * @param UpdateCategoryRequest $request The validated request for updating a category.
     * @param Category $service_category The category to update.
     * @return \Illuminate\Http\RedirectResponse A redirect response to the categories index page with a success message.
     */
    public function update(UpdateCategoryRequest $request, Category $service_category)
    {
        return $this->parseUpdate($request, $service_category);
    }

    /**
     * Remove the specified category from storage.
     *
     * @param Category $service_category The category to delete.
     * @return \Illuminate\Http\RedirectResponse A redirect response to the categories index page with a success message.
     */
    public function destroy(Category $service_category)
    {
        return $this->paraseDestroy($service_category);
    }
}
