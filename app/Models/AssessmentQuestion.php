<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class AssessmentQuestion
 *
 * @property int $id
 * @property int $section_id
 * @property string $question
 * @property bool $required
 */
class AssessmentQuestion extends Model
{
    protected $fillable = [
        'section_id',
        'question',
        'required',
    ];

    public function section()
    {
        return $this->belongsTo(AssessmentSection::class);
    }
}
