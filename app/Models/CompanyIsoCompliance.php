<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyIsoCompliance
 *
 * Represents the ISO compliance information of a company.
 *
 * @package App\Models
 */
class CompanyIsoCompliance extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'code',
        'title',
        'certificate_number',
        'notes',
    ];

    /**
     * Get the company associated with the ISO compliance.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
