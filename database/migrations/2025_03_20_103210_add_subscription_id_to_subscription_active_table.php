<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_active', function (Blueprint $table) {
            $table->string('subscription_id')->after('stripe_subscription_id')->comment('Subscription id on stripe dashboard');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_active', function (Blueprint $table) {
            $table->dropColumn('subscription_id');
        });
    }
};
