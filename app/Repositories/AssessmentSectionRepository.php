<?php

namespace App\Repositories;

use App\Models\AssessmentSection;

/**
 * Class AssessmentSectionRepository
 *
 * Handles data operations for the AssessmentSection model.
 */
class AssessmentSectionRepository extends BaseRepository
{
    /**
     * The AssessmentSection model instance.
     *
     * @var \App\Models\AssessmentSection
     */
    protected $model;

    /**
     * AssessmentSectionRepository constructor.
     *
     * @param \App\Models\AssessmentSection $assessmentSection
     * @return void
     */
    public function __construct(AssessmentSection $assessmentSection)
    {
        $this->model = $assessmentSection;
    }
}
