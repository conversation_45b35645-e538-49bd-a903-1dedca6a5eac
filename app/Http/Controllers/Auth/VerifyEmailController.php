<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\VerifyEmailOtpRequest;
use App\Models\OTP;
use App\Models\User;
use App\Services\OtpService;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Exception;

class VerifyEmailController extends Controller
{
    public function __construct(protected OtpService $otp)
    {
        $this->otp = $otp;
    }

    /**
     * Handle the submission of the email OTP.
     *
     * @param VerifyEmailOtpRequest $request
     * @return RedirectResponse
     */
    public function __invoke(VerifyEmailOtpRequest $request): RedirectResponse
    {
        $data = $request->validated();
        try{
            $code = (int) implode('', $data['otp']);
            if(auth()->check() && !empty(auth()->user()->username)){
               $user = auth()->user();
               $otpEmail = OTP::where('user_id', $user->id)->first();
               $email = $otpEmail->email;
               if(Session::has('verifyType')){
                    switch(session()->get('verifyType')){
                        case('oAuth');
                            return $this->otp->verify(
                                'email',
                                $email,
                                $code,
                                $request->verifyForOauthEmailUpdate()
                            );
                            break;  
                        case('verifyCompanyEmail');
                            return $this->otp->verifyForCompanyEmail(
                                'email',
                                $email,
                                $code,
                                $request->currentModalToDisplay()
                            );
                        case('emailUpdate');
                            return $this->otp->verifyForEmailUpdate(
                                'email',
                                $email,
                                $code,
                                $request->currentModalToDisplay()
                            );
                            break;   
                        case('companyEmailUpdate');
                            return $this->otp->verifyForCompanyEmailUpdate(
                                'email',
                                $email,
                                $code,
                                $request->currentModalToDisplay()
                            );
                            break;   
                    }
               }
            }else{
               $user = User::find(Session::get('registeredUserId'));
               $email = $user->email;
               return $this->otp->verifyForUserRegistration(
                    'email',
                    $email,
                    $code,
                    $request->currentModalToDisplay()
                );
            }

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.email_verification'));
        }
        
    }

    /**
     * Resend the OTP.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resend(): RedirectResponse
    {
        try{
            if(auth()->check() && !empty(auth()->user()->username)){
                $otp = OTP::where('user_id', auth()->user()->id)->first();
                $user = auth()->user();
                $user->sendEmailVerification($otp->email);
                
            }else{
                $user = User::find(Session::get('registeredUserId'));
                $email = $user->email;
                $user = User::where('email', $email)->firstOrFail();
                $user->sendEmailVerification();
            }
            
            return \Redirect::back()->with('status', 'OTP resent successfully')
                ->with('modalToShow', config('settings.modal.email_verification'));

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.email_verification'));
        }
    
    }
}
