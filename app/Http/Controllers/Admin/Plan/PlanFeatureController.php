<?php

namespace App\Http\Controllers\Admin\Plan;

use App\Http\Controllers\Controller;
use App\Models\PlanFeature;
use App\Models\StripeSubscription;
use App\Models\SubscriptionFeature;
use App\Services\PlanFeatureService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Yajra\DataTables\Facades\DataTables;

class PlanFeatureController extends Controller
{

    /**
     * Plan Feature service instance.
     *
     * @var PlanFeatureService
     */
    public $service;

    /**
     * routePrefix
     *
     * @var mixed
     */
    public $routePrefix;

    /**
     * Instantiate a new controller instance.
     *
     * @param PlanFeatureService $service The service layer for managing plan feature.
     * @return void
     */
    public function __construct(PlanFeatureService $service)
    {
        $this->service = $service;
        $this->routePrefix = 'admin.plan-features.';
    }
    /**
     * Display a listing of HowToHub entries.
     *
     * @return View|JsonResponse The view displaying the list of hubs.
     */
    public function index(Request $request): View|JsonResponse
    {

        if ($request->ajax()) {
            return $this->list($request);
        }
        $status = $request->status;
        $routePrefix = $this->routePrefix;
        $subscriptions = StripeSubscription::all();
        return view('admin.planFeature.index', [
            'routePrefix' => $routePrefix,
            'title' => 'Plan Feature',
            'status' => $status,
            'subscriptions' => $subscriptions,
        ]);

    }

    /**
     * Return a JSON response with the list of pages for DataTables.
     *
     * @param Request $request The current HTTP request instance.
     * @return JsonResponse The DataTable JSON response.
     */
    protected function list(Request $request): JsonResponse
    {
        $user = $this->service->current();
        $subscriptionName = $request->input('subscription');
        $planFeatures = $this->service->getPlanFeatures($subscriptionName);
        
        return DataTables::of($planFeatures)
            ->addColumn('feature_name', function ($planFeature) {
                return $planFeature->name;
            })
            ->addColumn('created_at', function ($planFeature) {
                return $planFeature->created_at->format('Y-M-d H:i:s');
            })

            ->addColumn('is_assigned', function ($planFeature) {
                $assignedSubscriptions = SubscriptionFeature::with('subscription')
                    ->where('feature_id', $planFeature->id)
                    ->get()
                    ->pluck('subscription.name')
                    ->filter()
                    ->implode(', ');

                return $assignedSubscriptions ?: 'None';
            })

            ->addColumn('updated_at', function ($planFeature) {
                return $planFeature->updated_at->format('Y-M-d H:i:s');
            })
            
            ->addColumn('updated_by', function ($planFeature) {
                return $planFeature->user?->username?:$planFeature->user?->name;
            })
            

            ->addColumn('action', function ($planFeature) use ($user) {
                $action = '';
                if ($user->can('manage plan features') || $user->can('view plan features')) {

                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    Action
                                </button>
                                <ul class="dropdown-menu" style="">';

                    $action .= '<li class="dropdown-item">';
                    $action .= '<a href="' . route('admin.plan-features.edit', $planFeature) . '" class="btn btn-primary"><i class="fa fa-edit">&nbsp;Edit</i></a> ';
                    $action .= '</li>';
                    $action .= '<li class="dropdown-item">';

                    $action .= '<a href="' . route($this->routePrefix . 'assign', $planFeature) . '" class="btn btn-primary"><i class="fa fa-user-plus">&nbsp;Assign</i></a> ';

                    $action .= '</li></ul>';
                }

                return $action;
            })
            ->rawColumns(['status', 'action'])
            ->toJson();
    }



    /**
     * Display the plan feature edit form.
     */
    public function edit(PlanFeature $planFeature): View
    {

        $routePrefix = 'admin.plan-features.';
        $title = 'Plan Feature';
        return view('admin.planFeature.edit', [
            'planFeature' => $planFeature,
            'routePrefix' => $routePrefix,
            'title' => $title,
        ]);
    }


    /**
     * Update the plan feature information.
     */
    public function update(Request $request, PlanFeature $planFeature): RedirectResponse
    {
        $rules = [
            'description' => 'nullable|string|max:500',
            'status' => 'required|boolean',
            'limit' => 'nullable|integer',
        ];

        if ($planFeature->feature_type == PlanFeature::FEATURE_TYPE_PLATFORM) {
            $rules['name'] = 'required|string|max:255';
        }

        $validated = $request->validate($rules);
        $validated['updated_by'] = $this->service->current()->id;
        $planFeature->update($validated);

        return back()->with('status', 'Plan feature updated successfully!');
    }


    /**
     * Show the form for creating a new plan feature.
     */
    public function create(): View
    {
        $routePrefix = 'admin.plan-features.';
        $title = 'Plan Feature';
        $stripeSubscriptions = StripeSubscription::all();
        return view('admin.planFeature.create', [
            'routePrefix' => $routePrefix,
            'title' => $title,
            'stripeSubscriptions' => $stripeSubscriptions,
        ]);
    }
    /**
     * Store a newly created plan feature in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate(
            [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'status' => 'required|boolean',
            ],
            [
                'name.required' => 'The Plan Feature Name field is required.',
            ]
        );
        $validated['feature_type'] = 2; // 2 is the feature type for platform
        $this->service->create($validated);

        return redirect()->route('admin.plan-features.index')->with('status', 'Plan feature created successfully!');
    }


    /**
     * Show the form for assigning a plan feature to a subscription.
     */
    public function assignToSubscriptionFeature(PlanFeature $planFeature): View
    {
        $routePrefix = 'admin.plan-features.';
        $title = 'Plan Feature';
        $stripeSubscriptions = StripeSubscription::all();
        return view('admin.planFeature.assign', [
            'planFeature' => $planFeature,
            'routePrefix' => $routePrefix,
            'title' => $title,
            'stripeSubscriptions' => $stripeSubscriptions,
        ]);
    }
    /**
     * Store the assigned plan feature to a subscription.
     */
    public function assignToSubscriptionFeatureStore(Request $request, PlanFeature $planFeature): RedirectResponse
    {
        $validated = $request->validate(
            [
                'stripe_subscription_id' => 'required|exists:stripe_subscriptions,id',
                'limit' => 'nullable|integer',
                'enabled' => 'required|boolean',
            ],
            [
                'stripe_subscription_id.required' => 'The Stripe Subscription field is required.',
                'stripe_subscription_id.exists' => 'The selected Stripe Subscription is invalid.',
            ]
        );
        $validated['updated_by'] = $this->service->current()->id;
        $this->service->assign($planFeature, $validated);

        return redirect()->route('admin.plan-features.index')
            ->with('status', 'Plan feature assigned successfully!');
    }

}
