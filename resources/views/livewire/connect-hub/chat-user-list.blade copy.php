<div>
    <div class="search-area">
        <div class="search-element">
            <img src="{{ asset('images/icons/search-v.svg') }}" alt="search" />
            <input type="search" wire:model.live="search" class="form-control" placeholder="Search" />
        </div>


        <div class="dropdown filter-group modal-filter">
            <button class="btn dropdown-toggle" type="button" id="dropdownFilterMenu" data-bs-toggle="dropdown"
                aria-expanded="false">
                <span>Filter ({{ $filter ? 1 : 0 }})</span>
                <img src="{{ asset('images/icons/fillter-drop.svg') }}" alt="dropdown" />
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownFilterMenu">
                <li><a wire:click="$set('filter', 'all')"
                        class="dropdown-item {{ $filter === 'all' ? 'active' : '' }}">All</a></li>
                <li><a wire:click="$set('filter', 'unread')"
                        class="dropdown-item {{ $filter === 'unread' ? 'active' : '' }}">Unread</a></li>
                <li><a class="dropdown-item" href="#" wire:click.prevent="setFilter('Users')">Users</a></li>
                <li><a class="dropdown-item" href="#" wire:click.prevent="setFilter('Suppliers')">Suppliers</a>
                </li>
            </ul>
        </div>
    </div>

    <h3 class="tab-heading">
        {{-- <span>Chats</span> --}}
        <div class="dropdown conversation-toggle">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="conversationTypeDropdown"
                data-bs-toggle="dropdown" aria-expanded="false">
                {{ $conversationView === 'archived' ? 'Archived Conversations' : 'Conversations' }}
            </button>
            <ul class="dropdown-menu" aria-labelledby="conversationTypeDropdown">
                <li><a wire:click="$set('conversationView', 'live')"
                        class="dropdown-item {{ $conversationView === 'live' ? 'active' : '' }}">Conversations</a></li>
                <li><a wire:click="$set('conversationView', 'archived')"
                        class="dropdown-item {{ $conversationView === 'archived' ? 'active' : '' }}">Archived
                        Conversations</a></li>
            </ul>
        </div>

        <a href="#" id="showSelect" class="checkbox-notSelected">select</a>
    </h3>

    {{-- searchable users list --}}
    @if (!$searchableUsers->isEmpty())
        <ul class="chat-contianer">
            @forelse ($searchableUsers as $user)
                <li>
                    <div class="list-box">
                        <div class="left">
                            <input type="checkbox" class="select-checkbox chat-checkbox hide-select">
                            <div class="avatar">
                                <img src="{{ $user->getFirstMedia('profile_photo') ? $user->getFirstMedia('profile_photo')->getUrl() : asset('images/dashboard/user/profile-pic.png') }}"
                                    alt="person" />
                                <div class="dropdown">
                                    <button class="btn btn-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                        <i class="fa fa-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li class="chat not-active"><a class="dropdown-item open-chat" href="#"
                                                data-user-id="{{ $user->id }}" data-user-name="{{ $user->name }}"
                                                data-user-location="New York" data-bs-toggle="modal"
                                                data-bs-target="#chatMessageModal">Open Chat</a></li>
                                        <li><a class="dropdown-item load-dynamic-profile"
                                                data-user-id="{{ $user->id }}" href="#">View Profile</a></li>
                                        <li><a class="dropdown-item" href="#">Delete Chat</a></li>
                                        <li><a class="dropdown-item" href="#">Report</a></li>
                                        <li><a class="dropdown-item" href="#">Block</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="details">
                                <h4>{{ $user->name }}</h4>
                                <p>
                                    {{ $user->user_company?->position }}{{ $user->user_company?->position ? ' @ ' . $user->company?->name : '' }}
                                </p>

                                <div class="message">{{ $user->last_message ?? 'No messages yet' }}</div>
                            </div>
                        </div>
                        <div class="right">
                            <span class="time">11:15</span>
                            <button class="btn btn-secondary chat not-active open-chat"
                                data-user-id="{{ $user->id }}" data-user-name="{{ $user->name }}"
                                data-user-location="New York" data-bs-toggle="modal" data-bs-target="#chatMessageModal">
                                <span>chat @if ($user->unread_count > 0)
                                        ({{ $user->unread_count }})
                                    @endif
                                </span>
                            </button>

                        </div>
                    </div>
                </li>
            @empty
            @endforelse
        </ul>
    @endif
    {{-- end searchable users list --}}

    {{-- chat users list --}}

    @if (!$search)
        <ul class="chat-contianer">
            <li class="total-user-count" data-user-count="{{ $users->count() }}"></li>
            @forelse ($users as $user)
                <li>
                    <div class="list-box">
                        <div class="left">
                            <input type="checkbox" class="select-checkbox chat-checkbox hide-select">
                            <div class="avatar">
                                <img src="{{ $user->getFirstMedia('profile_photo') ? $user->getFirstMedia('profile_photo')->getUrl() : asset('images/dashboard/user/profile-pic.png') }}"
                                    alt="person" />
                                <div class="dropdown">
                                    <button class="btn btn-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                        <i class="fa fa-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li class="chat not-active"><a class="dropdown-item open-chat" href="#"
                                                data-user-id="{{ $user->id }}"
                                                data-user-name="{{ $user->name }}" data-user-location="New York"
                                                data-bs-toggle="modal" data-bs-target="#chatMessageModal"
                                                wire:click="markMessagesAsRead({{ $user->id }})">Open Chat</a>
                                        </li>
                                        <li><a class="dropdown-item load-dynamic-profile"
                                                data-user-id="{{ $user->id }}" href="#">View Profile</a>
                                        </li>
                                        <li><a class="dropdown-item" href="#">Delete Chat</a></li>
                                        <li><a class="dropdown-item" href="#">Report</a></li>
                                        <li><a class="dropdown-item" href="#">Block</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="details">
                                <h4>{{ $user->name }}</h4>
                                <p>
                                    {{ $user->user_company?->position }}{{ $user->user_company?->position ? ' @ ' . $user->company?->name : '' }}
                                </p>

                                <div class="message">{{ $user->last_message ?? 'No messages yet' }}</div>
                            </div>
                        </div>
                        <div class="right">
                            <span class="time">
                                {{ $user->last_message_time ? \Carbon\Carbon::parse($user->last_message_time)->format('H:i') : '' }}
                            </span>

                            <button
                                class="btn btn-secondary chat @if ($user->unread_count == 0) not-active @endif open-chat"
                                data-user-id="{{ $user->id }}" data-user-name="{{ $user->name }}"
                                data-user-location="New York" data-bs-toggle="modal"
                                data-bs-target="#chatMessageModal"
                                wire:click="markMessagesAsRead({{ $user->id }})">
                                <span>chat @if ($user->unread_count > 0)
                                        ({{ $user->unread_count }})
                                    @endif
                                </span>
                            </button>

                        </div>
                    </div>
                </li>
            @empty
                <li>
                    <div class="text-center p-3">No
                        conversations{{ $filter === 'unread' ? ' with unread messages' : '' }}.</div>
                </li>
            @endforelse
        </ul>
    @endif
    {{-- end chat users list --}}
</div>
