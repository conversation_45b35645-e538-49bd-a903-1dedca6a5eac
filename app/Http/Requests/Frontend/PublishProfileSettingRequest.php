<?php

namespace App\Http\Requests\Frontend;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PublishProfileSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'about_me' => ['nullable', 'string', 'max:1000'],
            'org_profile_photo' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'crp_profile_photo' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'org_cover_image' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'crp_cover_image' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'first_name' => ['required', 'string', 'max:255', 'regex:/^[\p{L}\s]+$/u'],
            'last_name' => ['required', 'string', 'max:255', 'regex:/^[\p{L}\s]+$/u'],
            'city' => ['required', 'string', 'max:255'],
            'country' => ['required', 'string', 'max:255'],
            'country_code' => ['nullable', 'string'],
            'latitude' => ['nullable', 'string'],
            'longitude' => ['nullable', 'string'],
            'preferred_search_location' => ['nullable'],
            'occupation' => ['nullable', 'string', 'max:255'],
            'company_name' => ['nullable', 'string', 'max:255'],
            'categories' => ['nullable', 'array', 'min:1', 'max:6'],
            'linkedin_link' => ['nullable', 'string', 'max:2000', 'regex:/^(https?:\/\/)?(www\.)?linkedin\.com\/(in|pub)\/[a-zA-Z0-9_\-]+\/?$/'],
            'facebook_link' => ['nullable', 'string', 'max:2000', 'regex:/^(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9.\-_]+\/?$/'],
            'instagram_link' => ['nullable', 'string', 'max:2000', 'regex:/^(https?:\/\/)?(www\.)?instagram\.com\/[a-zA-Z0-9._]+\/?$/'],
            'public_profile_email' => ['nullable'],
            'public_profile_number' => ['nullable'],
        ];
    }
}
