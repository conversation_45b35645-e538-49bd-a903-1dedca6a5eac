<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GeoCountry extends Model
{
    public $timestamps = false;
    protected $fillable = [
        'continent_id',
        'code',
        'country_name'
    ];

    public function continent()
    {
        return $this->belongsTo(GeoContinent::class);
    }

    public function organisation_type_locals(){

        return $this->hasMany(OrganisationTypeLocal::class, 'iso_code', 'code');
    }

    public function country(){
        return $this->belongsTo(Country::class, 'code', 'code');
    }
}
