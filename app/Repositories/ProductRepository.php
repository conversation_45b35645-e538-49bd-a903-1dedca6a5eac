<?php

namespace App\Repositories;

use App\Models\Product;

/**
 * Class CompanyRepository
 *
 * Handles data operations for the Product model.
 */
class ProductRepository extends BaseRepository
{
    /**
     * The Product model instance.
     *
     * @var \App\Models\Product
     */
    protected $model;

    /**
     * ProductRepository constructor.
     *
     * @param \App\Models\Product $product
     */
    public function __construct(Product $product)
    {
        $this->model = $product;
    }


    /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {
        return [
            'Active' => $this->model::STATUS_ACTIVE,
            'Inactive' => $this->model::STATUS_INACTIVE,
        ];
    }


    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByMixedCondition(array $params)
    {
        return $this->model
            ->with('company','categories')
            ->when(isset($params['status']), function ($query) use ($params) {
                $query->where('listing_status', $params['status']);
            })
            ->where('type', $params['type'])
            //->orderByDesc('id')
            ->get();
    }


}
