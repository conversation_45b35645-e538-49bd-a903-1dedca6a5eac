<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('company_assessment_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assessment_id')->constrained('company_assessments')->onDelete('cascade');
            $table->foreignId('section_id')->constrained('assessment_sections')->onDelete('cascade');
            $table->string('path');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('company_assessment_documents');
    }
};
