<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * Class CaseStudy
 *
 * Represents a case study for a company, with its details and associated images.
 *
 * @package App\Models
 */
class CaseStudy extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'company_id',
        'title',
        'date',
        'client',
        'country_id',
        'city',
        'story',
    ];


    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('org_case_study_image');
        $this->addMediaCollection('crp_case_study_image');
    }

    /**
     * Get the company associated with the case study.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the country associated with the case study.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function geo_country()
    {
        return $this->belongsTo(GeoCountry::class, 'country_id', 'id');
    }

    /**
     * Get the images associated with the case study.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function images()
    {
        return $this->hasMany(CaseStudyImage::class);
    }

    public function case_study_date(){
        if($this->date == null){
            return '';
        }
        // Parse the date
        $date = Carbon::createFromFormat('Y-m-d', $this->date);

        // Format it with ordinal suffix for day
        $formatted = $date->format('jS M, Y');

        return $formatted; // Output: 12th May, 2025
    }
}
