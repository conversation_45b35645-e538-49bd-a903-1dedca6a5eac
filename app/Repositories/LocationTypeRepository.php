<?php

namespace App\Repositories;

use App\Models\LocationType;

/**
 * Class LocationTypeRepository
 *
 * Handles data operations for the LocationType model.
 */
class LocationTypeRepository extends BaseRepository
{
    /**
     * The LocationType model instance.
     *
     * @var \App\Models\LocationType
     */
    protected $model;

    /**
     * LocationTypeRepository constructor.
     *
     * @param \App\Models\LocationType $locationType
     */
    public function __construct(LocationType $locationType)
    {
        $this->model = $locationType;
    }
}
