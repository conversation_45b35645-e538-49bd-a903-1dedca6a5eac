<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyContinent extends Model
{
    public $timestamps = false;

    protected $fillable = ['company_id', 'continent_id'];

    public function continent()
    {
        return $this->belongsTo(GeoContinent::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function company_continent_countries()
    {
        return $this->hasMany(CompanyCountry::class, 'company_continent_id');
    }
}
