<?php

namespace App\Http\Controllers\Frontend\Product;

use ZipArchive;
use App\Models\User;
use App\Models\Company;
use App\Models\Country;
use App\Models\Product;
use App\Models\Category;
use App\Services\IPService;
use App\Services\CategoryService;
use Faker\Factory as Faker;
use Illuminate\Support\Str;
use App\Models\LocationType;
use Illuminate\Http\Request;
use OpenSearch\ClientBuilder;
use App\Models\ProductPricing;
use App\Models\OrganisationSize;
use App\Models\OrganisationType;
use App\Models\OrganisationTypeLocal;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Redirect;

class ProductController extends Controller
{
    protected $ipApi;
    protected $categoryService;

    /**
     * Create a new instance of the controller.
     *
     * @param IpService $ipService
     * @param CategoryService $categoryService
     */
    public function __construct(IPService $ipApi, CategoryService $categoryService)
    {
        $this->ipApi = $ipApi;
        $this->categoryService = $categoryService;
    }
    /**
     * Display a listing of products and services with search and filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $getData = $this->ipApi->getSearchLocation();
        $queryParams = $getData['queryParams'];
        $location = $getData['location'];
        $dropdownItems = [
            'best_match' => 'Best Match',
            'feedback' => 'Feedback',
            'newly_added' => 'Newly Added',
            'location_nearest' => 'Location (Nearest First)',
            'price_low_high' => 'Price Low - High',
            'price_high_low' => 'Price High - Low',
        ];
        // Fetch all categories for filtering
        // $categories = Category::all();

        $getCategoryFilterData = $this->categoryService->getCategoryFilterData(true);

        $sort = $request->input('sort', '');
        $filter = $request->input('filter', '');
        $quickFilter = request('quick_filter');
        $perPage = config('services.per_page.default'); // Default items per page

        if (!empty($queryParams) || !empty($queryParams) || !empty($sort) || !empty($filter)) {
            // Perform search with location filter
            $rawResults = Product::search($queryParams)->raw();
            //dd($rawResults);

            // Initialize typeCounts array
            $typeCounts = $rawResults['typeCounts'];

            // Extract total counts for each type
            $totalProducts = $typeCounts['product'] ?? 0;
            $totalServices = $typeCounts['service'] ?? 0;

            // Convert raw results into Laravel Collection for pagination
            $productIds = collect($rawResults['results'])->pluck('_id')->toArray();    
            // Fetch products from the Product model using the extracted IDs
            if ($quickFilter) {
                $searchResults = Product::whereIn('id', $productIds)->where('type', $quickFilter);
            } else {
                $searchResults = Product::whereIn('id', $productIds);
            }
            $idsString = implode(',', $productIds);
            if ($idsString !== '') {
                // Ensure $idsString is properly escaped and safe
                $searchResults = $searchResults->orderByRaw("FIELD(id, $idsString)");
            }
            $searchResults = $searchResults->paginate($perPage);

            // Attach additional metadata
            $searchResults->totalProducts = $totalProducts;
            $searchResults->totalServices = $totalServices;

            $products = $searchResults;
        } else {
            // Default behavior: fetch all products with pagination
            $products = Product::paginate($perPage);

            // Calculate totalProducts and totalServices
            $totalProducts = Product::where('type', 'product')->count();
            $totalServices = Product::where('type', 'service')->count();

            // Attach the counts to the paginator object
            $products->totalProducts = $totalProducts;
            $products->totalServices = $totalServices;
        }

        $countryFilterItems = [];
        $organizationSizeRange = [];
        $organizationTypes = [];

        if ($products) {
            foreach ($products as $product) {
                $oraganisationSize = $product->company->oraganisationSize;
                $range = $oraganisationSize->range_from . '-' . $oraganisationSize->range_to;
                if ($range && !in_array($range, $organizationSizeRange)) {
                    $organizationSizeRange[] = $range;
                }

                if (!in_array($product->company->oraganisationType->name, $organizationTypes)) {
                    $organizationTypes[] = $product->company->oraganisationType->name;
                }

                foreach ($product->company->company_locations ?? [] as $thisLocation) {
                    $code = $thisLocation->geo_country->code ?? null;
                    if ($code && !in_array($code, $countryFilterItems)) {
                        $countryFilterItems[] = $code;
                    }
                }
            }
        }

        // Return the view with search results
        return view('frontend.product.index', compact(
            'products',
            'dropdownItems',
            'getCategoryFilterData',
            'countryFilterItems',
            'organizationSizeRange',
            'organizationTypes',
            'queryParams',
            'location'
        ));
    }


    // Show a single product
    public function show($uuid)
    {
        $getData = $this->ipApi->getSearchLocation();
        $queryParams = $getData['queryParams'];
        $location = $getData['location'];
        $dropdownItems = config('dropdown.items');

        $feedbackSortOptions = [
            'new_to_old' => 'New → Old',
            'old_to_new' => 'Old → New',
            'most_replies' => 'Most Replies'
        ];

        $feedbackFilterOptions = [
            'question' => 'Question',
            'like' => 'Positive',
            'dislike' => 'Negative'
        ];

        // Fetch all categories for filtering
        $categories = Category::all();
        $product = Product::where('uuid', $uuid)->firstOrFail();
        $queryCategories = [   
            'id' => 1,
            'title' => 'Cate 1',

        ];
        return view('frontend.product.show', compact(
            'product',
            'categories',
            'dropdownItems',
            'queryParams',
            'location',
            'queryCategories',
            'feedbackSortOptions',
            'feedbackFilterOptions'
        ));
    }

    /**
     * Generate and insert fake product data.
     */
    public function generateFakeProducts()
    {
        $faker = Faker::create();

        // Fetch all categories to randomly assign them to products
        $categories = Category::pluck('id')->toArray();

        // // Fetch all organisation type, size, and user IDs to randomly assign them to companies
        // $organisationTypes = OrganisationType::pluck('id')->toArray();
        // $organisationSizes = OrganisationSize::pluck('id')->toArray();
        // $userIds = User::pluck('id')->toArray(); // Fetch all user IDs

        // // Fetch all country and location type IDs for company locations
        // $countryIds = Country::pluck('id')->toArray();
        // $locationTypeIds = LocationType::pluck('id')->toArray();

        // foreach (range(1, 10) as $index) { // Generate 10 fake companies
        //     $company = Company::create([
        //         'name'                 => $faker->company, // Generate a fake company name
        //         'email'                => $faker->unique()->companyEmail, // Generate a unique company email
        //         'phone'                => $faker->phoneNumber, // Generate a fake phone number
        //         'address'              => $faker->address, // Generate a fake address
        //         'website'              => $faker->url, // Generate a fake website URL
        //         'organisation_type_id' => $faker->randomElement($organisationTypes), // Assign a random organisation type
        //         'organisation_size_id' => $faker->randomElement($organisationSizes), // Assign a random organisation size
        //         'user_id'              => $faker->randomElement($userIds), // Assign a random user ID
        //     ]);

        //     // Create related company locations
        //     foreach (range(1, rand(1, 3)) as $i) { // Generate 1 to 3 locations per company
        //         $company->company_locations()->create([
        //             'country_id'        => $faker->randomElement($countryIds), // Assign a random country ID
        //             'location_type_id'  => $faker->randomElement($locationTypeIds), // Assign a random location type ID
        //             'city'              => $faker->city, // Generate a fake city name
        //             'address'           => $faker->address, // Generate a fake address
        //         ]);
        //     }
        // }

        // Fetch all company IDs to randomly assign them to products
        $companies = Company::pluck('id')->toArray(); // Replace with your actual Company model

        // Fetch all country data to randomly assign regions
        $countries = Country::pluck('id')->toArray(); // Replace 'region' with the actual column name for regions in your countries table

        foreach (range(1, 10) as $index) { // Generate 10 fake products
            // Create a fake product
            $product = Product::create([
                'uuid'           => Str::uuid(),
                'company_id'     => $faker->randomElement($companies), // Assign a random company ID
                'name'           => $faker->word . ' ' . $faker->randomElement(['Phone', 'Tablet', 'Laptop']),
                'description'    => $faker->sentence,
                'type'           => $faker->randomElement(['product', 'service']),
                'listing_status' => 'Active',
            ]);

            // Assign a random past date to created_at
            $product->created_at = now()->subDays(rand(1, 365)); // Random date within the last year
            $product->save();

            // Create related pricing entries for the product
            foreach (range(1, rand(2, 5)) as $i) { // Generate 2 to 5 pricing entries
                ProductPricing::create([
                    'product_id'    => $product->id,
                    'price'         => $faker->randomFloat(2, 10, 1000), // Random price between 10 and 1000
                    'per_unit'      => $faker->randomElement(['piece', 'box', 'kg', 'litre']),
                    'min_order'     => $faker->numberBetween(1, 50), // Random minimum order quantity
                    'part_number'   => $faker->bothify('PN-###??'), // Random part number
                    'payment_method' => $faker->randomElement(['Credit Card', 'PayPal', 'Bank Transfer']),
                    'notes'         => $faker->sentence,
                ]);
            }


            // Assign random categories to the product
            $assignedCategories = $faker->randomElements($categories, rand(1, 3)); // Assign 1 to 3 random categories
            $product->categories()->attach($assignedCategories);
            $assignedCountries = $faker->randomElements($countries, rand(1, 3)); // Assign 1 to 3 random countries
            $product->regions()->attach($assignedCountries);
            $product->searchable();
        }

        die('Fake products, pricing, and company locations generated successfully');
    }

    public function downloadAllDocuments($productId)
    {
        $product = Product::where('uuid', $productId)->first();
        $documents = $product->getMedia('documents');

        if ($documents->isEmpty()) {
            return Redirect::back()->with('error', 'No documents available for download.');
        }

        // Create a temporary ZIP file
        $zipFileName = 'documents-' . $product->uuid . '.zip';
        $zipFilePath = storage_path('app/public/' . $zipFileName);

        $zip = new ZipArchive;
        if ($zip->open($zipFilePath, ZipArchive::CREATE) === TRUE) {
            foreach ($documents as $document) {
                $zip->addFile($document->getPath(), $document->file_name);
            }
            $zip->close();
        } else {
            return Redirect::back()->with('error', 'Failed to create ZIP file.');
        }

        // Return the ZIP file as a download
        return response()->download($zipFilePath)->deleteFileAfterSend(true);
    }
}
