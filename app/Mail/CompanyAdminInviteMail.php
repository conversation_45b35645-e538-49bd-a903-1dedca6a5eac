<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CompanyAdminInviteMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $email;
    public $company;

    /**
     * Create a new message instance.
     *
     * @param  string  $email
     * @param  \App\Models\Company  $company
     */
    public function __construct(string $email, $company)
    {
        $this->email = $email;
        $this->company = $company;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Access Request')
            ->from($this->company->email, $this->company->name)
            ->to($this->email)
            ->markdown('emails.company_admin_invite')
            ->with([
                'company' => $this->company,
                'email' => $this->email,
            ]);
    }
}
