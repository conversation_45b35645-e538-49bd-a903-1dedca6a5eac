<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\VerifyPhoneOtpRequest;
use App\Models\OTP;
use App\Models\User;
// use App\Services\OtpService;
use App\Services\TwilioOtpService;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Exception;
use App\Services\UniqueIdService;

class VerifyPhoneController extends Controller
{
    public function __construct(protected TwilioOtpService $otp, )
    {
        $this->otp = $otp;
    }

    /**
     * Handle the submission of the phone OTP.
     *
     * @param VerifyPhoneOtpRequest $request
     * @return RedirectResponse
     */
    public function __invoke(VerifyPhoneOtpRequest $request): RedirectResponse
    {
        try{
            $data = $request->validated();
            $code = implode('', $data['otp']);
            if(auth()->check() && !empty(auth()->user()->username)){
                $user = auth()->user();
                $contactHistory = $user->user_contact_history()->orderBy('id', 'desc')->first();
                $otp = OTP::where(['user_id' => $user->id])->first();
                $phone = $otp->phone;
                $verified = $this->otp->verifyOtp($phone, $code);
                if (!$verified) {
                    $contactHistory->status = 'failed';
                    $contactHistory->failure_reason = 'Invalid OTP';
                    $contactHistory->save();
                    return \Redirect::back()->withErrors(['otp' => 'Invalid OTP.'])
                        ->with('modalToShow', $request->currentModalToDisplay());
                }else{
                    $user->update(['phone' => $otp->phone]);
                    $contactHistory->status = 'successful';
                    $contactHistory->processed_at = now();
                    $contactHistory->save();
                    $user->markPhoneAsVerified();
                }
                \Auth::logout();      
                return redirect()->route('frontend.index')
                ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Your mobile number has been successfully updated. If you did not request this change, please secure your account immediately.']);
                
            }else{
                $user = User::find(Session::get('registeredUserId'));
                $country = $user->area->country;
                $phone = $user->phone;
                $verified = $this->otp->verifyOtp($phone, $code);
                if (!$verified) {
                    return \Redirect::back()->withErrors(['otp' => 'Invalid OTP.'])
                        ->with('modalToShow', $request->currentModalToDisplay());
                }else{
                    $data;
                    $user->update([
                        'username' => app(UniqueIdService::class)->generate(
                            $user->first_name,
                            $user->last_name,
                            $country->code,
                        )
                    ]);
                    $user->markPhoneAsVerified();
                }
                if(!auth()->check()){
                    \Auth::login($user);
                }
                Session::forget('registeredUserId');
                         
                return \Redirect::back()
                ->with('modalToShow', config('settings.modal.user_profile_created'));
            }
    

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.phone_verification'));
        }

    }

    /**
     * Resend the OTP.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resend(): RedirectResponse
    {
        try{
            if(auth()->check() && !empty(auth()->user()->username)){
                $otp = OTP::where('user_id', (auth()->user()->id))->first();
                $user = auth()->user();
                $phone = $otp->phone;
            }else{
                $user = User::find(Session::get('registeredUserId'));
                $phone = $user->phone;
            }
            
            $this->otp->sendOtp($phone);
            return \Redirect::back()->with('status', 'OTP resent successfully')
                ->with('modalToShow', config('settings.modal.phone_verification'));

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.phone_verification'));
        }
        
    }
}
