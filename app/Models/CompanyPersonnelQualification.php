<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyPersonnelQualification
 *
 * Represents the qualifications of personnel in a company.
 *
 * @package App\Models
 */
class CompanyPersonnelQualification extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'title',
        'notes',
    ];

    /**
     * Get the company associated with the qualification.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
