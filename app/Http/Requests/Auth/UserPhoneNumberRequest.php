<?php

namespace App\Http\Requests\Auth;

use App\Rules\UniquePhoneWithCode;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use App\Rules\ValidPhoneNumberCode;

class UserPhoneNumberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone_prefix' => ['required', 'string'],
            'number' => [
                'required',
                new ValidPhoneNumberCode(request()->phone_prefix),
                new UniquePhoneWithCode(request()->phone_prefix),]
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function failedValidation(Validator $validator): void
    {
        throw new ValidationException(
            $validator,
            \Redirect::back()
                ->withInput($this->only('phone_prefix', 'number'))
                ->withErrors($validator)
                ->with('modalToShow', $this->currentModalToDisplay())
        );
    }

    /**
     * Get the ID of the modal that should be shown after validation failure.
     *
     * @return string
     */
    public function currentModalToDisplay(): string
    {
        return config('settings.modal.phone_number_prompt');
    }

    /**
     * Get the ID of the modal that should be shown after the request is successful.
     *
     * @return string
     */
    public function nextModalToDisplay(): string
    {
        return config('settings.modal.phone_verification');
    }
}
