<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropForeign(['chat_contact_user_id']);
        });
        // Clean up invalid foreign key data
        \DB::table('companies')->whereNotNull('chat_contact_user_id')->whereNotIn(
            'chat_contact_user_id',
            \DB::table('company_admins')->pluck('id')
        )->update(['chat_contact_user_id' => null]);
        Schema::table('companies', function (Blueprint $table) {
            $table->foreign('chat_contact_user_id')
            ->references('id') // role id
            ->on('company_admins')
            ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            //
        });
    }
};
