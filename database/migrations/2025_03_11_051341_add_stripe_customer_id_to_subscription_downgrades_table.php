<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_downgrades', function (Blueprint $table) {
            $table->string('stripe_customer_id')->nullable()->after('new_subscription_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_downgrades', function (Blueprint $table) {
            $table->dropColumn('stripe_customer_id');
        });
    }
};
