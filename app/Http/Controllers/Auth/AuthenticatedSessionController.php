<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class AuthenticatedSessionController extends Controller
{
    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request)
    {
        $data = $request->validated();

        $user = User::where(function ($query) use ($data) {
            $query->where('email', $data['email'])
                ->orWhere('phone', $data['email']);
        })->where('status', 1)->first();
        if(!empty($user)){
            if ((!$user->isEmailVerified() || !$user->isPhoneVerified()) && (empty($user->google_id) && empty($user->linkedin_id))) {
                return \Redirect::route('frontend.index')
                    ->withErrors(['email' => 'These credentials do not match our records.'])
                    ->with('modalToShow', $request->currentModalToDisplay());
            }
    
            if ($user && Hash::check($data['password'], $user->password)) {
                Auth::login($user);
                Session::forget('registeredUserId');
                $request->session()->regenerate();
                
                $previousUrl = url()->previous();
                if (str_contains($previousUrl, 'create-company-profile')) {
                    return redirect()->route('frontend.index'); // Replace with your route name
                }
                return \Redirect::back();

            }
        }

        return \Redirect::route('frontend.index')
            ->withErrors(['email' => 'These credentials do not match our records.'])
            ->with('modalToShow', $request->currentModalToDisplay());
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return \Redirect::route('frontend.index');
    }
}
