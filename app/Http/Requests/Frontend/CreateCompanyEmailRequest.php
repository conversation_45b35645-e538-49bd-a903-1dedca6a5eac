<?php

namespace App\Http\Requests\Frontend;

use App\Models\Company;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class CreateCompanyEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'string',
                // 'lowercase',
                'email',
                'max:50',
                Rule::unique(Company::class),
                Rule::unique(User::class),
                'regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix'
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.unique' => 'This email is already in use for another company account.',
        ];
    }

     /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function failedValidation(Validator $validator): void
    {
        throw new ValidationException(
            $validator,
            \Redirect::back()
                ->withInput()
                ->withErrors($validator)
                ->with('modalToShow', $this->currentModalToDisplay())
        );
    }

    /**
     * Get the ID of the modal that should be shown after validation failure.
     *
     * @return string
     */
    public function currentModalToDisplay(): string
    {
        return config('settings.modal.company-registration-step3');
    }

    /**
     * Get the ID of the modal that should be shown after the request is successful.
     *
     * @return string
     */
    public function nextModalToDisplay(): string
    {
        return config('settings.modal.email_verification');
    }
}
