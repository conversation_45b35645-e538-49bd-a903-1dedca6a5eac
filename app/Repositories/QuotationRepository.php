<?php

namespace App\Repositories;

use App\Models\QuotationRequest;

/**
 * Class QuotationRepository
 *
 * Handles data operations for the QuotationRequest model.
 */
class QuotationRepository extends BaseRepository
{
    /**
     * The QuotationRequest model instance.
     *
     * @var \App\Models\QuotationRequest
     */
    public $model;

    /**
     * QuotationRepository constructor.
     *
     * @param \App\Models\QuotationRequest $quotation
     */
    public function __construct(QuotationRequest $quotation)
    {
        $this->model = $quotation;
    }

    /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {
       return [
            'Pending' => $this->model::STATUS_PENDING,
            'Approved' => $this->model::STATUS_APPROVED,
            'Rejected' => $this->model::STATUS_REJECTED,
       ];
    }

    /**
     * Retrieve all records from the model by role
     *
     * @param string $role
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByRole(string $role)
    {
        return $this->model->role($role)->get();
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByMixedCondition(array $params)
    {
        $query = $this->model->select();
        
        /** filter by status */
        if (isset($params['status'])) {
            $query->status($params['status']);
        }

        /** filter by role */
        // if (isset($params['role'])) {
        //     $query->role($params['role']);
        // }

        return $query->get();
    }
}
