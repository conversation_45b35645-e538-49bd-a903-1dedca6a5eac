<?php

namespace App\Http\Controllers\Frontend\Supplier;

use App\Models\User;
use App\Models\Company;
use App\Models\Category;
use App\Models\OrganisationTypeLocal;
use Illuminate\View\View;
use App\Services\IPService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use App\Http\Controllers\Controller;
use App\Models\CompanyEndorsement;
use App\Models\SavedCompany;
use Illuminate\Support\Facades\Http;
use App\Services\CalculateRegionService;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Route;
use Stripe\Climate\Supplier;

class SupplierController extends Controller
{

    protected $ipApi;
    protected $regionService;

    /**
     * Create a new instance of the controller.
     *
     * @param IpService $ipService
     */
    public function __construct(IPService $ipApi, CalculateRegionService $regionService)
    {
        $this->regionService = $regionService;
        // $this->middleware('auth')->except(['supplierListing']);
        $this->regionService = $regionService;
        $this->ipApi = $ipApi;
        // $this->ipApi = $ipApi;
    }
    /**
     * Display a listing of suppliers.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */

    public function supplierListing(Request $request)
    {
        $getData = $this->ipApi->getSearchLocation();
        $queryParams = $getData['queryParams'];
        $location = $getData['location'];
        $dropdownItems = supplierFilterSortList();
        $categories = Category::all();

        $query = $request->input('query', '');
        $sort = $request->input('sort', '');
        $filter = $request->input('filter', '');
        $perPage = config('services.per_page.default'); // Default items per page

        if (empty($queryParams)) {
            $suppliers = Company::with([
                'company_locations.geo_country',
                'products'
            ])
            ->withCount([
                'products as services_count' => function ($query) {
                    $query->where('type', 'service');
                },
                'products as products_count' => function ($query) {
                    $query->where('type', 'product');
                },
            ])
            ->paginate($perPage);

        } else {

            $locationType = $request->location;
            switch($locationType){
                case 'all':
                    unset($queryParams['region_id']);
                    unset($queryParams['country_id']);
                    $searchResults = Company::search($queryParams)->get();
                    $queryParams = $getData['queryParams'];
                    break;
                case 'local':
                    $searchResults = Company::search($queryParams)->get();
                    break;
                case 'national':
                    unset($queryParams['region_id']);
                    $searchResults = Company::search($queryParams)->get();
                    $queryParams = $getData['queryParams'];
                    break;
                default:
                    $searchResults = Company::search($queryParams)->get();
                    break;    
            }
            
            $searchResults->load(['company_locations.geo_country', 'products']);
            $searchResults->transform(function ($company) {
                $company->services_count = $company->products ? $company->products->where('type', 'service')->count() : 0;
                $company->products_count = $company->products ? $company->products->where('type', 'product')->count() : 0;
                return $company;
            });
            $currentPage = LengthAwarePaginator::resolveCurrentPage();
            $pagedResults = $searchResults->forPage($currentPage, $perPage)->values();
            $suppliers = new LengthAwarePaginator(
                $pagedResults,
                $searchResults->count(),
                $perPage,
                $currentPage,
                ['path' => request()->url(), 'query' => request()->query()]
            );
            
            //To get the count of suppliers of particular region
            $location['localSuppliersCount'] = 0;
            if(isset($queryParams['region_id'])){
                $localSuppliers = Company::search($queryParams)->raw();
                $location['localSuppliersCount'] = $localSuppliers['total'];
            }

            //To get the count of suppliers of nearest regions
            $location['nationalSuppliersCount'] = 0;
            if(isset($queryParams['country_id'])){
                unset($queryParams['region_id']);
                $nationalSuppliers = Company::search($queryParams)->raw();
                $location['nationalSuppliersCount'] = $nationalSuppliers['total'];
                unset($queryParams['country_id']);
            }

            //To get the count of suppliers
            $getAllSuppliers = Company::search($queryParams)->raw();
            $location['allSuppliersCount'] = $getAllSuppliers['total'];

            
        }

        $countryFilterItems = [];
        $organizationSizeRange = [];
        $organizationTypes = [];

        if ($suppliers) {
            foreach ($suppliers as $supplier) {
                $oraganisationSize = $supplier->oraganisationSize;
                $range = $oraganisationSize->range_from . '-' . $oraganisationSize->range_to;
                if ($range && !in_array($range, $organizationSizeRange)) {
                    $organizationSizeRange[] = $range;
                }

                if (!in_array($supplier->oraganisationType->name, $organizationTypes)) {
                    $organizationTypes[] = $supplier->oraganisationType->name;
                }

                $organisationTypeLocal = OrganisationTypeLocal::where('organisation_type_id', $supplier->oraganisationType->id)
                    ->where('iso_code', $supplier->mainLocation->geo_country->code)
                    ->first();

                $organizationTypeLocalCode[$organisationTypeLocal->local_code] = $supplier->oraganisationType->name;

                foreach ($supplier->company_locations ?? [] as $thisLocation) {
                    $code = $thisLocation->geo_country->code ?? null;
                    if ($code && !in_array($code, $countryFilterItems)) {
                        $countryFilterItems[] = $code;
                    }
                }
            }
        }

        return view('frontend.supplier.suppliers', compact(
            'suppliers',
            'location',
            'categories',
            'dropdownItems',
            'countryFilterItems',
            'organizationSizeRange',
            'organizationTypes',
            'queryParams',
        ));

    }

    public function saveSupplier(Request $request)
    {
        try{
            $user = auth()->user();
            $companyId = $request->supplier_id;
            $company = Company::find($companyId);

            if ($user && $company) {
                $savedCompany = SavedCompany::create([
                    'user_id' => $user->id,
                    'company_id' => $company->id,
                ]);
                $savedCompany->searchable();
            }
            return redirect()->back()
            ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Supplier added to Saved Suppliers successfully.']);

        }catch (Exception $e) {
            return redirect()->back()
            ->with(['catchError' => $e->getMessage()]);
        }
    }

    public function unsaveSupplier(Request $request)
    {
        try{
            $user = auth()->user();
            $companyId = $request->supplier_id;
            $company = Company::find($companyId);

            if ($user && $company) {
                $savedCompany = SavedCompany::where([
                    'user_id' => $user->id,
                    'company_id' => $company->id,
                ])->first();
                $savedCompany->delete();
            }
            return redirect()->back()
            ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Supplier removed from Saved Suppliers successfully.']);

        }catch (Exception $e) {
            return redirect()->back()
            ->with(['catchError' => $e->getMessage()]);
        }
    }

    public function savedSuppliers(Request $request){
        $getData = $this->ipApi->getSearchLocation();
        $queryParams['query'] = $request->input('saved_query', '');
        $location = $getData['location'];
        $dropdownItems = [
            'feedback' => 'Feedback',
            'newly_added' => 'Newly Added',
            'price_low_high' => 'Price Low - High',
            'price_high_low' => 'Price High - Low',
        ];


        // Get the current query parameters and remove existing 'sort' if present
        // Fetch all categories for filtering
        $categories = Category::all();

        // Retrieve search query and location from the request
        $query = $request->input('saved_query', '');

        $sort = $request->input('sort', '');
        $filter = $request->input('filter', '');
        $perPage = config('services.per_page.default'); // Default items per page
        
        // Perform search with location filter
        $rawResults = SavedCompany::search($queryParams)->raw();
        // Extract all product IDs from rawResults
        $companyIds = collect($rawResults['results'])->pluck('_source.company_id')->toArray();
        $searchResults = Company::whereIn('id', $companyIds)
        ->withCount([
            'products as services_count',
            'services as products_count',
        ])->paginate($perPage);
        $suppliers = $searchResults;

        return view('frontend.supplier.suppliers', compact(
            'location',
            'suppliers',
            'categories',
            'dropdownItems',
            'queryParams',
        ));

    }

    public function viewSupplier($slug=null){
         if(!$slug){
            return redirect()->route('frontend.supplier.listing');
         }
         $getData = $this->ipApi->getSearchLocation();
         $queryParams = $getData['queryParams'];
         $location = $getData['location'];
         $dropdownItems = supplierFilterSortList();
         $categories = Category::all();

         $supplier = Company::with(['services', 'products', 'oraganisationSize', 'oraganisationType',
          'company_locations','company_business_categories', 'company_iso_compliance', 'company_personnel_qualifications',
          'company_product_certifications', 'company_awards', 'company_case_studies', 'activeCompanyEndorsements'])
          ->withCount([
            'products as services_count' => function ($query) {
                $query->where('type', 'service');
            },
            'products as products_count' => function ($query) {
                $query->where('type', 'product');
            },
        ])
        ->where('slug', $slug)->first();
        
         return view('frontend.supplier.supplier-view', compact(
            'supplier',
            'location',
            'categories',
            'dropdownItems',
            'queryParams',
        ));



    }
    public function supplyExtentHtml(Company $company)
    {
        return view('frontend.company.supply_extent_body', ['company' => $company]);
    }

    public function endorseSupplier(Request $request){
        try{
            $user = auth()->user();
            $endorsedCompany = $request->endorsed_supplier;
            if ($user && $endorsedCompany) {
                CompanyEndorsement::updateOrCreate([
                    'endorser_company' => $user->company->id,
                    'endorsed_company' => $endorsedCompany
                ],[
                    'endorser_user' => $user->id,
                    'remover_user' => null,
                    'ip_address' => request()->ip(),
                    'status' => 'active',
                    'created_at' => now(),
                    'removed_at' => null,
                ]);
            }
            return redirect()->back()
                    ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Supplier endorsed successfully.']);

        }catch (Exception $e) {
            return redirect()->back()
                    ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'title' => 'Error', 'message' => 'Unable to endorse supplier.']);
        }

    }

    public function removeEndorsedSupplier(Request $request){
        try{
            $user = auth()->user();
            $endorsedCompany = $request->endorsed_supplier;
            if ($user && $endorsedCompany) {
                CompanyEndorsement::updateOrCreate([
                    'endorser_company' => $user->company->id,
                    'endorsed_company' => $endorsedCompany
                ],[
                    'remover_user' => $user->id,
                    'status' => 'removed',
                    'removed_at' => now(),
                ]);
            }
            return redirect()->back()
                    ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Removed supplier from endorsement successfully.']);

        }catch (Exception $e) {
            return redirect()->back()
                    ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'title' => 'Error', 'message' => 'Unable to remove supplier from endorsement.']);
        }

    }

}
