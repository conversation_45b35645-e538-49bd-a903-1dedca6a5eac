<?php

namespace App\ScoutEngines;

use Lara<PERSON>\Scout\Engines\Engine;
use OpenSearch\Client;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use App\Services\IPService;
use Illuminate\Support\Facades\Log;

class OpenSearchEngine extends Engine
{
    protected $client;
    protected $index;
    public function __construct(Client $client, $index = null)
    {
        $this->client = $client;
        //$this->index = $index;
    }

    public function paginate($builder, $perPage, $page)
    {
        // Implement pagination logic here
        return $this->search($builder, $perPage, ($page - 1) * $perPage);
    }

    public function lazyMap($builder, $results, $model)
    {
        // Implement lazy mapping logic here
        return $this->map($builder, $results, $model)->lazy();
    }

    public function flush($model)
    {
        // Implement flush logic here
        $index = $model->searchableAs();
        $this->client->deleteByQuery([
            //'index' => $this->index,
            'index' => $index,
            'body' => [
                'query' => [
                    'match_all' => new \stdClass(),
                ],
            ],
        ]);
    }

    public function createIndex($name, array $options = [])
    {
        // Implement create index logic here
        $this->client->indices()->create([
            'index' => $name,
            'body' => $options,
        ]);
    }

    public function deleteIndex($name)
    {
        // Implement delete index logic here
        $this->client->indices()->delete([
            'index' => $name,
        ]);
    }


    public function update($models)
    {
        foreach ($models as $model) {
            $index = $model->searchableAs();

            // Create index if it doesn't exist
            if (!$this->client->indices()->exists(['index' => $index])) {
                $mapping = method_exists($model, 'searchableMapping')
                    ? $model->searchableMapping()
                    : [];

                $this->client->indices()->create([
                    'index' => $index,
                    'body' => [
                        'mappings' => $mapping,
                    ],
                ]);
            }

            // Index document
            $this->client->index([
                'index' => $index,
                'id' => $model->getScoutKey(),
                'body' => $model->toSearchableArray(),
            ]);
        }
    }

    public function delete($models)
    {
        foreach ($models as $model) {
            $index = $model->searchableAs();
            $this->client->delete([
                //'index' => $this->index,
                'index' => $index,
                'id' => $model->getScoutKey(),
            ]);
        }
    }

    public function search($builder, $size = null, $from = null)
    {
        // Get the index name from the model
        $index = $builder->model->searchableAs();
       
        // Update to pass the index to buildSortOptions
        $sort = $this->buildSortOptions($index);

        // Get location and filter from the request
        $location = request('location');
        $filter = request('filter');
        $productType = isset($builder->query['type']) ? $builder->query['type'] : '';
        $productStatus = isset($builder->query['product_status']) ? $builder->query['product_status'] : '';
        $productCompany= isset($builder->query['company_id']) ? $builder->query['company_id'] : '';
        $searchQuery = isset($builder->query['query']) ? $builder->query['query'] : '';

        // Build the initial query
        $query = [
            'bool' => [
                'should' => [
                    [
                        'multi_match' => [
                            'query' => trim($searchQuery),
                            'fields' => $builder->model::$searchableFields,
                            'fuzziness' => 'AUTO' // Enables typo tolerance
                        ],
                    ],
                    [
                        'wildcard' => [
                            'name.keyword' => [
                                'value' => "*{$searchQuery}*", // Allows partial matches
                                'boost' => 2.0 // Gives wildcard search higher priority
                            ]
                        ]
                    ],
                    [
                        'wildcard' => [
                            'description.keyword' => [
                                'value' => "*{$searchQuery}*",
                                'boost' => 1.5
                            ]
                        ]
                    ]
                ],
                'minimum_should_match' => 1
            ]
        ];
        // for both products_index and suppliers_index
        if (!empty($builder->query['region_id'])) {
            $regionArray = is_array($builder->query['region_id']) 
                ? $builder->query['region_id'] 
                : array_map('trim', explode(',', $builder->query['region_id']));

            if (!empty($regionArray)) {
                $regionShouldClauses = [];

                foreach ($regionArray as $regionId) {
                    $regionShouldClauses[] = [
                        'term' => [
                            'region_ids' => $regionId
                        ]
                    ];
                }

                $query['bool']['filter'][] = [
                    'bool' => [
                        'should' => $regionShouldClauses,
                        'minimum_should_match' => 1
                    ]
                ];
            }
        }

        if (!empty($builder->query['country_id'])) {
            $countryArray = is_array($builder->query['country_id']) 
                ? $builder->query['country_id'] 
                : array_map('trim', explode(',', $builder->query['country_id']));

            if (!empty($countryArray)) {
                $countryShouldClauses = [];

                foreach ($countryArray as $countryId) {
                    $countryShouldClauses[] = [
                        'term' => [
                            'country_ids' => $countryId
                        ]
                    ];
                }

                $query['bool']['filter'][] = [
                    'bool' => [
                        'should' => $countryShouldClauses,
                        'minimum_should_match' => 1
                    ]
                ];
            }
        }

        // Add product type filter if provided
        if (!empty($productType)) {
            $query['bool']['filter'][] = [
                'term' => [
                    'type.keyword' => $productType // Ensure 'type' is indexed as a keyword
                ]
            ];
        }
        // Add product company filter if provided
        if (!empty($productCompany)) {
            $query['bool']['filter'][] = [
                'term' => [
                    'company_id' => $productCompany // Ensure 'company_id' is indexed as a keyword
                ]
            ];
        }
        // Add product status filter if provided
        if (!empty($productStatus)) {
            $query['bool']['filter'][] = [
                'term' => [
                    'product_status.keyword' => $productStatus // Ensure 'product_status' is indexed as a keyword
                ]
            ];
        }

        // Add location filter if provided
        // if (!empty($location)) {
        //     // Explode the location by comma and get the last index
        //     $locationParts = explode(',', $location);
        //     $trimmedLocation = rtrim(end($locationParts)); // Get the last part and trim it
        //     if($index == 'products_index') {
        //         $query['bool']['filter'][] = [
        //             'match' => [
        //                 'location' => $trimmedLocation, // Ensure 'location' is indexed as a keyword
        //             ]
        //         ];
        //     }
        // }

        // Add additional filter if provided
        
        if ($filter) {
            $filterArray = array_map('trim', explode(',', $filter));
            if (!empty($filterArray)) {
                $shouldClauses = [];

                foreach ($filterArray as $filterItem) {
                    $shouldClauses[] = [
                        'term' => [
                            'filters.keyword' => $filterItem
                        ]
                    ];
                }

                $query['bool']['filter'][] = [
                    'bool' => [
                        'should' => $shouldClauses,
                        'minimum_should_match' => 1
                    ]
                ];
            }
        }

        $aggs = [
            'type_counts' => [
                'terms' => [
                    'field' => 'type.keyword', // Ensure type is indexed as a keyword
                    'size' => 10
                ]
            ]
        ];

        $body = [
            'query' => $query,
            'aggs' => $aggs,
            'sort' => $sort,
        ];

        if ($size !== null && $from !== null) {
            $body['size'] = $size;
            $body['from'] = $from;
        }

        // If sorting by location, add a geo-distance filter
        if (request('sort') === 'location_nearest') {
            $latitude = request('latitude');
            $longitude = request('longitude');
            
            // Get location if not in request
            if (!$latitude || !$longitude) {
                if (Auth::check() && Auth::user()->area) {
                    $latitude = Auth::user()->area->latitude;
                    $longitude = Auth::user()->area->longitude;
                } else {
                    $ipService = app(IPService::class);
                    $ipData = $ipService->getIpLocation();
                    if (isset($ipData['lat']) && isset($ipData['lon'])) {
                        $latitude = $ipData['lat'];
                        $longitude = $ipData['lon'];
                    }
                }
            }
            
            if ($latitude && $longitude) {
                // Add a function score query to boost products by proximity to company locations
                $originalQuery = $query;
                $query = [
                    'function_score' => [
                        'query' => $originalQuery,
                        'functions' => [
                            [
                                'script_score' => [
                                    'script' => [
                                        'source' => "double minDist = Double.MAX_VALUE; if (doc.containsKey('company_locations') && !doc['company_locations'].empty) { for (def location : doc['company_locations']) { if (location.containsKey('latitude') && location.containsKey('longitude')) { double lat1 = location.latitude; double lon1 = location.longitude; double lat2 = params.lat; double lon2 = params.lon; double x = Math.toRadians(lon2 - lon1) * Math.cos(Math.toRadians((lat1 + lat2) / 2)); double y = Math.toRadians(lat2 - lat1); double dist = Math.sqrt(x * x + y * y) * 6371; minDist = Math.min(minDist, dist); } } } return minDist != Double.MAX_VALUE ? 1 / (minDist + 1) : 0;",
                                        'params' => [
                                            'lat' => (float)$latitude,
                                            'lon' => (float)$longitude
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'boost_mode' => 'multiply'
                    ]
                ];
                
                // Update the body with the new query
                $body['query'] = $query;
            }
        }

        $params = [
            'index' => $index,
            'body' => $body,
        ];

        // Perform the search
        $response = $this->client->search($params);
        // Check if there are no results and fallback to a search without location
        // if (($response['hits']['total']['value'] ?? 0) === 0 && $location) {
        //     foreach ($query['bool']['filter'] as $key => $filter) {
        //         if (isset($filter['match']['location'])) {
        //             unset($query['bool']['filter'][$key]);
        //             break;
        //         }
        //     }
        //     $query['bool']['filter'] = array_values($query['bool']['filter']);
        //     $body['query'] = $query;
        //     $params['body'] = $body;

        //     // Perform the fallback search
        //     $response = $this->client->search($params);
        // }

        // Extract type counts from aggregations
        $typeCounts = [];
        if (isset($response['aggregations']['type_counts']['buckets'])) {
            foreach ($response['aggregations']['type_counts']['buckets'] as $bucket) {
                $typeCounts[$bucket['key']] = $bucket['doc_count'];
            }
        }

        return [
            'total' => $response['hits']['total']['value'] ?? 0,
            'typeCounts' => $typeCounts,
            'results' => $response['hits']['hits'] ?? [],
        ];
    }

    public function mapIds($results)
    {
        return collect($results['results'])->pluck('_id');
    }

    public function map($builder, $results, $model)
    {
        if ($results['total'] === 0) {
            return Collection::make();
        }

        $ids = collect($results['results'] ?? [])->pluck('_id')->all();
        return $model->whereIn($model->getKeyName(), $ids)->orderByRaw("FIELD(id, " . implode(',', $ids) . ")")->get();
    }

    public function getTotalCount($results)
    {
        return $results['total'];
    }

    protected function getTextFields($index)
    {
        // Example implementation: Return an array of text fields
        return ['name', 'description'];
    }

    protected function buildSortOptions($index = null)
    {
        $sort = match (request('sort')) {
            'id' => [['id' => ['order' => 'desc']]],
            'price_low_high' => [['price' => ['order' => 'asc']]],
            'price_high_low' => [['price' => ['order' => 'desc']]],
            'best_match' => [['_score' => ['order' => 'desc']]],
            'newly_added' => [['created_at' => ['order' => 'desc']]],
            'feedback' => [['feedback' => ['order' => 'desc']]],
            'location_nearest' => $this->buildGeoDistanceSort($index),
            'founded_new' => [['year_founded' => ['order' => 'desc']]],
            'founded_old' => [['year_founded' => ['order' => 'asc']]],
            // 'portfolio_size' => [['portfolio_size' => ['order' => 'asc']]],
            default => [['_score' => ['order' => 'desc']]],
        };
        
        return $sort;
    }
    
    /**
     * Build geo distance sort options based on user's location
     * 
     * @param string|null $index The index name to check for fields
     * @return array
     */
    protected function buildGeoDistanceSort($index = null)
    {
        // Get user's current location from request
        $latitude = request('latitude');
        $longitude = request('longitude');
        
        // If location not provided in request, try to get from session or user preferences
        if (!$latitude || !$longitude) {
            if (Auth::check() && Auth::user()->area) {
                $latitude = Auth::user()->area->latitude;
                $longitude = Auth::user()->area->longitude;
            } else {
                // Default to a fallback location if no user location available
                $ipService = app(IPService::class);
                $ipData = $ipService->getIpLocation();
                if (isset($ipData['lat']) && isset($ipData['lon'])) {
                    $latitude = $ipData['lat'];
                    $longitude = $ipData['lon'];
                }
            }
        }
        
        // If we have coordinates, build the geo_distance sort
        if ($latitude && $longitude && $index) {
            // Only apply geo_distance sort to specific indexes
            $geoSortableIndexes = ['products_index', 'suppliers_index'];
            
            if (in_array($index, $geoSortableIndexes) && $this->indexHasField($index, 'company_locations')) {
                // Use geo_distance sort for company locations
                return [
                    [
                        '_geo_distance' => [
                            'company_locations' => [
                                'lat' => (float)$latitude,
                                'lon' => (float)$longitude
                            ],
                            'order' => 'asc',
                            'unit' => 'km',
                            'distance_type' => 'arc',
                            'mode' => 'min' // Use the closest location if multiple exist
                        ]
                    ]
                ];
            }
        }
        
        // Fallback to default sort if no location available or index not supported
        return [['_score' => ['order' => 'desc']]];
    }

    protected function indexHasField($index, $field)
    {
        try {
            // Fetch the mapping for the given index
            $mapping = $this->client->indices()->getMapping(['index' => $index]);
            
            // Check if the field exists in the mapping
            $properties = data_get($mapping, "$index.mappings.properties", []);
            
            // For nested fields, check each part
            $fieldParts = explode('.', $field);
            $currentField = $properties;
            
            foreach ($fieldParts as $part) {
                if (!isset($currentField[$part])) {
                    return false;
                }
                
                if (isset($currentField[$part]['properties'])) {
                    $currentField = $currentField[$part]['properties'];
                }
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Error checking if field exists in index: ' . $e->getMessage());
            return false;
        }
    }
}
