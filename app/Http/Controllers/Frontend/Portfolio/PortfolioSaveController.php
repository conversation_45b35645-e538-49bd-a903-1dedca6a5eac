<?php

namespace App\Http\Controllers\Frontend\Portfolio;

use Carbon\Carbon;
use App\Models\Unit;
use App\Models\Country;
use App\Models\Product;
use App\Models\Category;
use App\Models\SaveProduct;
use App\Models\KeyFeature;
use Faker\Factory as Faker;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\ProductPricing;
use App\Models\ProductShipping;
use App\Models\AfterSaleSupport;
use App\Models\ProductAttribute;
use Illuminate\Support\Facades\DB;
use App\Search\ElasticsearchEngine;
use App\Http\Controllers\Controller;
use App\Models\ProductCertification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\ProductStandardsCompliance;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Requests\Frontend\ProductPortfolio\CreateProductPortfolioRequest;
use App\Http\Requests\Frontend\ProductPortfolio\UpdateProductPortfolioRequest;

class PortfolioSaveController extends Controller
{
    /**
     * Display a listing of products and services with search and filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $queryParams['query'] = request('search');

        $sort = $request->input('sort', '');
        $filter = $request->input('filter', '');
        $quickFilter = request('quick_filter');
        $perPage = config('services.per_page.default'); // Default items per page

        // Perform search with location filter
        $rawResults = SaveProduct::search($queryParams)->raw();
        // Initialize typeCounts array
        $typeCounts = $rawResults['typeCounts'];
        
        // Extract total counts for each type
        $totalProducts = $typeCounts['product'] ?? 0;
        $totalServices = $typeCounts['service'] ?? 0;
        // Extract all product IDs from rawResults
        $productIds = collect($rawResults['results'])->pluck('_source.product_id')->toArray();        
        // Convert raw results into Laravel Collection for pagination
        if($quickFilter) {
            // Fetch products from the Product model using the extracted IDs
            $searchResults = Product::whereIn('id', $productIds)->where('type', $quickFilter);
        } else {
            // Fetch products from the Product model using the extracted IDs
            $searchResults = Product::whereIn('id', $productIds);
        }
        $idsString = implode(',', $productIds);
        if ($idsString !== '') {
            // Ensure $idsString is properly escaped and safe
            $searchResults = $searchResults->orderByRaw("FIELD(id, $idsString)");
        }
        $searchResults = $searchResults->paginate($perPage);

        // Attach additional metadata
        $searchResults->totalProducts = $totalProducts;
        $searchResults->totalServices = $totalServices;
        $searchResults->totalSave = $totalProducts + $totalServices;

        $products = $searchResults;
        $totalCount = $activeCount = $draftCount =0;

        return view('frontend.portfolio.productsave.index', compact('products', 'totalCount', 'activeCount', 'draftCount'));
    }



    // Show a single product
    public function show($uuid)
    {
        $product = Product::where('uuid', $uuid)->with(['categories', 'keyFeatures', 'afterSaleSupports', 'regions', 'pricings', 'shippings', 'attributes', 'certifications', 'standardsCompliances'])->firstOrFail();
        // dd($product->categories);
        $user = Auth::user();
        $company = $user->company;

        $categories = Category::where('type', 1)
            ->where('status', 1)
            ->get();

        $features = KeyFeature::all();
        $afterSaleSupport = AfterSaleSupport::all();
        $regions = Country::all();
        $unit = Unit::all();
        return view('frontend.portfolio.product.show', compact('product', 'categories', 'features', 'afterSaleSupport', 'regions', 'company', 'unit'));
    }

    public function create()
    {
        $user = Auth::user();
        $company = $user->company;

        $categories = Category::where('type', 1)
            ->where('status', 1)
            ->get();
        $groupedCategories = $categories->groupBy('level');
        $features = KeyFeature::all();
        $afterSaleSupport = AfterSaleSupport::all();
        $unit = Unit::all();
        $regions = Country::all();

        return view('frontend.portfolio.product.create', compact('categories', 'groupedCategories', 'features', 'afterSaleSupport', 'regions', 'company', 'unit'));
    }


    public function store(CreateProductPortfolioRequest $request)
    {
        $featureIds = $regionIds = [];

        DB::beginTransaction();

        try {
            $existingProduct = Product::where('name', $request->name)
                ->where('company_id', $request->company_id)
                ->first();
            if ($existingProduct) {
                $product = $existingProduct;
            } else {
                $product = new Product();
            }

            $product->name = $request->name ?? null;
            $product->description = $request->product_description ?? null;
            $product->region_restrictions = $request->region_restrictions ?? null;
            $product->company_id = $request->company_id ?? null;
            $product->additional_pricing_detail = $request->additional_pricing_detail ?? null;
            $product->additional_shiping_rate_detail = $request->additional_shiping_rate_detail ?? null;
            $product->compliance_summary = $request->compliance_summary ?? null;
            $product->listing_duration = $request->listing_duration ?? null;
            if ($request->status == "save") {
                $product->listing_status = "Inactive";
            } else {
                $product->listing_status = "Active";
            }
            $product->remove_listing = $request->remove_listing ?? 0;
            if ($request->listing_duration === "Publish") {
                $product->listing_release_date = Carbon::now()->format('Y-m-d'); // Set to today's date
            } else {
                $product->listing_release_date = $request->listing_release_date
                    ? Carbon::createFromFormat('m/d/Y', $request->listing_release_date)->format('Y-m-d')
                    : null;
            }
            $product->listing_end_date = $request->listing_end_date ? Carbon::createFromFormat('m/d/Y', $request->listing_end_date)->format('Y-m-d') : null;
            $product->type = "product";
            $product->save();

            foreach ($request->key_feature as $featureName) {
                $feature = KeyFeature::firstOrCreate(['feature' => $featureName]); // Create if not exists
                $featureIds[] = $feature->id; // Collect feature IDs
            }
            $regionIds[] = $request->regions;

            $product->categories()->sync((array) $request->product_category);
            $product->afterSaleSupports()->sync($request->after_sales_support);
            $product->keyFeatures()->sync($featureIds);
            $product->regions()->sync($regionIds);

            // Store document files with titles
            if ($request->hasFile('upload_document')) {
                foreach ($request->file('upload_document') as $index => $document) {
                    $media = $product->addMedia($document)
                        ->toMediaCollection('documents');

                    // Add custom property for document title if provided
                    if (!empty($request->document_title[$index])) {
                        $media->setCustomProperty('document_title', $request->document_title[$index]);
                        $media->save();
                    }
                }
            }

            // Get the array of part numbers from the request
            $submittedPartNumbers = collect($request->product_prices)->pluck('part_number')->toArray();

            // Delete product prices that are not in the request
            ProductPricing::where('product_id', $product->id)
                ->whereNotIn('part_number', $submittedPartNumbers)
                ->delete();

            // Insert or update product pricing records
            foreach ($request->product_prices as $price) {

                ProductPricing::updateOrCreate(
                    [
                        'product_id' => $product->id, // Search criteria
                        'part_number' => $price['part_number']
                    ],
                    [
                        'price' => $price['price'] ?? null,
                        'per_unit' => $price['unit'] ?? null,
                        'min_order' => $price['min_order'] ?? null,
                        'payment_method' => $price['payment_terms'] ?? null,
                        'notes' => $price['notes'] ?? null
                    ]
                );
            }
            // Insert or update product pricing records
            foreach ($request->shipping_details as $shipping) {
                ProductShipping::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'destination' => $shipping['destination']
                    ],
                    [
                        'shipping_method' => $shipping['shipping_method'] ?? null,
                        'estimated_delivery_time' => $shipping['shipping_time'] ?? null,
                        'shipping_rate' => $shipping['shipping_rate'] ?? null,
                        'notes' => $shipping['notes'] ?? null
                    ]
                );
            }


            // Get submitted attribute names
            $submittedAttributes = collect($request->product_attributes)->pluck('name')->toArray();

            // Delete attributes that are not in the request
            ProductAttribute::where('product_id', $product->id)
                ->whereNotIn('attribute', $submittedAttributes)
                ->delete();

            // Insert or update attributes
            foreach ($request->product_attributes as $attribute) {
                ProductAttribute::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'attribute' => $attribute['name']
                    ],
                    [
                        'characteristic' => $attribute['characteristic'] ?? null
                    ]
                );
            }


            // Get the array of certification titles from the request
            $submittedCertificates = collect($request->product_certificates)->pluck('title')->toArray();

            // Delete certifications that are not in the request
            ProductCertification::where('product_id', $product->id)
                ->whereNotIn('certificate_title', $submittedCertificates)
                ->delete();

            // Insert or update certifications
            foreach ($request->product_certificates as $certificate) {
                ProductCertification::updateOrCreate(
                    [
                        'product_id' => $product->id, // Search criteria
                        'certificate_title' => $certificate['title']
                    ],
                    [
                        'certification_body' => $certificate['body'] ?? null,
                        'certification_number' => $certificate['number'] ?? null,
                        'notes' => $certificate['notes'] ?? null
                    ]
                );
            }


            // Get the array of standard names from the request
            $submittedStandards = collect($request->product_compliance)->pluck('standard')->toArray();

            // Delete standards that are not in the request
            ProductStandardsCompliance::where('product_id', $product->id)
                ->whereNotIn('standard_name', $submittedStandards)
                ->delete();

            // Insert or update compliance records
            foreach ($request->product_compliance as $compliance) {
                ProductStandardsCompliance::updateOrCreate(
                    [
                        'product_id' => $product->id, // Search criteria
                        'standard_name' => $compliance['standard']
                    ],
                    [
                        'notes' => $compliance['details'] ?? null // Update notes if standard exists
                    ]
                );
            }
            DB::commit(); // ✅ Commit the transaction if everything is successful
        } catch (\Exception $e) {
            DB::rollBack(); // ❌ Rollback on error
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }

        // Store all image after all other data has been saved
        // Store main image
        if ($request->hasFile('main_image')) {
            $product->addMedia($request->file('main_image'))
                ->toMediaCollection('main_image');
        }

        // Store thumbnail images
        if ($request->hasFile('thumb_images')) {
            foreach ($request->file('thumb_images') as $thumb) {
                $product->addMedia($thumb)
                    ->toMediaCollection('thumb_images');
            }
        }
        if ($request->status == "save") {
            $status = "drafted";
        } else {
            $status = "published";
        }

        return response()->json([
            'success' => true,
            'message' => "Product " . $status . " successfully.",
        ], 201);
    }


    public function edit($uuid)
    {
        $product = Product::where('uuid', $uuid)->with(['categories', 'keyFeatures', 'afterSaleSupports', 'regions', 'pricings', 'shippings', 'attributes', 'certifications', 'standardsCompliances'])->firstOrFail();

        $user = Auth::user();
        $company = $user->company;

        $categories = Category::where('type', 1)
            ->where('status', 1)
            ->get();
        $groupedCategories = $categories->groupBy('level');



        $features = KeyFeature::all();
        $afterSaleSupport = AfterSaleSupport::all();
        $regions = Country::all();
        $unit = Unit::all();
        return view('frontend.portfolio.product.edit', compact('product', 'categories', 'groupedCategories', 'features', 'afterSaleSupport', 'regions', 'company', 'unit'));
    }

    public function update(UpdateProductPortfolioRequest $request, $uuid)
    {
        $featureIds = $regionIds = [];
        DB::beginTransaction();
        $product = Product::where('uuid', $uuid)->firstOrFail();
        try {
            $product->name = $request->name ?? null;
            $product->description = $request->product_description ?? null;
            $product->region_restrictions = $request->region_restrictions ?? null;
            $product->company_id = $request->company_id ?? null;
            $product->additional_pricing_detail = $request->additional_pricing_detail ?? null;
            $product->additional_shiping_rate_detail = $request->additional_shiping_rate_detail ?? null;
            $product->compliance_summary = $request->compliance_summary ?? null;
            $product->listing_duration = $request->listing_duration ?? null;
            $product->listing_status = $request->listing_status ?? null;
            $product->remove_listing = $request->remove_listing ?? 0;
            if ($request->listing_duration === "Publish") {
                $product->listing_release_date = Carbon::now()->format('Y-m-d'); // Set to today's date
            } else {
                $product->listing_release_date = $request->listing_release_date
                    ? Carbon::createFromFormat('m/d/Y', $request->listing_release_date)->format('Y-m-d')
                    : null;
            }
            $product->listing_end_date = $request->listing_end_date ? Carbon::createFromFormat('m/d/Y', $request->listing_end_date)->format('Y-m-d') : null;
            $product->type = "product";
            $product->save();

            foreach ($request->key_feature as $featureName) {
                $feature = KeyFeature::firstOrCreate(['feature' => $featureName]); // Create if not exists
                $featureIds[] = $feature->id; // Collect feature IDs
            }
            $regionIds[] = $request->regions;

            $product->categories()->sync((array) $request->product_category);
            $product->afterSaleSupports()->sync($request->after_sales_support);
            $product->keyFeatures()->sync($featureIds);
            $product->regions()->sync($regionIds);

            // Store document files with titles
            if ($request->hasFile('upload_document')) {
                foreach ($request->file('upload_document') as $index => $document) {
                    $media = $product->addMedia($document)
                        ->toMediaCollection('documents');

                    // Add custom property for document title if provided
                    if (!empty($request->document_title[$index])) {
                        $media->setCustomProperty('document_title', $request->document_title[$index]);
                        $media->save();
                    }
                }
            }

            // Get the array of part numbers from the request
            $submittedPartNumbers = collect($request->product_prices)->pluck('part_number')->toArray();

            // Delete product prices that are not in the request
            ProductPricing::where('product_id', $product->id)
                ->whereNotIn('part_number', $submittedPartNumbers)
                ->delete();

            // Insert or update product pricing records
            foreach ($request->product_prices as $price) {
                ProductPricing::updateOrCreate(
                    [
                        'product_id' => $product->id, // Search criteria
                        'part_number' => $price['part_number']
                    ],
                    [
                        'price' => $price['price'] ?? null,
                        'per_unit' => $price['unit'] ?? null,
                        'min_order' => $price['min_order'] ?? null,
                        'payment_method' => $price['payment_terms'] ?? null,
                        'notes' => $price['notes'] ?? null
                    ]
                );
            }
            // Insert or update product pricing records
            foreach ($request->shipping_details as $shipping) {
                ProductShipping::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'destination' => $shipping['destination']
                    ],
                    [
                        'shipping_method' => $shipping['shipping_method'] ?? null,
                        'estimated_delivery_time' => $shipping['shipping_time'] ?? null,
                        'shipping_rate' => $shipping['shipping_rate'] ?? null,
                        'notes' => $shipping['notes'] ?? null
                    ]
                );
            }


            // Get submitted attribute names
            $submittedAttributes = collect($request->product_attributes)->pluck('name')->toArray();

            // Delete attributes that are not in the request
            ProductAttribute::where('product_id', $product->id)
                ->whereNotIn('attribute', $submittedAttributes)
                ->delete();

            // Insert or update attributes
            foreach ($request->product_attributes as $attribute) {
                ProductAttribute::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'attribute' => $attribute['name']
                    ],
                    [
                        'characteristic' => $attribute['characteristic'] ?? null
                    ]
                );
            }


            // Get the array of certification titles from the request
            $submittedCertificates = collect($request->product_certificates)->pluck('title')->toArray();

            // Delete certifications that are not in the request
            ProductCertification::where('product_id', $product->id)
                ->whereNotIn('certificate_title', $submittedCertificates)
                ->delete();

            // Insert or update certifications
            foreach ($request->product_certificates as $certificate) {
                ProductCertification::updateOrCreate(
                    [
                        'product_id' => $product->id, // Search criteria
                        'certificate_title' => $certificate['title']
                    ],
                    [
                        'certification_body' => $certificate['body'] ?? null,
                        'certification_number' => $certificate['number'] ?? null,
                        'notes' => $certificate['notes'] ?? null
                    ]
                );
            }


            // Get the array of standard names from the request
            $submittedStandards = collect($request->product_compliance)->pluck('standard')->toArray();

            // Delete standards that are not in the request
            ProductStandardsCompliance::where('product_id', $product->id)
                ->whereNotIn('standard_name', $submittedStandards)
                ->delete();

            // Insert or update compliance records
            foreach ($request->product_compliance as $compliance) {
                ProductStandardsCompliance::updateOrCreate(
                    [
                        'product_id' => $product->id, // Search criteria
                        'standard_name' => $compliance['standard']
                    ],
                    [
                        'notes' => $compliance['details'] ?? null // Update notes if standard exists
                    ]
                );
            }
            DB::commit(); // ✅ Commit the transaction if everything is successful
        } catch (\Exception $e) {
            DB::rollBack(); // ❌ Rollback on error
            return response()->json([
                'success' => false,
                'message' => $e->getMessage() . $e->getLine()
            ], 500);
        }

        // Store all image after all other data has been saved

        // Store main image
        if ($request->hasFile('main_image')) {
            $product->addMedia($request->file('main_image'))
                ->toMediaCollection('main_image');

            $selectedMainIds = $request->selected_pre_main_image ?? [];
            $product->getMedia('main_image')->each(function ($media) use ($selectedMainIds) {
                if (!in_array($media->id, $selectedMainIds)) {
                    $media->delete(); // Delete media if not in selected_pre_thumb_image
                }
            });
        }

        // Remove media of thumb_images that are not in selected_pre_thumb_image
        $selectedThumbIds = $request->selected_pre_thumb_image ?? [];
        $product->getMedia('thumb_images')->each(function ($media) use ($selectedThumbIds) {
            if (!in_array($media->id, $selectedThumbIds)) {
                $media->delete(); // Delete media if not in selected_pre_thumb_image
            }
        });

        // Store thumbnail images
        if ($request->hasFile('thumb_images')) {
            foreach ($request->file('thumb_images') as $thumb) {
                $product->addMedia($thumb)
                    ->toMediaCollection('thumb_images');
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Product Updated successfully.",
        ], 201);
    }

    public function destroy($id)
    {
        Product::findOrFail($id)->delete();
        return response()->json(['success' => 'Deleted successfully']);
    }

    public function deleteSelected(Request $request)
    {
        if (is_array($request->product_ids)) {
            $products = Product::whereIn('uuid', $request->product_ids)->get();
            foreach ($products as $product) {
                $product->delete();
                // $product->searchable();
            }
        } else {
            $product = Product::where('uuid', $request->product_ids)->first();
            $product->delete();
            // $product->searchable();
        }
        return response()->json(['success' => true]);
    }

    public function activateSelected(Request $request)
    {
        $products = Product::whereIn('uuid', $request->product_ids)->get();
        foreach ($products as $product) {
            $product->update([
                'remove_listing' => 0,
            ]);
            $product->searchable();
        }
        return response()->json(['success' => true]);
    }

    public function pauseSelected(Request $request)
    {
        $products = Product::whereIn('uuid', $request->product_ids)->get();
        foreach ($products as $product) {
            $product->update([
                'remove_listing' => 1,
                'listing_status' => "Inactive",
            ]);
            $product->searchable();
        }
        return response()->json(['success' => true]);
    }
}
