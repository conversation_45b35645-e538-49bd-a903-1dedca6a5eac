<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyAssessmentStatus
 *
 * @property int $id
 * @property int $assessment_id
 * @property int $updated_by
 * @property int $status
 */
class CompanyAssessmentStatus extends Model
{
    protected $fillable = [
        'assessment_id',
        'updated_by',
        'status',
    ];

    public function assessment()
    {
        return $this->belongsTo(CompanyAssessment::class);
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
