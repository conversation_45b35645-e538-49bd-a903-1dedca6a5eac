<?php

namespace App\Repositories;

use App\Models\CompanyAd;

/**
 * Class CompanyAdRepository
 *
 * Handles data operations for the CompanyAd model.
 */
class CompanyAdRepository extends BaseRepository
{
    /**
     * The CompanyAd model instance.
     *
     * @var \App\Models\CompanyAd
     */
    protected $model;

    /**
     * CompanyAdRepository constructor.
     *
     * @param \App\Models\CompanyAd $companyAd
     */
    public function __construct(CompanyAd $companyAd)
    {
        $this->model = $companyAd;
    }
}
