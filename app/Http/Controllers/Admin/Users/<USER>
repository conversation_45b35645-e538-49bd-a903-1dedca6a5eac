<?php

namespace App\Http\Controllers\Admin\Users;

use App\Http\Requests\Admin\UpdateUserRequest;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Support\Facades\Gate;

class UserController extends BaseUserController
{
    /**
     * Create a new controller instance.
     *  
     * @param UserService $service
     * @return void
     */
    public function __construct(UserService $service)
    {
        $role = User::ROLE_USER;
        $routePrefix = 'admin.users.';
        $title = 'User';
        
        parent::__construct($service, $role, $routePrefix, $title); 
    }


    public function show(User $user)
    {
        if (!Gate::any(['manage users', 'view users'])) {
            abort(403);
        }

        return $this->parseShow($user);
    }

    public function edit(User $user)
    {
        return $this->parseEdit($user);
    }

  
    public function update(UpdateUserRequest $request, User $user)
    {
        $request->validated();
        return $this->parseUpdate($request, $user);
    }
}
