<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->tinyInteger('status')
            ->default(0)
            ->comment('0 = Pending, 1 = Active, 2 = Rejected, 3 = Approved, 4 = Closed, 5 = Freezed')
            ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->tinyInteger('status')
            ->default(0)
            ->comment('0 = Pending, 1 = Approved, 2 = Rejected')
            ->change();
        });
    }
};
