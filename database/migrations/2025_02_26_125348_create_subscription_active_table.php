<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_active', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->unique();
            $table->unsignedBigInteger('stripe_subscription_id');
            $table->string('stripe_customer_id')->unique();
            $table->unsignedBigInteger('user_id');
            $table->enum('billing_cycle', ['monthly', 'annual']);
            $table->string('currency');
            $table->decimal('cost', 8, 2);
            $table->enum('last_payment_status', ['paid', 'failed']);
            $table->timestamp('last_payment_date')->nullable();
            $table->timestamps();
            
            $table->foreign('stripe_subscription_id')->references('id')->on('stripe_subscriptions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_active');
    }
};
