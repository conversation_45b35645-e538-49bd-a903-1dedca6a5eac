// imageHandler.js
import {reinitializeSlider} from './company-dashboard';
export let orgProfilePhoto = '';
export let crpProfilePhoto = '';
export let orgCoverImage = '';
export let crpCoverImage = '';
export let companyOrgLogoImage = '';
export let companyOrgCovImage = '';
export let companyOrgAdvtImage = '';
export let companyCrpLogoImage = '';
export let companyCrpCovImage = '';
export let companyCrpAdvtImage = '';
export let companyOrgAwardImages = [];
export let companyCrpAwardImages = [];
export let companyOrgCaseStudyImages = [];
export let companyCrpCaseStudyImage = '';
export let serviceMainOrgImage = '';
export let serviceMainCrpImage = '';
export let productMainOrgImage = '';
export let productMainCrpImage = '';

let imageInput = '';
let cropperInstance = null;
let currentDropArea = null;
let multipleImage = false;

export function initImageHandlers() {
    $('.drag-area').each(function () {
        bindDragAreaEvents($(this));
    });
}

export function bindDragAreaEvents($area) {
    let $dropArea = $area;
    let $dragText = $dropArea.find('.header');
    let $button = $dragText.find('.button');
    let $input = $dropArea.find("input");

    let $file;

    $button.on('click', function () {
        $input.click();
    });

    $input.on("change", function () {
        imageInput = $input.attr('name');
        const files = Array.from(this.files);
        if (!files.length) return;
        if(imageInput == 'case_study[]'){
            multipleImage = true;
            handleMultipleImages(files, $dropArea, $input);
        } else {
            $dropArea.html('');
            $file = files[0];
            if (!$file) return;
            
            currentDropArea = $dropArea; // Set current drop area
    
            $dropArea.addClass("active");
            // displayFile($dropArea, $file);
            openCropperModal($file);
            assignOriginalImage(imageInput, $file);
        }
        
    });

    $dropArea.on('dragover', function (e) {
        e.preventDefault();
        $dropArea.addClass('active');
        $dragText.text('Release to Upload');
    });

    $dropArea.on('dragleave', function (e) {
        e.preventDefault();
        $dropArea.removeClass("active");
        $dragText.text('Image');
    });

    $dropArea.on('drop', function (e) {
        e.preventDefault();
        if (e.originalEvent.dataTransfer?.files?.length) {
        imageInput = $dropArea.find('input').attr('name');
        const files = Array.from(e.originalEvent.dataTransfer.files || []);
      
        if(imageInput == 'case_study[]'){
            multipleImage = true;
            handleMultipleImages(files, $dropArea, $dropArea.find('input'));
        }else{
            $dropArea.html('');
            currentDropArea = $dropArea; // Set current drop area
            let droppedFile = files[0];
            // displayFile($dropArea, droppedFile);
            openCropperModal(droppedFile);
            assignOriginalImage(imageInput, droppedFile);
            
        }
    }
        
    });

    // Add click handler for existing preview images
    $area.on('click', '.preview-img', function() {
        const $dropArea = $(this).closest('.drag-area');
        // Try to get the original image URL first, fall back to the preview image
        const originalImageUrl = $dropArea.find('.original-image-url').val();
        const imageUrl = originalImageUrl || $(this).attr('src');
        const inputName = $dropArea.find('input[type="file"]').attr('name');
        
        // Only proceed if we have all the necessary information
        if (imageUrl && inputName) {
            openCropperForExistingImage(imageUrl, inputName, $dropArea);
        }
    });

    function handleMultipleImages(files, $dropArea, $input) {
        if (files.length > 6) {
            alert('You can only upload up to 6 images.');
            return;
        }
        let firstFile = files[0];

        // Only crop the first image
        currentDropArea = $dropArea;
        imageInput = $input.attr('name');
        openCropperModal(firstFile);
        assignOriginalImage(imageInput, firstFile);

        // Display previews for all
        // files.slice(1).forEach((file, idx) => {
        files.forEach((file, idx) => {
            if(idx == 0){
                assignOriginalImage(imageInput, file);
            }else{
                assignOriginalImage(imageInput, file); // Use correct index for storage
                displayFile(currentDropArea, file);
            }
            
        });
    }

    function openCropperModal(file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            const imageElement = document.getElementById('cropper-image');
            imageElement.src = e.target.result;

            const modalElement = document.getElementById('imageEditorModal');
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            modalElement.addEventListener('shown.bs.modal', function () {
                if (cropperInstance) cropperInstance.destroy();

                cropperInstance = new Cropper(imageElement, {
                    aspectRatio: getAspectRatio($input),
                    viewMode: 1,
                    zoomable: true,
                    movable: false,
                    ready(){
                         // Apply circular mask conditionally
                        const viewBox = document.querySelector('.cropper-view-box');
                        const imageType = $input.data('image-type');
                        if (viewBox) {
                            if (imageType === 'profile') {
                                viewBox.style.borderRadius = '50%';
                            } else {
                                viewBox.style.borderRadius = '0';
                            }
                        }
                    }
                });
                

            }, { once: true });
        };
        reader.readAsDataURL(file);
    }
}

function getAspectRatio($input) {
    const imageType = $input.data('image-type');
    switch (imageType) {
        case "profile": return 1;
        case "cover": return 4 / 1;
        case "logo":
        case "award": return 1;
        case "listing":
        case "case-study":
        case "advertisement": return NaN;
        default: return 1;
    }
}

function assignOriginalImage(name, file) {
    switch (name) {
        case "profile_photo": orgProfilePhoto = file; break;
        case "cover": orgCoverImage = file; break;
        case "company_logo": companyOrgLogoImage = file; break;
        case "cover_image": companyOrgCovImage = file; break;
        case "advertisement_image": companyOrgAdvtImage = file; break;
        case "award_upload[]": 
            let indexAwd = currentDropArea.data('index');
            companyOrgAwardImages[indexAwd] = file;
            break;
        case "case_study[]":
            companyOrgCaseStudyImages.push(file);
        case "service_main_image": serviceMainOrgImage = file; break;
        case "product_main_image": productMainOrgImage = file; break;
        break;
    }
}

function assignCroppedImage(name, blob) {
    switch (name) {
        case "profile_photo": crpProfilePhoto = blob; break;
        case "cover": crpCoverImage = blob; break;
        case "company_logo": companyCrpLogoImage = blob; break;
        case "cover_image": companyCrpCovImage = blob; break;
        case "advertisement_image": companyCrpAdvtImage = blob; break;
        case "award_upload[]":
            let indexAwd = currentDropArea.data('index');
            companyCrpAwardImages[indexAwd] = blob;
            break;
        case "case_study[]":
            companyCrpCaseStudyImage = blob;
            break;
        case "service_main_image": serviceMainCrpImage = blob; break;
        case "product_main_image": productMainCrpImage = blob; break;
        break;
    }
}
document.getElementById('saveCroppedImage').addEventListener('click', () => {
    if (!cropperInstance) return;

    cropperInstance.getCroppedCanvas().toBlob(function (blob) {
        if (!blob) return;

        const reader = new FileReader();
        reader.onloadend = function () {
            assignCroppedImage(imageInput, blob);

            // Preview cropped image in the correct drop area
            if (currentDropArea) {
                if(multipleImage == false){
                    currentDropArea.html('');
                }
                displayFile(currentDropArea, reader.result); // `reader.result` is a Data URL
            }

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('imageEditorModal'));
            modal.hide();
        };

        reader.readAsDataURL(blob);
    }, "image/png");
});
function displayFile(dropArea, fileOrDataURL) {
    //This is for appenidng case study slides for cropped ones
    if (typeof fileOrDataURL === 'string') {
        if(imageInput == 'case_study[]'){
            appendCaseStudySlides(fileOrDataURL);
            return;
        }

        // It's a Data URL (e.g. from cropped image)
        let html = `
        <div class="preview-container position-relative">
            <img src="${fileOrDataURL}" alt="" class="preview-img w-100">
            <button class="remove-image btn btn-danger btn-sm position-absolute top-0 end-0">×</button>
        </div>`;
        $(dropArea).append(html);
        return;
    }

    //This is for original ones
    let fileType = fileOrDataURL.type;
    let validExtensions = ["image/jpeg", "image/jpg", "image/png"];
    if (validExtensions.includes(fileType)) {
        let fileReader = new FileReader();
        fileReader.onload = () => {
            let fileURL = fileReader.result;
            if(imageInput == 'case_study[]'){
                appendCaseStudySlides(fileURL);
                return;
            }
            let html = `
            <div class="preview-container position-relative">
                <img src="${fileURL}" alt="" class="preview-img w-100">
                <button class="remove-image btn btn-danger btn-sm position-absolute top-0 end-0">×</button>
            </div>`;
            $(dropArea).append(html);
        };
        fileReader.readAsDataURL(fileOrDataURL);
    } else {
        alert("This is not an Image File");
        $(dropArea).removeClass("active");
    }
}



function appendCaseStudySlides(URL){
    let modal = document.querySelector("#caseStudyModal");
    const sliderContainer = modal.querySelector('.modal-image-sllider');
    const imageSlide = document.createElement('div');
    imageSlide.className = 'modal-slide';
    imageSlide.innerHTML = `
        <img src="${URL}" />
        <i class="fa fa-close"></i>
    `;
    sliderContainer.appendChild(imageSlide);
        // Re-initialize Owl Carousel
        if ($(sliderContainer).hasClass('owl-loaded')) {
        $(sliderContainer).trigger('destroy.owl.carousel').removeClass("owl-loaded");
        $(sliderContainer).find('.owl-stage-outer').children().unwrap();
    }
    reinitializeSlider();
}

$(document).on('click', '.remove-image', function (e) {
    e.preventDefault();
    const $dropArea = $(this).closest('.drag-area');
    $dropArea.removeClass('active').html(`
        <div class="icon mx-auto border-logo company-logo">
            <img src="${uploadImage}"
                alt="">
        </div>
        <span class="header">
            <span class="button">Upload
                Photos<br> or drag and drop</span>
        </span>
        <input type="file" hidden name="${$dropArea.find('input').attr('name')}" data-image-type="${$dropArea.find('input').data('image-type')}">
    `);
    
    // Optional: reset internal variables
    const inputName = $dropArea.find('input').attr('name');
    switch (inputName) {
        case "company_logo": 
            companyOrgLogoImage = '';
            companyCrpLogoImage = '';
            break;
        case "cover_image":
            companyOrgCovImage = '';
            companyCrpCovImage = '';
            break;
        case "advertisement_image":
            companyOrgAdvtImage = '';
            companyCrpAdvtImage = '';
            break;
        case "award_upload[]": 
            companyOrgAwardImages = []; // or selectively remove
            companyCrpAwardImages = []; // or selectively remove7
            break;
        case "award_upload[]": 
            companyOrgCaseStudyImages = []; // or selectively remove
            companyCrpCaseStudyImage = ''; // or selectively remove7
            break;
        case "service_main_image": 
            serviceMainOrgImage = '';
            serviceMainCrpImage = '';
            break;
        case "product_main_image": 
            productMainOrgImage = '';
            productMainCrpImage = '';
            break;
        default: 
            break;
    }

    // Rebind events after resetting HTML
    bindDragAreaEvents($dropArea);

});

export function openCropperForExistingImage(imageUrl, inputName, $dropArea) { 
    
    // Create a temporary image element to load the image
    const tempImg = new Image();
    tempImg.crossOrigin = "anonymous";  // Handle CORS if needed
    
    tempImg.onload = function() {
        // Check if the image is too large and resize it if necessary
        let sourceWidth = tempImg.width;
        let sourceHeight = tempImg.height;
        let targetWidth = sourceWidth;
        let targetHeight = sourceHeight;
        
        // If image dimensions are too large, scale them down
        const MAX_DIMENSION = 1600; // Maximum dimension for either width or height
        if (sourceWidth > MAX_DIMENSION || sourceHeight > MAX_DIMENSION) {
            if (sourceWidth > sourceHeight) {
                targetWidth = MAX_DIMENSION;
                targetHeight = (sourceHeight / sourceWidth) * MAX_DIMENSION;
            } else {
                targetHeight = MAX_DIMENSION;
                targetWidth = (sourceWidth / sourceHeight) * MAX_DIMENSION;
            }
        }
        
        // Convert the loaded image to a blob with potential resizing
        const canvas = document.createElement('canvas');
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(tempImg, 0, 0, sourceWidth, sourceHeight, 0, 0, targetWidth, targetHeight);
        
        // Use a lower quality for JPEG to reduce file size
        canvas.toBlob(function(blob) {
            // Set global variables
            imageInput = inputName;
            currentDropArea = $dropArea;
            multipleImage = false;
            
            // Create a File object from the blob
            const file = new File([blob], "original-image.jpg", { type: "image/jpeg" });
            
            // Log the file size for debugging
            console.log(`Processed image size: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
            
            // Assign the original image to the appropriate variable
            assignOriginalImage(inputName, file);
            
            // Instead of using openCropperModal, let's directly set up the cropper
            const reader = new FileReader();
            reader.onload = function (e) {
                const imageElement = document.getElementById('cropper-image');
                if (!imageElement) {
                    console.error('Cropper image element not found');
                    return;
                }
                
                imageElement.src = e.target.result;
                
                const modalElement = document.getElementById('imageEditorModal');
                if (!modalElement) {
                    console.error('Image editor modal not found');
                    return;
                }
                
                // Make sure Bootstrap is available
                if (typeof bootstrap === 'undefined') {
                    console.error('Bootstrap is not defined');
                    return;
                }
                
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                
                modalElement.addEventListener('shown.bs.modal', function () {
                    if (cropperInstance) {
                        cropperInstance.destroy();
                    }
                    
                    const $input = $dropArea.find('input[type="file"]');
                    cropperInstance = new Cropper(imageElement, {
                        aspectRatio: getAspectRatio($input),
                        viewMode: 1,
                        zoomable: true,
                        movable: false,
                        ready() {
                            // Apply circular mask conditionally
                            const viewBox = document.querySelector('.cropper-view-box');
                            const imageType = $input.data('image-type');
                            if (viewBox) {
                                if (imageType === 'profile') {
                                    viewBox.style.borderRadius = '50%';
                                } else {
                                    viewBox.style.borderRadius = '0';
                                }
                            }
                        }
                    });
                }, { once: true });
            };
            reader.readAsDataURL(blob);
        }, 'image/jpeg', 0.85); // Use 85% quality JPEG to reduce file size
    };
    
    // Add error handling for the image loading
    tempImg.onerror = function() {
        console.error('Failed to load image:', imageUrl);
        alert('Failed to load the image. Please try again.');
    };
    
    // Start loading the image
    tempImg.src = imageUrl;
}

