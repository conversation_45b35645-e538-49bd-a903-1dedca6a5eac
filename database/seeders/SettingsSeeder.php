<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $json = file_get_contents(database_path('seeders/stubs/settings.json'));
        $settings = collect(json_decode($json, true));

        $settings->each(function ($setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']], // Match based on the unique 'key'
                [
                    'type' => $setting['type'] ?? Setting::TYPE_INPUT,
                    'name' => $setting['name'],
                    'section' => $setting['section'],
                    'value' => $setting['value'],
                ]
            );
        });
    }
}
