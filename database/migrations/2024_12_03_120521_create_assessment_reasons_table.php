<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('assessment_reasons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('section_id')->constrained('assessment_sections')->onDelete('cascade');
            $table->string('reason');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('assessment_reasons');
    }
};
