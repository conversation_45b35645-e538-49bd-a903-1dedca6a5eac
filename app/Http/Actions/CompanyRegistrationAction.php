<?php

namespace App\Http\Actions;

use App\Models\Company;
use App\Models\CompanyRegistration;
use App\Models\CompanyRegistrationStatus;
use App\Models\Country;
use App\Models\GeoCountry;
use Illuminate\Support\Facades\Session;

class CompanyRegistrationAction
{

   /**
     * Handles the registration of a new company.
     *
     * @param array $data The company data for registration.
     * @return void
     */
    public function __invoke(array $data)
    {
        return Session::put('companyRegistrationData', $data);
    }

    /**
     * Handles the registration of a new company.
     *
     * @param array $data The company data for registration.
     * @return void
     */
    public function storeCompanyAddress(array $data)
    {
        $phonePrefix = $data['phone_prefix'] ? explode('@', $data['phone_prefix']) : '';
        $companyRegistrationData = Session::get('companyRegistrationData'); // Get existing data or an empty array
        // $country = Country::where('name', $data['country'])->first();
        $companyRegistrationData['country_code'] = $data['country_code']; // Add new key-value pair
        $companyRegistrationData['city'] = $data['city'];
        $companyRegistrationData['postal_code'] = $data['postal_code'];
        $companyRegistrationData['latitude'] = $data['latitude'];
        $companyRegistrationData['longitude'] = $data['longitude'];
        $companyRegistrationData['address_line_1'] = $data['address_line_1'];
        $companyRegistrationData['address_line_2'] = $data['address_line_2'];
        $companyRegistrationData['phone_prefix'] = $data['number'] ? $phonePrefix[1] : '';
        $companyRegistrationData['number'] = $data['number'] ? $phonePrefix[1].$data['number'] : '';
        $companyRegistrationData['is_main'] = 1;
        Session::put(['companyRegistrationData' => $companyRegistrationData]);
    }

    public function storeCompanyEmail($data)
    {
        $companyRegistrationData = Session::get('companyRegistrationData');
        $companyRegistrationData['email'] = $data['email'];
        Session::put(['companyRegistrationData' => $companyRegistrationData]);
        $user = auth()->user();
        $user->sendEmailVerification($data['email']);
    }

    public function storeCompanyRegistrationStatus():CompanyRegistrationStatus
    {
        $data = Session::get('companyRegistrationData');
        //Fetching country_id by code
        $country = GeoCountry::where('code', $data['country_code'])->first();
        $companyRegistrationStatus = \DB::transaction(function () use ($data, $country) {
            $companyRegistration = CompanyRegistration::create([
                'user_id' => auth()->user()->id,
                'registered_location' => $data['registered_location'],
                'organisation_type_id' => $data['organisation_type'],
                'organisation_size_id' => $data['organisation_size'],
                'business_sector_id' => $data['main_business_sector'],
                'company_name' => $data['company_name'],
                'position' => $data['position'],
                'website_url' => $data['company_website_url'],
                'country_id' => $country->id,
                'city' => $data['city'],
                'postcode' => $data['postal_code'],
                'latitude' => $data['latitude'],
                'longitude' => $data['longitude'],
                'address_line_1' => $data['address_line_1'],
                'address_line_2' => $data['address_line_2'],
                'phone_prefix' => $data['number'] ? $data['phone_prefix'] : '',
                'phone' => $data['number'] ? $data['number'] : '',
                'is_main' => 1,
                'email' => $data['email']
            ]);
            Session::forget('companyRegistrationData');
            return CompanyRegistrationStatus::create([
                'user_id' => auth()->user()->id,
                'registration_id' => $companyRegistration->id,
                'status' => 0,
                'attempt_number' => 1
            ]);
        });
        return $companyRegistrationStatus;
    }

    

}