<div>
    <div class="search-area">
        <div class="search-element">
            <img src="{{ asset('images/icons/search-v.svg') }}" alt="search" />
            <input type="search" wire:model.live="search" class="form-control" placeholder="Search" />
        </div>

        <div class="dropdown filter-group modal-filter">
            <button class="btn dropdown-toggle" type="button" id="dropdownFilterMenu" data-bs-toggle="dropdown"
                aria-expanded="false">
                <span>Filter ({{ $filter ? 1 : 0 }})</span>
                <img src="{{ asset('images/icons/fillter-drop.svg') }}" alt="dropdown" />
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownFilterMenu">
                <li><a wire:click="$set('filter', 'all')"
                        class="dropdown-item {{ $filter === 'all' ? 'active' : '' }}">All</a></li>
                <li><a wire:click="$set('filter', 'unread')"
                        class="dropdown-item {{ $filter === 'unread' ? 'active' : '' }}">Unread</a></li>
                <li><a class="dropdown-item" href="#" wire:click.prevent="setFilter('Users')">Users</a></li>
                <li><a class="dropdown-item" href="#" wire:click.prevent="setFilter('Suppliers')">Suppliers</a>
                </li>
            </ul>
        </div>
    </div>

    <h3 class="tab-heading">

        @if (count($selectedConversations) > 0)
            <div class="archive-toolbar d-flex flex-column gap-2 p-3 mb-3 bg-light rounded border">
                {{-- First row: selected count & archive/cancel --}}
                <div class="d-flex justify-content-between align-items-center gap-2">
                    @if (!$confirmingArchive)
                        <div>
                            <strong class="me-3">{{ count($selectedConversations) }}</strong> selected
                        </div>
                    @endif

                    @if (!$confirmingArchive)
                        <div class="d-flex gap-2">
                            <button wire:click="confirmArchive" class="btn btn-sm btn-warning">Archive</button>
                            <button wire:click="cancelSelection" class="btn btn-sm btn-secondary">Cancel</button>
                        </div>
                    @endif
                </div>

                {{-- Second row: confirmation prompt --}}
                @if ($confirmingArchive)
                    <div class="d-flex flex-column gap-2">
                        <p class="mb-2">
                            Are you sure you want to archive this conversation?<br>
                            It will be moved to Archived Conversations. You can restore it anytime by continuing the
                            chat.
                        </p>

                        <div class="d-flex gap-2">
                            <button wire:click="archiveSelected" class="btn btn-sm btn-danger">Yes</button>
                            <button wire:click="$set('confirmingArchive', false)"
                                class="btn btn-sm btn-outline-secondary">No</button>
                        </div>
                    </div>
                @endif

            </div>
        @endif



        @if (count($selectedConversations) == 0)
            <div class="dropdown conversation-toggle">

                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="conversationTypeDropdown"
                    data-bs-toggle="dropdown" aria-expanded="false">

                    {{ $conversationView === 'archived' ? 'Archived Conversations' : 'Conversations' }}

                </button>

                <ul class="dropdown-menu" aria-labelledby="conversationTypeDropdown">
                    <li><a wire:click="$set('conversationView', 'live')"
                            class="dropdown-item {{ $conversationView === 'live' ? 'active' : '' }}">Conversations</a>
                    </li>
                    <li><a wire:click="$set('conversationView', 'archived')"
                            class="dropdown-item {{ $conversationView === 'archived' ? 'active' : '' }}">Archived
                            Conversations</a></li>
                </ul>
            </div>
        @endif

        @if (!$confirmingArchive)
            <a href="#" id="showSelect" class="checkbox-notSelected"
                wire:click="$toggle('showingCheckboxes')">Select</a>
        @endif
    </h3>

    {{-- Searchable Results --}}
    @if ($search && !$searchableUsers->isEmpty())
        <ul class="chat-contianer">
            @foreach ($searchableUsers as $user)
                @include('components.chat-user', ['user' => $user])
            @endforeach
        </ul>
    @endif

    {{-- Normal User List Based on Conversation View --}}
    @if (!$search)
        <ul class="chat-contianer">
            <li class="total-user-count" data-user-count="{{ $users->count() }}"></li>
            @forelse ($users as $user)
                @include('components.chat-user', ['user' => $user])
            @empty
                <li>
                    <div class="text-center p-3">
                        No conversations{{ $filter === 'unread' ? ' with unread messages' : '' }} in
                        {{ $conversationView === 'archived' ? 'archived' : 'active' }} tab.
                    </div>
                </li>
            @endforelse
        </ul>
    @endif
</div>
