<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class NotificationUserCategory extends Model
{
    protected $table = 'notifications_user_categories';
    protected $fillable = ['name', 'description'];

    public function assignees()
    {
        return $this->belongsToMany(User::class, 'notification_assignees', 'category_id', 'user_id');
    }
    public function notifications()
    {
        return $this->hasMany(BusinessNotification::class, 'category_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
