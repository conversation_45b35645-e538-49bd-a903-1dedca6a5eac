<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('plan_id')->constrained()->cascadeOnDelete(); // References 'plans.id'
            $table->foreignId('user_id')->constrained()->cascadeOnDelete(); // References 'users.id'
            $table->string('transaction_id')->nullable();
            $table->json('info')->nullable();
            $table->float('price', 8, 2)->default(0.00);
            $table->tinyInteger('status')->default(0); // 0 = pending, 1 = active, 2 = ended
            $table->timestamp('started_at');
            $table->timestamp('ended_at')->nullable();
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
