<?php

namespace App\Http\Controllers\Admin\ConnectHub;

use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use App\Models\NotificationHonleyCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use App\Models\AdminNotificationReply;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    public function sent(Request $request)
    {
        if ($request->ajax()) {
            return $this->listSent($request);
        }

        return view('admin.connecthub.notifications.sent');
    }

    protected function listSent(Request $request)
    {
        $notifications = AdminNotification::with('media')
            //->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->get();

        return DataTables::of($notifications)
            ->addIndexColumn()
            ->addColumn('subject', fn($n) => $n->subject)
            ->addColumn('status', function ($n) {
                $label = ucfirst($n->status);
                $color = match ($n->status) {
                    'resolved' => 'success',
                    'in_progress' => 'warning',
                    default => 'secondary',
                };
                return '<span class="badge bg-' . $color . '">' . $label . '</span>'; 
            })
            ->addColumn('attachment', function ($n) {
                $media = $n->getFirstMedia('attachments');
                return $media
                    ? '<a href="' . $media->getUrl() . '" target="_blank" class="btn btn-sm btn-outline-info">View</a>'
                    : '<span class="text-muted">None</span>';
            })
            ->addColumn('created_at', fn($n) => $n->created_at->format('d M Y, h:i A'))
            ->addColumn('action', function ($n) {
                $view = route('admin.connecthub.notifications.show', $n->id);
                $delete = route('admin.connecthub.notifications.destroy', $n->id);
                return <<<HTML
<a href="{$view}" class="btn btn-sm btn-primary">View</a>
<form id="delete-form-{$n->id}" action="{$delete}" method="POST" style="display:inline-block;">
    <input type="hidden" name="_token" value="{$n->_token}">
    <input type="hidden" name="_method" value="DELETE">
    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Delete this notification?')">Delete</button>
</form>
HTML;
            })
            ->rawColumns(['status', 'action', 'attachment'])
            ->toJson();
    }

    public function create()
    {
        $categories = NotificationHonleyCategory::all();
        $roles = Role::all();
        $users = User::all();

        return view('admin.connecthub.notifications.create', compact('categories', 'roles', 'users'));
    }

    public function send(Request $request)
    {
        $request->validate([
            'sendToType' => 'required|in:role,individual',
            'role' => 'required_if:sendToType,role',
            'users' => 'required_if:sendToType,individual|array',
            'category' => 'required|string',
            'subject' => 'required|string',
            'message' => 'required|string',
            'attachment' => 'nullable|file|mimes:pdf,docx,jpg,jpeg,png|max:2048',
        ]);

        $recipients = collect();

    
        if ($request->sendToType === 'individual' && $request->filled('users')) {
            $recipients = User::whereIn('id', $request->users)->get();
        }

        if ($request->sendToType === 'role' && $request->filled('role')) {
            try {
                $role = Role::findByName($request->role);
                $recipients = $recipients->merge($role->users);
            } catch (\Exception $e) {
                \Log::error('Role error: ' . $e->getMessage());
            }
        }

        foreach ($recipients as $user) {
            $notification = AdminNotification::create([
                'user_id' => $user->id,
                'reference_number' => 'ADM-' . strtoupper(Str::random(6)),
                'category' => $request->category,
                'subject' => $request->subject,
                'message' => $request->message,
                'allow_replies' => $request->has('allow_replies'),
                'assigned_to' => Auth::id(),
                'status' => 'open',
            ]);

            // Store file using Spatie Media Library
            if ($request->hasFile('attachment')) {
                $notification
                    ->addMediaFromRequest('attachment')
                    ->toMediaCollection('attachments');
            }
        }

        return redirect()
            ->route('admin.connecthub.notifications.sent')
            ->with('success', 'Notification sent to ' . $recipients->count() . ' user(s).');
    }

    public function show($id)
    {
        $notification = AdminNotification::with('replies.sender','media')
        ->findOrFail($id);

        return view('admin.connecthub.notifications.show', compact('notification'));
    }
}
