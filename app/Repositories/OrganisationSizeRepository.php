<?php

namespace App\Repositories;

use App\Models\OrganisationSize;

/**
 * Class OrganisationSizeRepository
 *
 * Handles data operations for the OrganisationSize model.
 */
class OrganisationSizeRepository extends BaseRepository
{
    /**
     * The OrganisationSize model instance.
     *
     * @var \App\Models\OrganisationSize
     */
    protected $model;

    /**
     * OrganisationSizeRepository constructor.
     *
     * @param \App\Models\OrganisationSize $organisationSize
     */
    public function __construct(OrganisationSize $organisationSize)
    {
        $this->model = $organisationSize;
    }
}
