<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;

class CompanyDashboardSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $company = auth()->user()->company;
        return [
            'registered_location' => ['required', 'string'],
            'organisation_type' => ['required','numeric'],
            'organisation_size' => ['required','numeric'],
            'year_founded' => ['required','string'],
            'about_the_company' => ['required','string', 'max:3500'],
            'level_2_category.*' => ['nullable'],
            'level_3_category.*' => ['nullable'],
            'continents.*' => ['nullable'],
            'countries.*' => ['nullable'],
            'regions.*' => ['nullable'],
            'company_org_logo' => [$company->getFirstMedia('company_org_logo') ? 'nullable' : 'required', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'company_crp_logo' => [$company->getFirstMedia('company_crp_logo') ? 'nullable' : 'required', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'company_org_cov_image' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'company_crp_cov_mage' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'company_org_adv_image' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'company_crp_adv_image' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'duration_from' => ['nullable'],
            'duration_to' => ['nullable'],
            'company_website_url' => ['nullable'],
            'workplace_verification' => ['nullable'],
            'allow_email_forwarding' => ['nullable'],
            'phone_prefix' => ['required', 'nullable'],
            'phone_number' => ['required', 'nullable'],
            'display_phone' => ['nullable'],
            'phone_contact_title' => ['nullable', 'string'],
            'notification_access_ids' => ['nullable', 'array'],
            'company_chat' => ['nullable'],
            'company_iso_compliance_id.*' => ['nullable', 'numeric'],
            'iso_standard_code.*' => ['nullable', 'string'],
            'standard_title.*' => ['nullable', 'string'],
            'certification_number.*' => ['nullable', 'string', 'max:80'],
            'notes.*' => ['nullable', 'string', 'max:255'],
            'company_qualification_id.*' => ['nullable', 'numeric'],
            'qualification_title.*' => ['nullable', 'string', 'max:80'],
            'qualification_note.*' => ['nullable', 'string', 'max:255'],
            'company_certificate_id.*' => ['nullable', 'numeric'],
            'certificate_title.*' => ['nullable', 'string', 'max:80'],
            'certification_body.*' => ['nullable', 'string', 'max:255'],
            'certificate_number.*' => ['nullable', 'string', 'max:255'],
            'certificate_note.*' => ['nullable', 'string', 'max:255'],
            'compiliance_summary'  => ['nullable', 'string', 'max:3500'],
            'org_award_image.*' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'crp_award_image.*' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,webp,bmp,svg', 'max:2048'],
            'award_title.*' => ['nullable', 'string', 'max:80'],
            'award_year.*' => ['nullable', 'string', 'max:80'],
            'award_description.*' => ['nullable', 'string', 'max:255'],
            'award_id.*' => ['nullable'], 
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $awardIds = $this->input('award_id', []);
            $orgAwardImages = $this->file('org_award_image', []);
            $crpAwardImages = $this->file('crp_award_image', []);
            $awardTitles = $this->input('award_title', []);
            foreach ($awardIds as $index => $id) {
                $hasAwardId = isset($awardIds[$index]) && $awardIds[$index] !== null;
                if(!isset($orgAwardImages[$index])){
                    array_unshift($orgAwardImages, null);
                }
                $hasImage = $orgAwardImages[$index] !== null;
                $hasAwardTitle = isset($awardTitles[$index]) && $awardTitles[$index] !== null;
                 
                if ((!$hasAwardId && !$hasImage) || (!$hasAwardId && $hasAwardTitle && !$hasImage)) {
                    $validator->errors()->add("award_image.$index", "Award image is required.");
                } 
            }


        });
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.email' => 'Invalid email format. Please enter a valid email.',
            'email.regex' => 'Invalid email format. Please enter a valid email.',
            'email.unique' => 'This email is already in use. Please enter a different email.',
        ];
    }
}
