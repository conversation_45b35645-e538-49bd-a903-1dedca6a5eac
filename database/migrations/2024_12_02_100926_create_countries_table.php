<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->char('code', 2)->nullable();
            $table->char('currency_code', 3)->nullable();
            $table->char('fips_code', 2)->nullable();
            $table->char('iso_numeric', 4)->nullable();
            $table->string('continent_name', 100)->nullable();
            $table->char('continent', 2)->nullable();
            $table->string('phone_prefix', 10);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
