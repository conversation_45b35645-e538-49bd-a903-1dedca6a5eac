<?php

namespace Database\Factories;

use App\Models\Area;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Area>
 */
class AreaFactory extends Factory
{
    protected $model = Area::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->city,
            'slug' => null, // Will be auto-generated by the `HasSlug` trait
            'country_id' => Country::factory(),
        ];
    }
}
