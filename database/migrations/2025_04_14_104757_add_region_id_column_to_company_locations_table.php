<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_locations', function (Blueprint $table) {
            if (Schema::hasColumn('company_locations', 'country_id')) {
                $table->dropForeign(['country_id']);
                $table->dropColumn('country_id');
            }
            $table->foreignId('geo_country_id')
                ->after('location_type_id')
                ->constrained('geo_countries')
                ->cascadeOnDelete()
                ->default(0);
            $table->foreignId('geo_region_id')
                ->after('geo_country_id')
                ->constrained('geo_regions')
                ->cascadeOnDelete()
                ->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_locations', function (Blueprint $table) {
            //
        });
    }
};
