<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_contact_history', function (Blueprint $table) {
            // Primary Key
            $table->id();

            // Foreign Key
            $table->unsignedBigInteger('user_id');

            // Contact Change Information
            $table->enum('contact_type', ['email', 'phone']);
            $table->string('old_value', 255)->nullable();
            $table->string('new_value', 255);
            $table->enum('status', ['pending', 'successful', 'failed']);
            $table->text('failure_reason')->nullable();

            // Request Timestamps
            $table->timestamp('requested_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('processed_at')->nullable();

            // Additional Details
            $table->string('user_ip_address', 45);
            $table->unsignedBigInteger('country_id')->nullable();
            $table->string('address_line_1', 255)->nullable();
            $table->string('address_line_2', 255)->nullable();
            $table->string('city', 255)->nullable();
            $table->string('postcode', 255)->nullable();
            $table->string('latitude', 255)->nullable();
            $table->string('longitude', 255)->nullable();
            $table->tinyInteger('is_main')->default(0);

            // Foreign Key Constraints
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->foreign('country_id')->references('id')->on('countries')->nullOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_contact_history');
    }
};
