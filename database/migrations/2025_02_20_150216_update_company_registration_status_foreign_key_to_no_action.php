<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_registration_status', function (Blueprint $table) {
            
            $table->dropForeign(['registration_id']);

            $table->foreign('registration_id')
                ->references('id')
                ->on('company_registrations')
                ->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_registration_status', function (Blueprint $table) {

            $table->dropForeign(['registration_id']);
            
            $table->foreign('registration_id')
                ->references('id')
                ->on('company_registrations')
                ->onDelete('cascade');
        });
    }
};
