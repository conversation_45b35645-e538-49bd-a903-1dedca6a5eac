<?php

namespace App\Services;

use App\Models\GeoCountry;
use Illuminate\Support\Facades\Http;

Class CalculateRegionService{

    public function calculateRegion($countryId, $lat, $long)
    {
        //Fetching country_id by code
        $pointWKT = "POINT({$lat} {$long})";
        //Fetching region_id by country_id and point
        $region = \DB::table('geo_regions as r')
        ->join('geo_countries as c', 'r.country_id', '=', 'c.id')
        ->select('r.id as region_id')
        ->where('c.id', $countryId)
        ->whereRaw("ST_Contains(r.shape, ST_GeomFromText(?, 4326))", [$pointWKT])
        ->first();
        return $region ? $region->region_id : '';
    }

   
    public function calculateNearestRegion($radius, $lat, $long, $countryId)
    {
        $nearbyRegionIds = \DB::table('geo_regions as r')
                            ->where('r.country_id', $countryId)
                            ->whereRaw(
                                "(6371 * acos(cos(radians(?)) * cos(radians(r.latitude)) * cos(radians(r.longitude) - radians(?)) + sin(radians(?)) * sin(radians(r.latitude)))) < ?",
                                [$lat, $long, $lat, $radius]
                            )
                            ->pluck('r.id')
                            ->toArray();
        // $nearbyRegionIds = \DB::table('geo_regions as r')
        //     ->select('r.id')
        //     ->selectRaw(
        //         "(6371 * acos(cos(radians(?)) * cos(radians(r.latitude)) * cos(radians(r.longitude) - radians(?)) + sin(radians(?)) * sin(radians(r.latitude)))) AS distance",
        //         [$lat, $long, $lat]
        //     )
        //     ->where('r.country_id', $countryId)
        //     ->having('distance', '<', $radius)
        //     ->pluck('id');
            // $nearbyRegionIds = \DB::table('geo_regions as r')
            //     ->join('geo_countries as c', 'r.country_id', '=', 'c.id')
            //     ->select('r.id')
            //     ->selectRaw(
            //         "(6371 * acos(cos(radians(?)) * cos(radians(r.latitude)) * cos(radians(r.longitude) - radians(?)) + sin(radians(?)) * sin(radians(r.latitude)))) AS distance",
            //         [$lat, $long, $lat]
            //     )
            //     ->where('c.id', $countryId)
            //     ->having('distance', '<', $radius)
            //     ->orderBy('distance')
            //     ->pluck('r.id');

        return $nearbyRegionIds;
   }

}

