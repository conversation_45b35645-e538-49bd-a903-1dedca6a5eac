<?php

use App\Models\PlanFeature;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
if (!function_exists('userHasFeatureAccess')) {
    function userHasFeatureAccess($featureSlug): bool
    {
        $user = auth()->user();

        if (!$user || !$user->stripeSubscription) {
            return false;
        }

        return $user->stripeSubscription
            ->features()
            ->where('slug', $featureSlug)
            ->exists();
    }

    function userFeatureLimit($featureSlug): ?int
    {
        $user = auth()->user();

        if (!$user || !$user->stripeSubscription) {
            return null;
        }

        $feature = $user->stripeSubscription
            ->features()
            ->where('slug', $featureSlug)
            ->first();

        return $feature?->pivot->limit;
    }
}