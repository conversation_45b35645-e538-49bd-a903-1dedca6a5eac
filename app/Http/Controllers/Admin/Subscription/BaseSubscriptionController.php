<?php

namespace App\Http\Controllers\Admin\Subscription;

use App\Http\Controllers\Controller;
use App\Models\CompanySubscription;
use App\Models\SubscriptionActive;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Services\SubscriptionService;

class BaseSubscriptionController extends Controller
{
    /**
     * SubscriptionService service
     *
     * @var SubscriptionService
     */
    public $service;


    /**
     * Route prefix based on the role of users
     *
     * @var $routePrefix
     */
    public $routePrefix;

    /**
     * Title of the page
     *
     * @var $title
     */
    public $title;

    /**
     * Create a new controller instance.
     *
     * @param SubscriptionService $service
     * @param string $routePrefix
     * @param string $title
     * @return void
     */
    public function __construct(
        SubscriptionService $service,
        string $routePrefix,
        string $title
    ) {
        $this->service = $service;
        $this->routePrefix = $routePrefix;
        $this->title = $title;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        $user = $this->service->current();
        $status = $request->status ?? null;
        $title = $this->title;
        $routePrefix = $this->routePrefix;

        return view('admin.subscriptions.index', compact('user', 'status', 'title', 'routePrefix'));
    }

    protected function list($request)
    {
        $status = $request->status;
        $user = $this->service->current();
        $entrys = $this->service->getListForAdmin($status);
        
        return DataTables::of($entrys)

            ->addColumn('last_payment_status',function ($subscription) {
               return strtoupper($subscription->last_payment_status);
            })
            ->addColumn('company_name', function ($subscription) {
                return $subscription->company->name ?? 'N/A';
            })
            ->addColumn('username', function ($subscription) {
                return $subscription->user->name ?? 'N/A';
            })
            ->addColumn('subscription_type', function ($subscription) {
                return $subscription->type ?? 'N/A';
            })
            ->addColumn('plan_name', function ($subscription) {
                return $subscription->subscription_plan->name ?? 'N/A';
            })
            ->addColumn('product_id', function ($subscription) {
                return $subscription->subscription_plan->stripe_product_id ?? 'N/A';
            })
            ->addColumn('currency', function ($subscription) {
                return strtoupper($subscription->currency) ?? 'N/A';
            })
            ->addColumn('last_payment_date', function ($subscription) {
                return $subscription->last_payment_date 
                    ? \Carbon\Carbon::parse($subscription->last_payment_date)->format('d-M-Y')
                    : 'N/A';
            })
            
            ->addColumn('downgrade_pending', function ($subscription) {
              return $subscription->company->pending_downgrade ? 'Yes' : 'No';
            })
            ->addColumn('downgrade_due_date', function ($subscription) {
                return ($subscription->company->pending_downgrade && $subscription->company->pending_downgrade->downgrade_status === 'pending')
                    ? \Carbon\Carbon::parse($subscription->company->pending_downgrade->downgrade_date)->format('d-M-Y')
                    : 'N/A';
            })
            
            ->addColumn('action', function ($subscription) use ($user) {
                $action = '';
                if ($user->can('manage subscriptions') || $user->can('view subscriptions')) {
                    $action .= '<a href="' . route('admin.subscriptions.show', $subscription) . '" class="btn btn-primary">View</a> ';
                }

                return $action;
            })
            ->addColumn('start_date', function ($subscription) {
                return $subscription->start_date ? $subscription->start_date->format('Y-m-d') : 'N/A';
            })
            ->addColumn('end_date', function ($subscription) {
                return $subscription->end_date ? $subscription->end_date->format('Y-m-d') : 'N/A';
            })
            ->addColumn('amount', function ($subscription) {
                return number_format($subscription->amount, 2) . ' ' . $subscription->currency;
            })
            ->addColumn('plan_name', function ($subscription) {
                return $subscription->subscription_plan->name ?? 'N/A';
            })
            ->rawColumns(['status', 'action'])
            ->toJson();

    }

    public function parseShow(SubscriptionActive $subscription)
    {
        //dd($subscription->company->subscription_change_logs);
        $title = $this->title;
        $routePrefix = $this->routePrefix;
        $subscriptionListLink = route($routePrefix . 'index');

        return view('admin.subscriptions.show', compact('subscription', 'subscriptionListLink', 'title', 'routePrefix'));
    }

}
