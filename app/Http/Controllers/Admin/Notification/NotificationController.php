<?php

namespace App\Http\Controllers\Admin\Notification;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use App\Notifications\AdminMessageNotification;
use App\Mail\AdminNotificationMailable;
use App\Services\MailService;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Gate;

class NotificationController extends Controller
{
    public const MANAGE_PERMISSION = 'manage connect hub';
    public const VIEW_PERMISSION = 'view connect hub';

    public function index()
    {
        if (!Gate::any([$this::MANAGE_PERMISSION])) {
            abort(403);
        }
        $notifications = \DB::table('notifications')->orderByDesc('created_at')->paginate(10);
        return view('admin.notifications.index', compact('notifications'));
    }


    public function create()
    {
        if (!Gate::any([$this::MANAGE_PERMISSION])) {
            abort(403);
        }

        $roles = Role::where('name', 'supplier')->get();
        $users = User::all();
        return view('admin.notifications.create', compact('roles', 'users'));
    }

    public function send(Request $request)
    {
        $request->validate([
            'subject' => 'required|string',
            'message' => 'required|string',
            'role' => 'required_without:users',
            'users' => 'required_without:role|array',
        ]);

        $usersToNotify = collect();

        if ($request->filled('role')) {
            try {
                $role = Role::findByName($request->role);
                $usersToNotify = $usersToNotify->merge($role->users);
            } catch (\Exception $e) {
                \Log::error('Role error: ' . $e->getMessage());
            }
        }

        if ($request->filled('users')) {
            $users = User::whereIn('id', $request->input('users'))->get();
            $usersToNotify = $usersToNotify->merge($users);
        }

        $usersToNotify = $usersToNotify->unique('id');
        $failed = [];

        foreach ($usersToNotify as $user) {
            try {
                // 1. Save to database using Laravel notification
                $user->notify(new AdminMessageNotification($request->message, $request->subject));

                // 2. Send via custom MailService
                $mailable = new AdminNotificationMailable($request->message, $request->subject);
                MailService::send($mailable, $user->email);

            } catch (\Exception $e) {
                \Log::error("Failed for {$user->email}: " . $e->getMessage());
                $failed[] = $user->email;
            }
        }

        if (count($failed)) {
            return redirect()->back()->withErrors([
                'notification' => 'Failed to send to: ' . implode(', ', $failed),
            ]);
        }

        return redirect()->back()->with('success', 'Notification sent and saved successfully.');
    }

    public function show($id)
    {
        if (!Gate::any([$this::VIEW_PERMISSION])) {
            abort(403);
        }

        $notification = DatabaseNotification::with('notifiable')->find($id);

        if (!$notification) {
            return redirect()->back()->withErrors(['notification' => 'Notification not found.']);
        }

        return view('admin.notifications.show', compact('notification'));
    }

    public function destroy($id)
    {
        if (!Gate::any([$this::MANAGE_PERMISSION])) {
            abort(403);
        }

        $notification = DatabaseNotification::find($id);

        if ($notification) {
            $notification->delete();
            return redirect()->back()->with('success', 'Notification deleted successfully.');
        }

        return redirect()->back()->withErrors(['notification' => 'Notification not found.']);

    }

}
