<?php

namespace Database\Factories;

use App\Models\CompanyAssessmentAnswer;
use App\Models\CompanyAssessment;
use App\Models\AssessmentQuestion;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyAssessmentAnswer>
 */
class CompanyAssessmentAnswerFactory extends Factory
{
    protected $model = CompanyAssessmentAnswer::class;

    public function definition(): array
    {
        return [
            'assessment_id' => CompanyAssessment::factory(),
            'question_id' => AssessmentQuestion::factory(),
            'answered_by' => User::factory(),
            'answer' => $this->faker->text(100),
        ];
    }
}
