<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyEndorsement extends Model
{
    public $timestamps = false;
    protected $fillable = [
        'endorser_company',
        'endorsed_company',
        'endorser_user',
        'remover_user',
        'status',
        'ip_address',
        'created_at',
        'removed_at'
    ];

    public function endorserCompany()
    {
        return $this->belongsTo(Company::class, 'endorser_company');
    }
}
