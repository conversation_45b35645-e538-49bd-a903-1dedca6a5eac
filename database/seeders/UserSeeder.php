<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $users = User::factory()->count(20)->create();
    
            $role = Role::findByName(User::ROLE_USER);
    
            foreach ($users as $user) {
                $user->assignRole($role);
            }
        });
    }
}
