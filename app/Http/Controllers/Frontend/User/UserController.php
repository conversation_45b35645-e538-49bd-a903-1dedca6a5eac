<?php

namespace App\Http\Controllers\Frontend\User;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     * 
     */
    public function switchAccount()
    {
        if(!Session::has('currentAccount')){
            Session::put('currentAccount', 'supplier');
            return Redirect::route('frontend.company.dashboard');
            
        }
        if(Session::get('currentAccount') === 'user'){
            Session::put('currentAccount', 'supplier'); 
            return Redirect::route('frontend.company.dashboard');
        }
        if(Session::get('currentAccount') === 'supplier'){
            Session::put('currentAccount', 'user');
            return Redirect::route('frontend.user-profile-setting');
        }
    }

  
}
