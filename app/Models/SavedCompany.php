<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use <PERSON><PERSON>\Scout\Searchable;

class SavedCompany extends Model
{
    use Searchable;

    public $table = 'saved_companies';

    protected $fillable = [
        'user_id',
        'company_id',
    ];

    /**
     * The attributes that should be searchable in the model.
     *
     * @var array<string>
     */
    public static array $searchableFields = [
        'name',
        'description',
        'website_url',
        'phone',
        'email',
    ];
    

    /**
     * Define the index name in Elasticsearch.
     */
    public function searchableAs()
    {
        return 'saved_suppliers_index';
    }

    /**
     * The attributes that are mass assignable.
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    /**
     * The attributes that are mass assignable.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function services()
    {
        return $this->hasManyThrough(
            Product::class,
            Company::class,
            'id',           // Company id (in companies table)
            'company_id',   // Company id (in products table)
            'company_id',   // FK in saved_companies table
            'id'            // PK in companies table
        )->where('type', 'service');
    }

    public function products()
    {
        return $this->hasManyThrough(
            Product::class,
            Company::class,
            'id',
            'company_id',
            'company_id',
            'id'
        )->where('type', 'product');
    }


    /**
     * Define the indexable data array for the model.
     */
    public function toSearchableArray()
    {
        $company = $this->company;
        return [
            'id' => $this->id,
            'user_id' => (int)$this->user_id,
            'company_id' => (int)$this->company_id,
            'name' => $company->name,
            'website_url' => $company->website_url,
            'phone' => $company->full_phone,
            'email' => $company->email,
            'country_ids' => $company->company_countries()->exists()
                ? implode(', ', $company->company_countries()->get()->pluck('country_id')->toArray())
                : null,
            'region_ids' => $company->company_regions()->exists()
                ? implode(', ', $company->company_regions()->get()->pluck('region_id')->toArray())
                : null,
            'categories' => $company->company_business_categories()->exists()
                ? implode(', ', $company->company_business_categories()->with('level_3_category')->get()->pluck('level_3_category.name')->filter()->toArray())
                : null,
            'filters' => $company->company_business_categories()->exists()
                ? implode(', ', $company->company_business_categories()->with('level_2_category')->get()->pluck('level_2_category.name')->filter()->toArray()) . ', ' .
                  implode(', ', $company->company_business_categories()->with('level_3_category')->get()->pluck('level_3_category.name')->filter()->toArray()) . ', ' .
                  implode(', ', $company->company_countries()->with('country')->get()->pluck('country.code')->toArray())
                : null
        ];
    }
}
