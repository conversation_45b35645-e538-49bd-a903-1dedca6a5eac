<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class AssessmentSection
 *
 * @property int $id
 * @property int|null $parent_id
 * @property string $title
 * @property string|null $description
 * @property bool $reason_required
 * @property bool $document_required
 */
class AssessmentSection extends Model
{
    protected $fillable = [
        'parent_id',
        'title',
        'description',
        'reason_required',
        'document_required',
    ];

    public function parent()
    {
        return $this->belongsTo(AssessmentSection::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(AssessmentSection::class, 'parent_id');
    }
}
