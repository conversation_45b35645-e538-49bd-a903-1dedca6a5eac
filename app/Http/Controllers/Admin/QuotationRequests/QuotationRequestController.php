<?php

namespace App\Http\Controllers\Admin\QuotationRequests;

use App\Http\Controllers\Admin\QuotationRequests\BaseQuotationRequestController;
use App\Http\Requests\Admin\UpdateUserRequest;
use App\Models\QuotationRequest;
use App\Models\User;
use App\Services\QuotationService;
use Illuminate\Support\Facades\Gate;

class QuotationRequestController extends BaseQuotationRequestController
{
    /**
     * Create a new controller instance.
     *  
     * @param QuotationService $service
     * @return void
     */
    public function __construct(QuotationService $service)
    {
        $role = User::ROLE_USER;
        $routePrefix = 'admin.quotations.';
        $title = 'Quotation Requests';
        
        parent::__construct($service, $role, $routePrefix, $title); 
    }


    public function show(QuotationRequest $quotation)
    {
        if (!Gate::any(['manage quotations', 'view quotations'])) {
            abort(403);
        }

        return $this->parseShow($quotation);
    }

    
}
