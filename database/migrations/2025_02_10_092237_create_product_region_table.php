<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('product_region', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('product_id'); // Foreign key to products table
            $table->unsignedBigInteger('country_id'); // Foreign key to countries table
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->foreign('country_id')->references('id')->on('countries')->onDelete('cascade');

            // Unique constraint to prevent duplicate entries
            $table->unique(['product_id', 'country_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_region');
    }
};
