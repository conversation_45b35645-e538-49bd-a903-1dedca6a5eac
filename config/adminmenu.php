<?php

return [
    [
        'text' => 'Dashboard',
        'route' => 'admin.dashboard',
        'icon' => 'fas fa-tachometer-alt',
    ],
    [
        'text' => 'Global Settings',
        'route' => 'admin.settings.index',
        'icon' => 'fas fa-cogs',
        'permissions' => [
            'manage settings',
        ],
    ],
    [
        'text' => 'Product Categories',
        'route' => 'admin.product-categories',
        'icon' => 'fas fa-tags',
        'permissions' => [
            'manage categories',
            'view categories',
        ],
        'submenu' => [
            [
                'text' => 'List(Table View)',
                'route' => 'admin.product-categories.index',
                'icon' => 'fas fa-list',
                'permissions' => [
                    'manage categories',
                    'view categories',
                ],
            ],
            [
                'text' => 'List(Nested View)',
                'route' => 'admin.product-categories.nested',
                'icon' => 'fas fa-list-alt',
                'permissions' => [
                    'manage categories',
                    'view categories',
                ],
            ],
            [
                'text' => 'Add',
                'route' => 'admin.product-categories.create',
                'icon' => 'fas fa-plus',
                'permissions' => [
                    'manage categories',
                ],
            ],
            [
                'text' => 'Import',
                'route' => 'admin.product-categories.import',
                'icon' => 'fas fa-file-upload',
                'permissions' => [
                    'manage categories',
                ],
            ],
        ],
    ],
    [
        'text' => 'Service Categories',
        'route' => 'admin.service-categories',
        'icon' => 'fas fa-tags',
        'permissions' => [
            'manage categories',
            'view categories',
        ],
        'submenu' => [
            [
                'text' => 'List(Table View)',
                'route' => 'admin.service-categories.index',
                'icon' => 'fas fa-list',
                'permissions' => [
                    'manage categories',
                    'view categories',
                ],
            ],
            [
                'text' => 'List(Nested View)',
                'route' => 'admin.service-categories.nested',
                'icon' => 'fas fa-list-alt',
                'permissions' => [
                    'manage categories',
                    'view categories',
                ],
            ],
            [
                'text' => 'Add',
                'route' => 'admin.service-categories.create',
                'icon' => 'fas fa-plus',
                'permissions' => [
                    'manage categories',
                ],
            ],
            [
                'text' => 'Import',
                'route' => 'admin.service-categories.import',
                'icon' => 'fas fa-file-upload',
                'permissions' => [
                    'manage categories',
                ],
            ],
        ],
    ],
    [
        'text' => 'Manage Users',
        'route' => 'admin.users',
        'icon' => 'fas fa-users',
        'permissions' => [
            'manage users',
            'view users',
        ],
        'submenu' => [
            [
                'text' => 'Active Users',
                'route' => 'admin.users.index',
                'param' => [
                    'status' => \App\Models\User::STATUS_ACTIVE,
                ],
                'icon' => 'fas fa-user-check',
                'permissions' => [
                    'manage users',
                    'view users',
                ],
            ],
        ],
    ],
    [
        'text' => 'Manage Suppliers',
        'route' => 'admin.suppliers',
        'icon' => 'fas fa-industry',
        'permissions' => [
            'manage suppliers requests',
            'manage suppliers',
            'view suppliers',
        ],
        'submenu' => [
            [
                'text' => 'Comp. Registrations',
                'route' => 'admin.suppliers.requests.index',
                // 'param' => [
                //     'status' => \App\Models\Company::STATUS_PENDING,
                // ],
                'icon' => 'fas fa-envelope-open-text',
                'permissions' => [
                    'manage suppliers requests',
                ],
            ],
            // [
            //     'text' => 'Comp. Assessments',
            //     'route' => 'admin.suppliers.requests.index',
            //     // 'param' => [
            //     //     'status' => \App\Models\Company::STATUS_PENDING,
            //     // ],
            //     'icon' => 'fas fa-check-circle',
            //     'permissions' => [
            //         'manage suppliers requests',
            //     ],
            // ],
            [
                'text' => 'Company Listing',
                'route' => 'admin.suppliers.index',
                'icon' => 'fas fa-list-alt',
                'permissions' => [
                    'manage suppliers',
                    'view suppliers',
                ],
            ],

        ],
    ],
    [
        'text' => 'Products & Services',
        'route' => 'admin.products',
        'icon' => 'fas fa-credit-card',
        'permissions' => [
            'manage products',  'view products'
        ],
        'submenu' => [
            [
                'text' => 'Product Listing',
                'route' => 'admin.products.index',
                'param' => ['type' => 'product'],
                'icon' => 'fas fa-shopping-cart',
                'permissions' => ['manage products', 'view products'],
            ],
            [
                'text' => 'Service Listing',
                'route' => 'admin.products.index',
                'param' => ['type' => 'service'],
                'icon' => 'fas fa-wrench',
                'permissions' => ['manage products', 'view products'],
            ],
        ],
    ],
    // [
    //     'text' => 'Manage Quotations',
    //     'route' => 'admin.quotations',
    //     'icon' => 'fas fa-credit-card',
    //     'permissions' => [
    //         'manage quotations'
    //     ],
    //     'submenu' => [
    //         [
    //             'text' => 'Quotation Requests',
    //             'route' => 'admin.quotations.index',
    //             'icon' => 'fas fa-file-invoice',
    //             'permissions' => [
    //                 'manage quotations',
    //                 'view quotations',
    //             ],
    //         ],
    //     ],
    // ],
    [
        'text' => 'Subscriptions',
        'route' => 'admin.subscriptions',
        'icon' => 'fas fa-credit-card',
        'permissions' => [
            'manage subscriptions',
            'manage plans',
        ],
        'submenu' => [
            [
                'text' => 'Stripe Subscriptions',
                'route' => 'admin.plans.list',
                'icon' => 'fas fa-box',
                'permissions' => [
                    'manage plans',
                ],
            ],
            [
                'text' => 'Plan Features',
                'route' => 'admin.plan-features.index',
                'icon' => 'fas fa-layer-group',
                'permissions' => [
                    'manage plan features',
                ],
            ],
            [
                'text' => 'Assigned Features',
                'route' => 'admin.subscription-features.index',
                'icon' => 'fas fa-puzzle-piece',
                'permissions' => [
                    'manage subscriptions features',
                ],
            ],
            [
                'text' => 'Company Subscription',
                'route' => 'admin.subscriptions.index',
                'icon' => 'fas fa-sync-alt',
                'permissions' => [
                    'manage subscriptions',
                ],
            ]
        ],
    ],

    [
        'text' => 'Role & Responsibility',
        'route' => 'admin.security',
        'icon' => 'fas fa-user-shield',
        'permissions' => [
            'manage roles permissions',
        ],
        'submenu' => [
            [
                'text' => 'Admin Users',
                'route' => 'admin.security.admin_users.index',
                'icon' => 'fas fa-user-check',
                'permissions' => [
                    'manage roles permissions',
                ],
            ],
        ],
    ],
    [
        'text' => 'Content Management',
        'route' => 'admin.contents',
        'icon' => 'fas fa-file-code',
        'permissions' => [
            'manage contents',
        ],
        'submenu' => [
            [
                'text' => 'Pages',
                'route' => 'admin.contents.pages.index',
                'icon' => 'fas fa-file-alt',
                'permissions' => [
                    'manage contents',
                ],
            ],
            [
                'text' => 'How To Hub',
                'route' => 'admin.contents.howtohubs.index',
                'icon' => 'fas fa-file-alt',
                'permissions' => [
                    'manage contents',
                ],
            ],
        ],
    ],
    [
        'text' => 'Connect Hub',
        'route' => 'admin.connecthub',
        'icon' => 'fas fa-comments',
        'permissions' => [
            'manage connect hub',
        ],
        'submenu' => [
            [
               'text' => 'Chat',
                'route' => 'admin.connecthub.chat.index',
                'icon' => 'fas fa-comments',
                'permissions' => [
                    'manage connect hub',
                ],
            ],
             [
                'text' => 'My Inbox',
                'route' => 'admin.connecthub.inbox.mine',
                'icon' => 'fas fa-user-check',
                'permissions' => [
                    'manage connect hub',
                ],
            ],
            [
                'text' => 'Inbox (All)',
                'route' => 'admin.connecthub.inbox.index',
                'icon' => 'fas fa-inbox',
                'permissions' => [
                    'manage connect hub',
                ],
            ],
           
            [
                'text' => 'Sent Notifications',
                'route' => 'admin.connecthub.notifications.sent',
                'icon' => 'fas fa-paper-plane',
                'permissions' => [
                    'manage connect hub',
                ],
            ],
            [
                'text' => 'Create Notification',
                'route' => 'admin.connecthub.notifications.create',
                'icon' => 'fas fa-plus-circle',
                'permissions' => [
                    'manage connect hub',
                ],
            ],
            [
                'text' => 'Assignees',
                'route' => 'admin.connecthub.assignees.index',
                'icon' => 'fas fa-user-cog',
                'permissions' => [
                    'manage connect hub',
                ],
            ],
        ],
    ],
    //
    [
        'text' => 'My Profile',
        'route' => 'admin.profile.edit',
        'icon' => 'fas fa-user-circle',
        'permissions' => [
            'manage profile',
        ],
    ],
];
