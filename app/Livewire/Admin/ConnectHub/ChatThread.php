<?php

namespace App\Livewire\Admin\ConnectHub;

use App\Events\UserToSiteAdminChatEvent;
use Livewire\Component;
use App\Models\AdminConversation;
use App\Models\AdminMessage;
use Illuminate\Support\Facades\Auth;


class ChatThread extends Component
{
    public AdminConversation $conversation;
    public $message;

    protected $listeners = ['messageReceived' => '$refresh'];

    protected $rules = [
        'message' => 'required|string|max:2000',
    ];

    public function send()
    {
        $this->validate([
            'message' => 'required|string|max:5000',
        ]);

        $message = $this->conversation->messages()->create([
            'sender_id' => auth()->id(),
            'message' => $this->message,
        ]);

        $this->conversation->update([
            'status' => 'in_progress',
            'updated_at' => now(),
        ]);

        $this->message = '';
        ///$this->conversation->refresh();

        $this->dispatch('chat-scroll-bottom');

       broadcast(new UserToSiteAdminChatEvent($message))->toOthers();
    }


    public function render()
    {
        $this->conversation->load('messages.sender');

        return view('livewire.admin.connect-hub.chat-thread');
        
    }
}

