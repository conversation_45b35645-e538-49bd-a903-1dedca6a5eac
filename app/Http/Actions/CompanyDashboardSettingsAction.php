<?php

namespace App\Http\Actions;

use App\Models\Company;
use App\Models\Country;
use App\Models\CaseStudy;
use App\Models\GeoRegion;
use App\Models\GeoCountry;
use App\Models\CompanyAward;
use App\Models\GeoContinent;
use App\Models\CompanyLocation;
use App\Models\CompanyIsoCompliance;
use Illuminate\Support\Facades\Session;
use App\Models\CompanyProductCertification;
use App\Models\CompanyPersonnelQualification;

class CompanyDashboardSettingsAction
{


     /**
     * Handles the registration of a new user.
     *
     * @param array $data The user data for registration.
     * @return void
     */
    public function __invoke($request, array $data)
    {
         \DB::transaction(function () use ($request, $data) {
            $company = auth()->user()->company;
            //Saving level 2 and level 3 categories
            if(isset($data['level_2_category']) && isset($data['level_3_category'])){
                $company->company_business_categories()->delete();
                for($i=0;$i<count($data['level_2_category']);$i++){
                    $level3Categories = [];
                    foreach ($data['level_3_category'] as $category) {
                        if (str_starts_with($category, $data['level_2_category'][$i] . '-')) {
                            // Extract the part after the hyphen
                            $parts = explode('-', $category);
                            if (isset($parts[1])) {
                                $level3Categories[] = $parts[1];
                            }
                        }
                    }
                    if(!empty($level3Categories)){
                        for($j=0;$j<count($level3Categories);$j++){
                            $company->company_business_categories()->create([
                                'company_id' => $company->id,
                                'level_2_category_id' => $data['level_2_category'][$i],
                                'level_3_category_id' => $level3Categories[$j]
                            ]);
                        }
                    }
                }
            }
            //Saving continents, countries and regions
            if(isset($data['continents'])){
                $company->company_continents()->delete();
                $company->company_countries()->delete();
                $company->company_regions()->delete();
                for($i=0;$i<count($data['continents']);$i++){
                    $companyContinent = $company->company_continents()->create([
                        'company_id' => $company->id,
                        'continent_id' => $data['continents'][$i]
                    ]);
                    GeoCountry::where('continent_id', $data['continents'][$i])->get()->each(function($country) use ($company, $companyContinent){
                        $companyCountry = $company->company_countries()->create([
                            'company_id' => $company->id,
                            'country_id' => $country->id,
                            'company_continent_id' => $companyContinent->id
                        ]);
                        GeoRegion::where('country_id', $country->id)->get()->each(function($region) use ($company,$companyCountry){
                            $company->company_regions()->create([
                                'company_id' => $company->id,
                                'region_id' => $region->id,
                                'company_country_id' => $companyCountry->id
                            ]);
                        });
                    });
                }
            }
            if(isset($data['countries'])){
                $company->company_continents()->delete();
                $company->company_countries()->delete();
                $company->company_regions()->delete();
                $insertedContinents = [];
                for($j=0;$j<count($data['countries']);$j++){
                    $country = GeoCountry::find($data['countries'][$j]);
                     // Ensure continent is inserted only once
                     if (!isset($insertedContinents[$country->continent_id])) {
                        $companyContinent = $company->company_continents()->create([
                            'company_id' => $company->id,
                            'continent_id' => $country->continent_id
                        ]);
                        $insertedContinents[$country->continent_id] = $companyContinent->id;
                    }
                    $companyCountry = $company->company_countries()->create([
                        'company_id' => $company->id,
                        'country_id' => $data['countries'][$j],
                        'company_continent_id' => $insertedContinents[$country->continent_id]

                    ]);

                    GeoRegion::where('country_id', $data['countries'][$j])->get()->each(function($region) use ($company, $companyCountry){
                        $company->company_regions()->create([
                            'company_id' => $company->id,
                            'region_id' => $region->id,
                            'company_country_id' => $companyCountry->id
                        ]);
                    });
                }
                
            }
            if (isset($data['regions'])) {
                // Delete old data
                $company->company_continents()->delete();
                $company->company_countries()->delete();
                $company->company_regions()->delete();
            
                // Arrays to track inserted continents and countries
                $insertedContinents = [];
                $insertedCountries = [];
            
                foreach ($data['regions'] as $regionId) {
                    $region = GeoRegion::find($regionId);
            
                    if (!$region) {
                        continue; // Skip if region is not found
                    }
            
                    $country = GeoCountry::find($region->country_id);
            
                    if (!$country) {
                        continue; // Skip if country is not found
                    }
            
                    // Ensure continent is inserted only once
                    if (!isset($insertedContinents[$country->continent_id])) {
                        $companyContinent = $company->company_continents()->create([
                            'company_id' => $company->id,
                            'continent_id' => $country->continent_id
                        ]);
                        $insertedContinents[$country->continent_id] = $companyContinent->id;
                    }
            
                    // Ensure country is inserted only once
                    if (!isset($insertedCountries[$region->country_id])) {
                        $companyCountry = $company->company_countries()->create([
                            'company_id' => $company->id,
                            'country_id' => $region->country_id,
                            'company_continent_id' => $insertedContinents[$country->continent_id] // Link to continent
                        ]);
                        $insertedCountries[$region->country_id] = $companyCountry->id;
                    }
            
                    // Insert region
                    $company->company_regions()->create([
                        'company_id' => $company->id,
                        'region_id' => $regionId,
                        'company_country_id' => $insertedCountries[$region->country_id] // Link to country
                    ]);
                }
            }
            
            // Updating company logo
            if($request->hasFile('company_org_logo') && $request->hasFile('company_crp_logo')){
                $company->addMediaFromRequest('company_org_logo')
                    ->toMediaCollection('company_org_logo', 'public'); 
                $company->addMediaFromRequest('company_crp_logo')
                    ->toMediaCollection('company_crp_logo', 'public'); 
                     
            }
            //Updating cover image 
            if($request->hasFile('company_org_cov_image') && $request->hasFile('company_crp_cov_mage')){
                $company->addMediaFromRequest('company_org_cov_image')
                    ->toMediaCollection('company_org_cov_image', 'public'); 
                $company->addMediaFromRequest('company_crp_cov_mage')
                    ->toMediaCollection('company_crp_cov_mage', 'public'); 
                     
            }
            //Updating cover image 
            if($request->hasFile('company_org_adv_image') && $request->hasFile('company_crp_adv_image')){
                $company->addMediaFromRequest('company_org_adv_image')
                    ->toMediaCollection('company_org_adv_image', 'public'); 
                $company->addMediaFromRequest('company_crp_adv_image')
                    ->toMediaCollection('company_crp_adv_image', 'public'); 
                     
            }
            //Updating company awards
            if(isset($data['award_id']) && $data['award_id']){
                $orgAwardImages = $request->file('org_award_image', []);
                $crpAwardImages = $request->file('crp_award_image', []);
                for($i=0;$i<count($data['award_id']);$i++){
                    if(!isset($orgAwardImages[$i]) && !isset($crpAwardImages[$i])){
                        array_unshift($orgAwardImages, null);
                        array_unshift($crpAwardImages, null);
                    }
                    if($data['award_id'][$i]){
                            CompanyAward::where('id', $data['award_id'][$i])->update([
                                'title' => $data['award_title'][$i],
                                'year' => $data['award_year'][$i],
                                'description' => $data['award_description'][$i]
                            ]);
                    }else{
                        $company_award = CompanyAward::create([
                            'company_id' => $company->id,
                            'title' => $data['award_title'][$i],
                            'year' => $data['award_year'][$i],
                            'description' => $data['award_description'][$i]
                        ]);
                        $company_award->addMedia($orgAwardImages[$i])
                                ->toMediaCollection('org_award_image', 'public');
                        $company_award->addMedia($crpAwardImages[$i])
                                ->toMediaCollection('crp_award_image', 'public');
                    }
                }
                
            }
            //Updating company iso compliance
            if(isset($data['iso_standard_code'])){
                for($i=0;$i<count($data['iso_standard_code']);$i++){
                    if($data['iso_standard_code'][$i]){
                        if(isset($data['company_iso_compliance_id']) && $data['company_iso_compliance_id'][$i]){
                            CompanyIsoCompliance::where('id', $data['company_iso_compliance_id'][$i])->update([
                                'code' => $data['iso_standard_code'][$i],
                                'title' => $data['standard_title'][$i],
                                'certificate_number' => $data['certification_number'][$i],
                                'notes' => $data['notes'][$i]
                            ]);
                        }else{
                            CompanyIsoCompliance::create([
                                'company_id' => $company->id,
                                'code' => $data['iso_standard_code'][$i],
                                'title' => $data['standard_title'][$i],
                                'certificate_number' => $data['certification_number'][$i],
                                'notes' => $data['notes'][$i]
                            ]);
                        }
                    }
                } 
            }
            //Updating company personal qualifications
            if(isset($data['qualification_title'])){
                for($i=0;$i<count($data['qualification_title']);$i++){
                    if($data['qualification_title'][$i]){
                        if(isset($data['company_qualification_id']) && $data['company_qualification_id'][$i]){
                            CompanyPersonnelQualification::where('id', $data['company_qualification_id'][$i])->update([
                                'company_id' => $company->id,
                                'title' => $data['qualification_title'][$i],
                                'notes' => $data['qualification_note'][$i]
                            ]);
                        }else{
                            CompanyPersonnelQualification::create([
                                'company_id' => $company->id,
                                'title' => $data['qualification_title'][$i],
                                'notes' => $data['qualification_note'][$i]
                            ]);
                        }
                    }
                }
            }
            //Updating company product certifications
            if(isset($data['certificate_title'])){
                for($i=0;$i<count($data['certificate_title']);$i++){
                    if($data['certificate_title'][$i]){
                        if(isset($data['company_certificate_id']) && $data['company_certificate_id'][$i]){
                            CompanyProductCertification::where('id', $data['company_certificate_id'][$i])->update([
                                'company_id' => $company->id,
                                'title' => $data['certificate_title'][$i],
                                'body' => $data['certification_body'][$i],
                                'number' => $data['certificate_number'][$i],
                                'notes' => $data['certificate_note'][$i]
                            ]);
                        }else{
                            CompanyProductCertification::create([
                                'company_id' => $company->id,
                                'title' => $data['certificate_title'][$i],
                                'body' => $data['certification_body'][$i],
                                'number' => $data['certificate_number'][$i],
                                'notes' => $data['certificate_note'][$i]
                            ]);
                        }
                    }
                }
            }
            //Updating company details
            $phonePrefix = $data['phone_prefix'] ? explode('@', $data['phone_prefix']) : '';
            return $company->update([
                'registered_location' => $data['registered_location'],
                'organisation_type_id' => $data['organisation_type'],
                'organisation_size_id' => $data['organisation_size'],
                'allow_email_forwarding' => isset($data['allow_email_forwarding']) ? $data['allow_email_forwarding'] : 0,
                'phone' => $data['phone_number'],
                'phone_prefix' => $data['phone_prefix'] ? $phonePrefix[1] : '',
                'year_founded' => $data['year_founded'],
                'description' => $data['about_the_company'],
                'website_url' => $data['company_website_url'],
                'workplace_verification' => isset($data['workplace_verification']) ? $data['workplace_verification'] : 1,
                'display_phone' => isset($data['display_phone']) ? $data['display_phone'] : 1,
                'phone_contact_title' => isset($data['phone_contact_title']) ? $data['phone_contact_title'] : '',
                'notification_access_ids' => isset($data['notification_access_ids']) ? json_encode($data['notification_access_ids']) : null,
                'chat_contact_title' => isset($data['company_chat_contact_title']) ? $data['company_chat_contact_title'] : '',
                'chat_contact_user_id' => $data['company_chat'],
                'advertising_from' => $data['duration_from'],
                'advertising_to' => $data['duration_to'],
                'compliance_summary' => $data['compiliance_summary']
            ]);
        });
    }

     /**
     * Handles the saving location.
     *
     * @param array $data The location data
     * @return void
     */
    public function saveCompanyLocation(array $data)
    {
        $companyLocation = \DB::transaction(function () use ($data) {
            $phonePrefix = $data['phone_prefix'] ? explode('@', $data['phone_prefix']) : '';
            $companyId = auth()->user()->company->id;
            //Fetching country_id by code
            $country = GeoCountry::where('code', $data['country_code'])->first();
            $pointWKT = "POINT({$data['latitude']} {$data['longitude']})";
            //Fetching region_id by country_id and point
            $region = \DB::table('geo_regions as r')
            ->join('geo_countries as c', 'r.country_id', '=', 'c.id')
            ->select('r.id as region_id')
            ->where('c.id', $country->id)
            ->whereRaw("ST_Contains(r.shape, ST_GeomFromText(?, 4326))", [$pointWKT])
            ->first();
            if(!empty($data['location_id'])){
                $isMain = $data['location_type'] == 1 ? 1 : 0;
                if($isMain == 1){
                   CompanyLocation::where(['company_id' => $companyId, 'is_main' => 1])->update([
                       'is_main' => 0,
                       'location_type_id' => 2
                   ]);
                   
                }
               $city = explode(',', $data['city'])[0];
               return CompanyLocation::where('id', $data['location_id'])->update([
                   'company_id' => $companyId,
                   'location_type_id' => $data['location_type'],
                   'city' => $city,
                   'geo_country_id' => $country->id,
                   'geo_region_id' => $region->region_id,
                   'address_line_1' => $data['address_line_1'],
                   'address_line_2' => $data['address_line_2'],
                   'postcode' => $data['postal_code'],
                   'latitude' => $data['latitude'] ?? null ,
                   'longitude' => $data['longitude'] ?? null,
                   'email' => $data['location_email'],
                   'phone' => $data['number'],
                   'phone_prefix' => $phonePrefix ? $phonePrefix[1] : '',
                   'is_main' => $data['location_type'] == 1 ? 1 : 0
               ]); 
            }else{
                $city = explode(',', $data['city'])[0];
                return CompanyLocation::create([
                    'company_id' => $companyId,
                    'location_type_id' => $data['location_type'],
                    'city' => $city,
                    'geo_country_id' => $country->id,
                    'geo_region_id' => $region->region_id,
                    'address_line_1' => $data['address_line_1'],
                    'address_line_2' => $data['address_line_2'],
                    'postcode' => $data['postal_code'],
                    'latitude' => $data['latitude'] ?? null ,
                    'longitude' => $data['longitude'] ?? null,
                    'email' => $data['location_email'],
                    'phone' => $data['number'],
                    'phone_prefix' => $phonePrefix ? $phonePrefix[1] : '',
                    'is_main' => $data['location_type'] == 1 ? 1 : 0
                ]); 
            }
        
        });
        return $companyLocation;
    }

    /**
     * Handles the company email update.
     *
     * @param array $data The user data for company email update.
     * @return void
     */
    public function companyEmailUpdate(array $data)
    {
         \DB::transaction(function () use ($data) {
            $user = auth()->user();
            $company = $user->company;
            Session::put('verifyType', 'companyEmailUpdate');
            $company->company_contact_history()->create([
                'company_id' => $company->id,
                'contact_type' => 'email',
                'old_value' => $company->email,
                'new_value' => $data['email'],
                'status' => 'pending',
                'user_ip_address' => request()->ip(),
                'requested_at' => now()
            ]);
            $user->sendEmailVerification($data['email']);
            return $user;
        });
    }

    public function saveCompanyCaseStudy($request, array $data)
    {
        \DB::transaction(function () use ($request, $data) {
            $company = auth()->user()->company;
            //Fetching country_id by code
            $country = $data['city'] && $data['country_code'] ? GeoCountry::where('code', $data['country_code'])->first() : null;
            if(!empty($data['case_study_id'])){
                $caseStudy = CaseStudy::find($data['case_study_id']);
                $caseStudy->update([
                    'title' => $data['case_study_title'],
                    'date' => $data['date'],
                    'client' => $data['client_name'],
                    'country_id' => $country ? $country->id : null,
                    'city' => $data['city'] ? explode(',', $data['city'])[0] : null,
                    'story' => $data['case_study_story']
                ]);
                if ($request->hasFile('org_case_study_image') && $request->hasFile('crp_case_study_image')) {
                    $orgImages = $request->file('org_case_study_image');
                    $crpImage = $request->file('crp_case_study_image');
                    $caseStudy->addMedia($crpImage)
                                      ->toMediaCollection('crp_case_study_image', 'public');
                    for ($i = 0; $i < count($orgImages); $i++) {
                        // Attach original image
                        $caseStudy->addMedia($orgImages[$i])
                                  ->toMediaCollection('org_case_study_image', 'public');
                
                        // Attach cropped image only if it exists at this index
                        // if (isset($crpImages[$i])) {
                        //     $caseStudy->addMedia($crpImages[$i])
                        //               ->toMediaCollection('crp_case_study_image', 'public');
                        // }
                    }
                }
            }else{
                $caseStudy = $company->company_case_studies()->create([
                    'company_id' => $company->id,
                    'title' => $data['case_study_title'],
                    'date' => $data['date'],
                    'client' => $data['client_name'],
                    'country_id' => $country ? $country->id : null,
                    'city' => $data['city'] ? explode(',', $data['city'])[0] : null,
                    'story' => $data['case_study_story']
                ]);
               
                if ($request->hasFile('org_case_study_image') && $request->hasFile('crp_case_study_image')) {
                    $orgImages = $request->file('org_case_study_image');
                    $crpImage = $request->file('crp_case_study_image');
                    $caseStudy->addMedia($crpImage)
                                      ->toMediaCollection('crp_case_study_image', 'public');
                    for ($i = 0; $i < count($orgImages); $i++) {
                        // Attach original image
                        $caseStudy->addMedia($orgImages[$i])
                                  ->toMediaCollection('org_case_study_image', 'public');
                
                        // Attach cropped image only if it exists at this index
                        // if (isset($crpImages[$i])) {
                        //     $caseStudy->addMedia($crpImages[$i])
                        //               ->toMediaCollection('crp_case_study_image', 'public');
                        // }
                    }
                }
            }
        });
        
    }
}
