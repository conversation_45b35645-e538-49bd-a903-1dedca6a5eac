<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckFrontendPermission
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, $permission): Response
    {
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Unauthorized');
        }

        // Deny access if the user is an admin
        if ($user->roles()->where('type', 'admin')->exists()) {
            abort(403, 'Admins are not allowed to access this area');
        }

        // Allow if user belongs to a company directly
        if ($user->company) {
            return $next($request);
        }

        // Check if user has the required supplier permission
        $hasPermission = $user->permissions()
            ->where([
                ['name', '=', $permission],
                ['type', '=', 'supplier'],
            ])
            ->exists();

        if (!$hasPermission) {
            abort(403, 'Permission denied');
        }

        return $next($request);
    }
}
