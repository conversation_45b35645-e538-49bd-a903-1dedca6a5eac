<?php

namespace App\Http\Controllers\Auth;

use App\Http\Actions\CreateUserProfileAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\UserBasicDetailsRequest;
use App\Http\Requests\Auth\UserOccupationDetailsRequest;
use App\Http\Requests\Auth\UserPhoneNumberRequest;
use App\Models\Company;
use App\Models\User;
use App\Services\TwilioOtpService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Exception;

class CreateUserProfileController extends Controller
{
    protected $userProfile;
    protected $user;

    /**
     * CreateUserProfileController constructor.
     *
     * @param CreateUserProfileAction $userProfile
     */
    public function __construct(CreateUserProfileAction $userProfile, Protected TwilioOtpService $otpService)
    {
        $this->userProfile = $userProfile;
        $this->otpService = $otpService;
        $user = User::find(Session::get('registeredUserId'));
        $this->user = $user;
    }

    /**
     * Handle the user's basic details submission.
     *
     * @param UserBasicDetailsRequest $request
     * @return RedirectResponse
     */
    public function storeBasicDetails(UserBasicDetailsRequest $request): RedirectResponse
    {
        $data = $request->validated();
        try{
            $this->userProfile->executeBasicDetails($data, $this->user);

            return \Redirect::back()
                ->with('modalToShow', $request->nextModalToDisplay());

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.user_basic_details'));
        }
        
    }

    /**
     * Back to step 2
     */
    public function backToBasicDetails(): RedirectResponse
    {

        return \Redirect::back()
            ->with('modalToShow', config('settings.modal.user_basic_details'));
    }

    /**
     * Company Search
     */
    public function companySearch(Request $request){
        $query = $request->input('query');
        $companies = Company::where('name', 'like', "%{$query}%")
            ->select('id', 'name') // Select only required fields
            ->limit(10) // Limit results for performance
            ->get();
        return response()->json($companies);
    }


    /**
     * Handle the submission of occupation details.
     *
     * @param UserOccupationDetailsRequest $request
     * @return RedirectResponse
     */
    public function storeOccupationDetails(UserOccupationDetailsRequest $request): RedirectResponse
    {
        try{
            
            $data = $request->validated();
            $this->userProfile->executeOccupationDetails($data, $this->user);

            return \Redirect::back()
                ->with('modalToShow', $request->nextModalToDisplay());

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.occupation_details'));
        }
        
    }

    /**
     * Back to step 3
     *
     *
     */
    public function backToOccupationDetails(): RedirectResponse
    {

        return \Redirect::back()
            ->with('modalToShow', config('settings.modal.occupation_details'));
    }


    /**
     * Handle the submission of phone number.
     *
     * @param UserPhoneNumberRequest $request
     * @return RedirectResponse
     */
    public function storePhoneNumber(UserPhoneNumberRequest $request): RedirectResponse
    {
        try{

            $data = $request->validated();
            $phonePrefix = explode('@', $request->phone_prefix);
            $data['phone_prefix'] = $phonePrefix[1];
            $this->userProfile->executePhoneNumber($data, $this->user);
            $this->otpService->sendOtp($phonePrefix[1].$request->number);
            return \Redirect::back()
            ->with('modalToShow', $request->nextModalToDisplay());

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.phone_number_prompt'));
        }

        
    }

    public function backToPhoneNumber() : RedirectResponse {
        return \Redirect::back()
            ->with('modalToShow', config('settings.modal.phone_number_prompt'));
    }
}
