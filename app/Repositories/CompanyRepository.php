<?php

namespace App\Repositories;

use App\Models\Company;
use App\Models\CompanyRegistrationStatus;

/**
 * Class CompanyRepository
 *
 * Handles data operations for the Company model.
 */
class CompanyRepository extends BaseRepository
{
    /**
     * The Company model instance.
     *
     * @var \App\Models\Company
     */
    protected $model;

    /**
     * CompanyRepository constructor.
     *
     * @param \App\Models\Company $company
     */
    public function __construct(Company $company)
    {
        $this->model = $company;
    }


    /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {
        return [
            'Pending' => $this->model::STATUS_PENDING,
            'Active' => $this->model::STATUS_ACTIVE,
            'Approve' => $this->model::STATUS_APPROVED,
            'Reject' => $this->model::STATUS_REJECTED,
        ];
    }


    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByMixedCondition(array $params)
    {
        return $this->model
            ->with(['supplier', 'registrationStatus'])
            ->whereHas('registrationStatus', function ($subQuery) use ($params) {
                $subQuery->where('status', '!=', CompanyRegistrationStatus::STATUS_PENDING);

                if (!empty($params['status'])) {
                    $subQuery->status($params['status']);
                }
            })
            ->when($params['id'], function () use ($params) {
                return $this->model->where('id', $params['id']);
            })
            ->whereHas('supplier')
            ->orderByDesc('updated_at')
            ->get();
    }

}
