<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organisation_types_local', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organisation_type_id')->constrained('organisation_types')->onDelete('cascade');
            $table->string('iso_code', 2);
            $table->string('local_code', 255);
            $table->string('local_name', 255);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('table_organisation_types_local');
    }
};
