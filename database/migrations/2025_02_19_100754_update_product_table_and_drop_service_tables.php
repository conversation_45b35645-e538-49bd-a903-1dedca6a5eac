<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        // List of tables to drop
        $tablesToDrop = [
            'services',
            'category_service',
            'key_feature_service',
            'service_region',
            'service_pricings',
            'service_attributes',
            'service_certifications',
            'service_qualifications'
        ];

        foreach ($tablesToDrop as $table) {
            Schema::disableForeignKeyConstraints(); // Temporarily disable foreign key constraints
            Schema::dropIfExists($table);
            Schema::enableForeignKeyConstraints();
        }

        // Add 'type' column to products table
        Schema::table('products', function (Blueprint $table) {
            $table->enum('type', ['product', 'service'])->default('product')->after('remove_listing');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
