<?php

namespace App\Repositories;

use App\Models\CompanyPersonnelQualification;

/**
 * Class CompanyPersonnelQualificationRepository
 *
 * Handles data operations for the CompanyPersonnelQualification model.
 */
class CompanyPersonnelQualificationRepository extends BaseRepository
{
    /**
     * The CompanyPersonnelQualification model instance.
     *
     * @var \App\Models\CompanyPersonnelQualification
     */
    protected $model;

    /**
     * CompanyPersonnelQualificationRepository constructor.
     *
     * @param \App\Models\CompanyPersonnelQualification $companyPersonnelQualification
     */
    public function __construct(CompanyPersonnelQualification $companyPersonnelQualification)
    {
        $this->model = $companyPersonnelQualification;
    }
}
