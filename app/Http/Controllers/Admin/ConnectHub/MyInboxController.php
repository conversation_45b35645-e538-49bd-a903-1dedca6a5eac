<?php

namespace App\Http\Controllers\Admin\ConnectHub;

use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use App\Models\AdminNotificationReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class MyInboxController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        return view('admin.connecthub.inbox.my');
    }

    protected function list(Request $request)
    {
        $notifications = AdminNotification::with(['user', 'replies'])
            ->where('assigned_to', Auth::id())
            ->orderBy('created_at', 'desc');

        return DataTables::of($notifications)
            ->addIndexColumn()
            ->addColumn('user', function ($notification) {
                return $notification->user->name . '<br><small>' . $notification->user->email . '</small>';
            })
            ->addColumn('company', function ($notification) {
                return $notification->user->company->name ?? '-';
            })
            ->addColumn('status', function ($notification) {
                return '<span class="badge bg-' . match ($notification->status) {
                    'resolved' => 'success',
                    'in_progress' => 'warning',
                    default => 'secondary',
                } . '">' . ucfirst($notification->status) . '</span>';
            })
            ->addColumn('last_message', function ($notification) {
                return optional($notification->replies->last())->message ?? \Str::limit($notification->message, 50);
            })
            ->addColumn('updated_at', function ($notification) {
                return $notification->updated_at->format('d M Y, h:i A');
            })
            ->addColumn('action', function ($notification) {
                return '<a href="' . route('connecthub.inbox.show', $notification->id) . '" class="btn btn-sm btn-primary">View</a>';
            })
            ->rawColumns(['user', 'status', 'action'])
            ->toJson();
    }

    public function show($id)
    {
        $notification = AdminNotification::with(['user', 'replies.sender'])
            ->findOrFail($id);

        return view('admin.connecthub.inbox.show', compact('notification'));
    }

    public function reply(Request $request, $id)
    {
        $request->validate([
            'message' => 'required|string',
        ]);

        $notification = AdminNotification::findOrFail($id);

        $reply = new AdminNotificationReply();
        $reply->admin_notification_id = $notification->id;
        $reply->sender_id = Auth::id();
        $reply->message = $request->message;
        $reply->save();

        $notification->status = 'in_progress';
        $notification->save();

        return redirect()->back()->with('success', 'Reply sent successfully.');
    }
}
