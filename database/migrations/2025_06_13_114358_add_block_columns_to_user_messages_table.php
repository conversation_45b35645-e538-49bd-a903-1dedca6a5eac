<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_messages', function (Blueprint $table) {
            if (Schema::hasColumn('user_messages', 'is_blocked')) {
                $table->dropColumn('is_blocked');
            }
            if (Schema::hasColumn('user_messages', 'blocked_at')) {
                $table->dropColumn('blocked_at');
            }
        });

        Schema::table('user_messages', function (Blueprint $table) {
            $table->boolean('is_blocked')->default(false)->after('read_at');
            $table->timestamp('blocked_at')->nullable()->after('is_blocked');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_messages', function (Blueprint $table) {
            $table->dropColumn(['is_blocked', 'blocked_at']);
        });
    }
};
