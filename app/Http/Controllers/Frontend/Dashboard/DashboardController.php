<?php

namespace App\Http\Controllers\Frontend\Dashboard;

use App\Models\User;
use App\Models\Company;
use App\Models\Country;
use App\Models\Product;
use App\Models\Category;
use Faker\Factory as Faker;
use Illuminate\Support\Str;
use App\Models\LocationType;
use Illuminate\Http\Request;
use OpenSearch\ClientBuilder;
use App\Models\ProductPricing;
use App\Models\OrganisationSize;
use App\Models\OrganisationType;
use App\Http\Controllers\Controller;
use App\Services\CategoryService;
use App\Services\IPService;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    protected $ipApi;
    protected $categoryService;

    /**
     * Create a new instance of the controller.
     *
     * @param IpService $ipService
     */
    public function __construct(IPService $ipApi, CategoryService $categoryService)
    {
        $this->ipApi = $ipApi;
        $this->categoryService = $categoryService;;
    }
    /**
     * Display a listing of products and services with search and filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $getData = $this->ipApi->getSearchLocation();
        $queryParams = $getData['queryParams'];
        $location = $getData['location'];

        $rawResults = Product::search('')
            ->raw();
        // Initialize typeCounts array
        $typeCounts = $rawResults['typeCounts'];

        // Extract total counts for each type
        $totalProducts = $typeCounts['product'] ?? 0;
        $totalServices = $typeCounts['service'] ?? 0;

        // get total users
        $totalUsers = User::count();
        // get total suppliers
        $totalSuppliers = Company::count();

        $ProductCategoryCounts = DB::table('category_product')
            ->join('categories', 'category_product.category_id', '=', 'categories.id')
            ->select('categories.name', DB::raw('COUNT(category_product.product_id) as product_count'))
            ->groupBy('categories.name')
            ->get();
        $getCategoryFilterData = $this->categoryService->getCategoryFilterData();
        $companyCategoryCounts = DB::table('company_business_categories')
            ->join('categories', 'company_business_categories.level_2_category_id', '=', 'categories.id')
            ->select('categories.name', DB::raw('COUNT(company_business_categories.company_id) as company_count'))
            ->groupBy('categories.name')
            ->get();

        return view('frontend.dashboard.index', compact('totalServices', 'totalProducts', 'totalUsers', 'totalSuppliers', 'location', 'queryParams', 'ProductCategoryCounts', 'companyCategoryCounts', 'getCategoryFilterData'));
    }


    // Show a single product
    public function show($uuid)
    {
        $product = Product::where('uuid', $uuid)->firstOrFail();
        return view('frontend.product.show', compact('product'));
    }

    /**
     * Generate and insert fake product data.
     */
    public function generateFakeProducts()
    {
        $faker = Faker::create();

        // Fetch all categories to randomly assign them to products
        $categories = Category::pluck('id')->toArray();

        // // Fetch all organisation type, size, and user IDs to randomly assign them to companies
        // $organisationTypes = OrganisationType::pluck('id')->toArray();
        // $organisationSizes = OrganisationSize::pluck('id')->toArray();
        // $userIds = User::pluck('id')->toArray(); // Fetch all user IDs

        // // Fetch all country and location type IDs for company locations
        // $countryIds = Country::pluck('id')->toArray();
        // $locationTypeIds = LocationType::pluck('id')->toArray();

        // foreach (range(1, 10) as $index) { // Generate 10 fake companies
        //     $company = Company::create([
        //         'name'                 => $faker->company, // Generate a fake company name
        //         'email'                => $faker->unique()->companyEmail, // Generate a unique company email
        //         'phone'                => $faker->phoneNumber, // Generate a fake phone number
        //         'address'              => $faker->address, // Generate a fake address
        //         'website'              => $faker->url, // Generate a fake website URL
        //         'organisation_type_id' => $faker->randomElement($organisationTypes), // Assign a random organisation type
        //         'organisation_size_id' => $faker->randomElement($organisationSizes), // Assign a random organisation size
        //         'user_id'              => $faker->randomElement($userIds), // Assign a random user ID
        //     ]);

        //     // Create related company locations
        //     foreach (range(1, rand(1, 3)) as $i) { // Generate 1 to 3 locations per company
        //         $company->company_locations()->create([
        //             'country_id'        => $faker->randomElement($countryIds), // Assign a random country ID
        //             'location_type_id'  => $faker->randomElement($locationTypeIds), // Assign a random location type ID
        //             'city'              => $faker->city, // Generate a fake city name
        //             'address'           => $faker->address, // Generate a fake address
        //         ]);
        //     }
        // }

        // Fetch all company IDs to randomly assign them to products
        $companies = Company::pluck('id')->toArray(); // Replace with your actual Company model

        // Fetch all country data to randomly assign regions
        $countries = Country::pluck('id')->toArray(); // Replace 'region' with the actual column name for regions in your countries table

        foreach (range(1, 10) as $index) { // Generate 10 fake products
            // Create a fake product
            $product = Product::create([
                'uuid'           => Str::uuid(),
                'company_id'     => $faker->randomElement($companies), // Assign a random company ID
                'name'           => $faker->word . ' ' . $faker->randomElement(['Phone', 'Tablet', 'Laptop']),
                'description'    => $faker->sentence,
                'type'           => $faker->randomElement(['product', 'service']),
                'listing_status' => 'Active',
            ]);

            // Assign a random past date to created_at
            $product->created_at = now()->subDays(rand(1, 365)); // Random date within the last year
            $product->save();

            // Create related pricing entries for the product
            foreach (range(1, rand(2, 5)) as $i) { // Generate 2 to 5 pricing entries
                ProductPricing::create([
                    'product_id'    => $product->id,
                    'price'         => $faker->randomFloat(2, 10, 1000), // Random price between 10 and 1000
                    'per_unit'      => $faker->randomElement(['piece', 'box', 'kg', 'litre']),
                    'min_order'     => $faker->numberBetween(1, 50), // Random minimum order quantity
                    'part_number'   => $faker->bothify('PN-###??'), // Random part number
                    'payment_method' => $faker->randomElement(['Credit Card', 'PayPal', 'Bank Transfer']),
                    'notes'         => $faker->sentence,
                ]);
            }


            // Assign random categories to the product
            $assignedCategories = $faker->randomElements($categories, rand(1, 3)); // Assign 1 to 3 random categories
            $product->categories()->attach($assignedCategories);
            $assignedCountries = $faker->randomElements($countries, rand(1, 3)); // Assign 1 to 3 random countries
            $product->regions()->attach($assignedCountries);
            $product->searchable();
        }

        die('Fake products, pricing, and company locations generated successfully');
    }
}
