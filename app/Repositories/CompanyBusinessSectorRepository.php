<?php

namespace App\Repositories;

use App\Models\CompanyBusinessSector;

/**
 * Class CompanyBusinessSectorRepository
 *
 * Handles data operations for the CompanyBusinessSector model.
 */
class CompanyBusinessSectorRepository extends BaseRepository
{
    /**
     * The CompanyBusinessSector model instance.
     *
     * @var \App\Models\CompanyBusinessSector
     */
    protected $model;

    /**
     * CompanyBusinessSectorRepository constructor.
     *
     * @param \App\Models\CompanyBusinessSector $companyBusinessSector
     */
    public function __construct(CompanyBusinessSector $companyBusinessSector)
    {
        $this->model = $companyBusinessSector;
    }
}
