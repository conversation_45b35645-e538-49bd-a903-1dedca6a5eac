<?php

namespace App\Services\Content;

use App\Models\HowToHub;
use App\Models\HowToHubFile;
use App\Repositories\HowToHubRepository as Repository;
use App\Repositories\HowToHubFileRepository as FileRepository;
use App\Traits\DBService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HowToHubService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

    /*
     * How To Hub Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /*
     * How To Hub File Repository Obj
     *
     * @var FileRepository $fileRepository
     */
    public $fileRepository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository, FileRepository $fileRepository)
    {
        $this->repository = $repository;
        $this->fileRepository = $fileRepository;
    }

    /**
     * Get list of all the statuses
     *
     * @return array
     */
    public function getStatuses()
    {
        return $this->repository->statuses();
    }

    public function addFiles(HowToHub $hub, Request $request)
    {
        $destinationPath = HowToHubFile::FILE_PATH;
        if (!Storage::disk('public')->exists($destinationPath)) {
            Storage::disk('public')->makeDirectory($destinationPath);
        }

        if ($request->file('files')) {
            foreach ($request->file('files') as $file) {
                $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                $file->storePubliclyAs($destinationPath, $filename, 'public');

                $hub->files()->create([
                    'file' => $filename,
                ]);
            }
        }
    }

    /**
     * Get query or collection for the page list
     *
     * @param boolean $query
     * @param array $params
     * @return Builder|Collection
     */
    public function getListForAdmin(bool $query = false, array $params = []) : Builder|Collection
    {
        if ($query) {
            return $this->repository->queryByMixedCondition($params);
        } else {
            return $this->repository->getByMixedCondition($params);
        }        
    }

    public function deleteFile(int $id)
    {
        $file = $this->fileRepository->findById($id);

        if ($file) {
            $filePath = $file->file_url;

            if (Storage::exists($filePath)) {
                Storage::delete($filePath);
            }

            $file->delete();

            return response()->json(['message' => 'File deleted successfully.']);
        }

        return response()->json(['message' => 'File not found.'], 404);
    }
}
