<?php

namespace App\Http\Controllers\Admin\Plan;

use App\Http\Controllers\Controller;
use App\Models\StripeSubscription;
use App\Services\PlanService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Yajra\DataTables\Facades\DataTables;

class PlanController extends Controller
{

    /**
     * How To Hub service instance.
     *
     * @var PlanService
     */
    public $service;
    
    /**
     * routePrefix
     *
     * @var mixed
     */
    public $routePrefix;

    /**
     * Instantiate a new controller instance.
     *
     * @param PlanService $service The service layer for managing plans.
     * @return void
     */
    public function __construct(PlanService $service)
    {
        $this->service = $service;
        $this->routePrefix = 'admin.plans.';
    }
    /**
     * Display a listing of HowToHub entries.
     *
     * @return View|JsonResponse The view displaying the list of hubs.
     */
    public function getPlans(Request $request): View|JsonResponse
    {

        if ($request->ajax()) {
            return $this->list($request);
        }

        return view('admin.plans.index');

    }

    /**
     * Return a JSON response with the list of pages for DataTables.
     *
     * @param Request $request The current HTTP request instance.
     * @return JsonResponse The DataTable JSON response.
     */
    protected function list(Request $request): JsonResponse
    {
        $plans = $this->service->getAllStripePlans(true);
        $user = $this->service->current();

        return DataTables::of($plans)
            ->addColumn('created', function ($plan) {
                return $plan->created_at->format('d-m-Y');
            })
            ->addColumn('is_assigned', function ($plan) {
                return $plan->is_assigned ? 'Yes' : 'No';
            })
            
            ->addColumn('status', function ($plan) {
                $statusDetails = StripeSubscription::getStatusLabelAndStyle($plan->is_active);
                return '<div style="' . $statusDetails['style'] . '">' . $statusDetails['label'] . '</div>';
            })

            ->addColumn('action', function ($plan) use ($user) {
                $action = '';
                if ($user->can('manage plans') || $user->can('view plans')) {
                    $action .= '<a href="' . route('admin.plans.show', ['plan' => $plan->id]) . '" class="btn btn-primary">View</a> ';
                }
                
                return $action;
            })
            ->addColumn('subscription_rank', function ($plan) {
                $statusDetails = StripeSubscription::getStatusLabelAndStyle(0);
                return $plan->subscription_rank==0 ? '<div style="' . $statusDetails['style'] . '">Missing Rank</div>' : $plan->subscription_rank;
            })
            ->rawColumns(['status', 'action','name','subscription_rank'])
            ->toJson();
    }
    /**
     * Display the specified plan details.
     *
     * @param StripeSubscription $plan The plan to display.
     * @return \Illuminate\View\View The view with plan details.
     */
    public function show(StripeSubscription $plan)
    {
        if (!Gate::any(['manage plans', 'view plans'])) {
            abort(403);
        }

        $routePrefix = 'admin.plans.';
        $title = 'Stripe Subscription';

        return view('admin.plans.show', compact(
            'title',
            'routePrefix',
            'plan'
        ));
    }


    /**
     * Display the plan edit form.
     */
    public function edit(StripeSubscription $plan): View
    {
        return view('admin.plans.edit', [
            'plan' => $plan,
        ]);
    }


        /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return back()->with('status', 'Profile information updated successfully!');
    }
}
