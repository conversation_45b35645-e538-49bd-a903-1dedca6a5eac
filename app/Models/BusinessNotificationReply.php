<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BusinessNotificationReply extends Model
{
    protected $fillable = [
        'business_notification_id',
        'sender_id',
        'message',
    ];

    public function notification()
    {
        return $this->belongsTo(BusinessNotification::class, 'business_notification_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }
}

