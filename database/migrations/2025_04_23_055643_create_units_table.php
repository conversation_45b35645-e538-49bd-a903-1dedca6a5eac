<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUnitsTable extends Migration
{
    public function up()
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id(); // Primary key
            $table->string('name'); // Unit name
            $table->text('description'); // Unit description
            $table->timestamps(); // Created and updated timestamps
        });

        // Modify product_pricings table
        Schema::table('product_pricings', function (Blueprint $table) {
            $table->unsignedBigInteger('per_unit')->nullable()->change(); // Change per_unit to int
            $table->foreign('per_unit')->references('id')->on('units')->onDelete('set null'); // Add foreign key
        });
    }

    public function down()
    {
        Schema::table('product_pricings', function (Blueprint $table) {
            $table->dropForeign(['per_unit']); // Drop foreign key
            $table->string('per_unit')->change(); // Revert per_unit to string
        });

        Schema::dropIfExists('units');
    }
}
