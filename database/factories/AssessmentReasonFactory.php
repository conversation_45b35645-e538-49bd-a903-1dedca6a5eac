<?php

namespace Database\Factories;

use App\Models\AssessmentReason;
use App\Models\AssessmentSection;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<AssessmentReason>
 */
class AssessmentReasonFactory extends Factory
{
    protected $model = AssessmentReason::class;

    public function definition(): array
    {
        return [
            'section_id' => AssessmentSection::factory(),
            'reason' => $this->faker->sentence,
        ];
    }
}
