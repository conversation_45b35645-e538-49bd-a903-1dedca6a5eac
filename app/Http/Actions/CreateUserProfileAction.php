<?php

namespace App\Http\Actions;

use App\Models\Area;
use App\Models\Country;
use App\Models\GeoCountry;
use App\Models\User;
use App\Models\UserCompany;
use Illuminate\Support\Facades\DB;

class CreateUserProfileAction
{
    /**
     * Handles the submission of basic details.
     *
     * @param array $data The user data for basic details.
     * @param User $user The user instance.
     * @return User
     */
    public function executeBasicDetails(array $data, User $user): User
    {
        //Fetching country_id by code
        $country = GeoCountry::where('code', $data['country_code'])->first();
        $pointWKT = "POINT({$data['latitude']} {$data['longitude']})";
        //Fetching region_id by country_id and point
        $region = \DB::table('geo_regions as r')
        ->join('geo_countries as c', 'r.country_id', '=', 'c.id')
        ->select('r.id as region_id')
        ->where('c.id', $country->id)
        ->whereRaw("ST_Contains(r.shape, ST_GeomFromText(?, 4326))", [$pointWKT])
        ->first();
        //Saving area
        $city = explode(',', $data['city'])[0];
        $area = Area::updateOrCreate(
            [
                'name' => $city,
                'geo_region_id' => $region ? $region->region_id : 0,
                'geo_country_id' => $country->id,
            ],
            [
                'slug' => $this->createSlug($city),
                'latitude' => $data['latitude'],
                'longitude' => $data['longitude'],
            ]
        );
        //Updating user details
        $data['area_id'] = $area->id;

        $user->update($data);
        return $user;
    }

    /**
     * Handles the submission of occupation details.
     *
     * @param array $data The user data for occupation details.
     * @param User $user The user instance.
     * @return User
     */
    public function executeOccupationDetails(array $data, User $user): User
    {
        return DB::transaction(function () use ($user, $data) {
            if(!empty($data['company_name']) || !empty($data['occupation'])){
                UserCompany::updateOrCreate(
                    [
                        'user_id' => $user->id,
                    ],
                    [
                        'company_name' => $data['company_name'],
                        'position' => $data['occupation'],
                    ]
                );
            }
            if (isset($data['categories'])) {
                $user->categories()->sync($data['categories']);
            }else{
                $user->categories()->detach();
            }

            
            
            return $user;
        });
    }

    /**
     * Handles the submission of phone number.
     *
     * @param array $data The user data for phone number.
     * @param User $user The user instance.
     * @return User
     */
    public function executePhoneNumber(array $data, User $user): User
    {
        $data['phone'] = $data['phone_prefix'] . $data['number'];
        $user->update($data);
        return $user;
    }

    function createSlug($string) {
        // Convert to lowercase
        $string = strtolower($string);
        
        // Replace non-alphanumeric characters (except spaces) with an empty string
        $string = preg_replace('/[^a-z0-9\s]/', '', $string);
        
        // Replace multiple spaces with a single hyphen
        $string = preg_replace('/\s+/', '-', $string);
        
        return trim($string, '-'); // Remove leading and trailing hyphens
    }
}
