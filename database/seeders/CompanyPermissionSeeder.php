<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class CompanyPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        // Reset cached roles and permissions
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        // Delete existing supplier-type permissions
        Permission::where('type', 'supplier')->delete();

        // Define permissions
        $permissions = [
            [
                'slug' => 'manage-dashboard',
                'name' => 'manage dashboard',
                'type' => 'supplier',
            ],
            [
                'slug' => 'view-only-dashboard',
                'name' => 'view only dashboard',
                'type' => 'supplier',
            ],
            [
                'slug' => 'inbox sccess',
                'name' => 'inbox access',
                'type' => 'supplier',
            ],
            [
                'slug' => 'chat access',
                'name' => 'chat access',
                'type' => 'supplier',
            ],
        ];

        // Create permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                [
                    'slug' => $permission['slug'],
                     'type' => $permission['type']
                    ],
                [
                    'name' => $permission['name']
                ]
            );
        }

    }
}
