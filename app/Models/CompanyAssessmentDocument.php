<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyAssessmentDocument
 *
 * @property int $id
 * @property int $assessment_id
 * @property int $section_id
 * @property string $path
 * @property int $uploaded_by
 */
class CompanyAssessmentDocument extends Model
{
    protected $fillable = [
        'assessment_id',
        'section_id',
        'path',
        'uploaded_by',
    ];

    public function assessment()
    {
        return $this->belongsTo(CompanyAssessment::class);
    }

    public function section()
    {
        return $this->belongsTo(AssessmentSection::class);
    }

    public function uploadedBy()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
}
