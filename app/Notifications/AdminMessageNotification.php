<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminMessageNotification extends Notification
{
    use Queueable;

    public $subject;
    public $message;


    /**
     * Create a new notification instance.
     */
    public function __construct($message, $subject)
    {
        $this->subject = $subject;
        $this->message = $message;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        try {
            return (new MailMessage)
                ->mailer(config('mail.default')) // Use primary mailer
                ->subject('New Notification from Admin')
                ->greeting("Hello {$notifiable->name},")
                ->line($this->message)
                ->salutation("Best regards,  \nAdmin Team");
        } catch (\Exception $e) {
            \Log::error('Admin Notification Email Failed: ' . $e->getMessage());

            return (new MailMessage)
                ->mailer(env('MAIL_FAILOVER', 'smtp')) // Fallback mailer
                ->subject('New Notification from Admin')
                ->greeting("Hello {$notifiable->name},")
                ->line($this->message)
                ->salutation("Best regards,  \nAdmin Team");
        }
    }


    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subject' => $this->subject,
            'message' => $this->message
        ];
    }
}
