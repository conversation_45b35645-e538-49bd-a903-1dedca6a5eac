<?php

namespace App\Http\Middleware;

use App\Http\Controllers\Auth\RegisteredUserController;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class CheckSignupSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
            // Check if the session variable exists
            $user = null;
            if (Session::has('registeredUserId') && !Session::has('modalToShow')) {
                $user = User::find(Session::get('registeredUserId'));
            }
            if($request->email){
                app(RegisteredUserController::class)->handleUserDestroy($request->email, $user);
            }
            
            
        return $next($request);
    }
}
