<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionDowngradeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private $companyName;
    private $subscriptionPlanName;


    /**
     * Create a new notification instance.
     */
    public function __construct($companyName, $subscriptionPlanName)
    {
        $this->companyName = $companyName;
        $this->subscriptionPlanName = $subscriptionPlanName;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        try {
            return (new MailMessage)
                ->mailer(config('mail.default')) // Use primary mailer
                ->subject('Subscription Downgraded – Action Required')
                ->greeting("Dear {$notifiable->name},")
                ->line("We previously reached out regarding your **{$this->companyName}** subscription at **Honley.online**, but as payment has not been received within the **14-day grace period**, your account has now been **downgraded from {$this->subscriptionPlanName} to Free Tier**.")
                ->line('### **To Restore Your Subscription:**')
                ->line("1. Log in to your company account at [Honley.online](https://honley.online).")
                ->line('2. Go to **"Subscription & Payment"** under account settings.')
                ->line('3. Select a plan and update your payment details.')
                ->line("Your data has been retained where possible, but some features may no longer be available. If you need assistance, please contact our support team.")
                ->salutation("**Best regards,**  \nHonley Support Team");
        } catch (\Exception $e) {
            \Log::error('Mail Sending Failed: ' . $e->getMessage()); // Log the error

            return (new MailMessage)
                ->mailer(env('MAIL_FAILOVER', 'smtp')) // Use fallback mailer
                ->subject('Subscription Downgraded – Action Required')
                ->greeting("Dear {$notifiable->name},")
                ->line("We previously reached out regarding your **{$this->companyName}** subscription at **Honley.online**, but as payment has not been received within the **14-day grace period**, your account has now been **downgraded from {$this->subscriptionPlanName} to Free Tier**.")
                ->line('### **To Restore Your Subscription:**')
                ->line("1. Log in to your company account at [Honley.online](https://honley.online).")
                ->line('2. Go to **"Subscription & Payment"** under account settings.')
                ->line('3. Select a plan and update your payment details.')
                ->line("Your data has been retained where possible, but some features may no longer be available. If you need assistance, please contact our support team.")
                ->salutation("**Best regards,**  \nHonley Support Team");
        }
    }


    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'company_name' => $this->companyName,
            'subscription_plan' => $this->subscriptionPlanName,
            'message' => "Your subscription at Honley.online has been downgraded from {$this->subscriptionPlanName} to Free Tier due to non-payment.",
            'action_url' => url('/billing'),
            'notified_at' => now(),
        ];
    }

}
