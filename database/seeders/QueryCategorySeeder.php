<?php

namespace Database\Seeders;

use App\Models\QueryCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class QueryCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $titles = [
            'Account or Profile Management',
            'Compliance and Policies',
            'Billing and Payments',
            'Collaborative Opportunities',
            'Feedback and Complaints',
            'General Inquiries'];

        foreach($titles as $title){
             QueryCategory::create([
                 'title' => $title
             ]);
        }
    }
}
