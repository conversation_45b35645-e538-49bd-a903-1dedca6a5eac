const html = document.querySelector('html');
html.dataset.theme = `theme-light`;

function switchTheme(theme) {
  html.dataset.theme = `theme-${theme}`;
  jQuery('body').addClass(html.dataset.theme);
}

jQuery(document).ready(function(){
    if(getCookie('set_dark_theme')){
        switchTheme('dark');
        if(jQuery('.switch').length == 1){
            jQuery('.switch input').prop('checked',true);
        }
    }
    else{
        switchTheme('light');
    }
    if(jQuery('html').attr('data-theme')=='theme-light'){
        jQuery('.navbar-brand img').attr('src','images/logo.png');
        jQuery('.footer-copy-logo').attr('src','images/logo.png');
    }
    else
    {
        jQuery('.navbar-brand img').attr('src','images/logo_dark.png');
        jQuery('.footer-copy-logo').attr('src','images/logo_dark.png');
    }
});

jQuery(document).on('change','.switch input',function(){
    console.log(jQuery(this).prop('checked'));
    if(jQuery(this).prop('checked')==true){
        setCookie('set_dark_theme',1,30);
    }
    else
    {
        setCookie('set_dark_theme',-1,-1);
    }
    setTimeout(function(){
        window.location.reload();
    },1000);
});

function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}

function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}