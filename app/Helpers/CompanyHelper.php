<?php

use App\Models\CompanyAdmin;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

if (!function_exists('getCompanyId')) {
    function getCompanyId(): ?int
    {
        $user = auth()->user();

        if (!$user) {
            return null;
        }

        if (!is_null($user->company)) {
            return $user->company->id;
        }


        return CompanyAdmin::where('user_id', $user->id)
            ->where('status', CompanyAdmin::STATUS_ACTIVE)
            ->value('company_id');
    }
}

function supplierFilterSortList()
{

    return [
        'best_match' => "Best Match",
        'feedback' => "Feedback",
        'location_nearest' => "Location  (Nearest First)",
        'founded_new' => "Founded New → Old",
        'founded_old' => "Founded Old → New",
        'portfolio_size' => "Portfolio Size",
    ];
}


if (!function_exists('getUsers')) {
    function getUsers($user = null, $search = null)
    {

        if (!is_null($user)) {
            return User::where('id', $user->id)
                ->with(['company'])
                ->first();

        }

        return User::whereDoesntHave('roles', function ($query) {
            $query->where('name', User::ROLE_ADMIN);
        })
            ->when($search, function ($query) {
                $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                        ->orWhere('last_name', 'like', '%' . $this->search . '%');
                });
            })
            ->with(['company'])
            ->where(function ($query) {
                $query->whereHas('company', function ($q) {
                    $q->where('status', CompanyAdmin::STATUS_ACTIVE);
                })
                    ->orWhereDoesntHave('company');
            })
            ->get();


    }
}