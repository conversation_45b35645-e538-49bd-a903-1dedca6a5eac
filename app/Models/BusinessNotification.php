<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BusinessNotification extends Model
{
    protected $fillable = [
        'user_id',
        'business_id',
        'reference_number',
        'category',
        'subject',
        'message',
        'is_resolved',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function business()
    {
        return $this->belongsTo(Company::class, 'business_id');
    }

    public function replies()
    {
        return $this->hasMany(BusinessNotificationReply::class);
    }
}

