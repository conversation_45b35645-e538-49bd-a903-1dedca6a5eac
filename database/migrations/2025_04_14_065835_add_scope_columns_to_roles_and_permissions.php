<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Adding scope columns to roles and permissions tables
        // type: admin or company
        // company_id: the id of the company that the role or permission belongs to

        Schema::table('roles', function (Blueprint $table) {
            $table->string('type')->after('guard_name')->comment('admin or company');
            $table->unsignedBigInteger('company_id')->nullable()->after('type');

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');

        });

        Schema::table('permissions', function (Blueprint $table) {
            $table->string('type')->after('guard_name')->comment('admin or company');
            $table->unsignedBigInteger('company_id')->nullable()->after('type');

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        
        Schema::table('roles', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
            $table->dropColumn(['type', 'company_id']);
        });

        Schema::table('permissions', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
            $table->dropColumn(['type', 'company_id']);
        });
    }
};
