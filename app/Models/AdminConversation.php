<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AdminConversation extends Model
{
    protected $fillable = ['user_id', 'assigned_to', 'status'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assignedAdmin()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function messages()
    {
        return $this->hasMany(AdminMessage::class, 'conversation_id');
    }
}
