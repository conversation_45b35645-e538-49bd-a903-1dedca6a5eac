<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NotificationsUserCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('notifications_user_categories')->insert([
            ['name' => 'Account or Profile Management', 'description' => null],
            ['name' => 'Compliance and Policies', 'description' => null],
            ['name' => 'Billing and Payments', 'description' => null],
            ['name' => 'Collaborative Opportunities', 'description' => null],
            ['name' => 'Feedback and Complaints', 'description' => null],
            ['name' => 'General Inquiries', 'description' => null],
        ]);
    }
}
