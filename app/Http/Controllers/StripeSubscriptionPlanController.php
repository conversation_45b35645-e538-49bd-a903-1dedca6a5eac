<?php

namespace App\Http\Controllers;

use App\Models\PlanFeature;
use App\Models\StripeSubscription;
use Illuminate\Http\Request;

class StripeSubscriptionPlanController extends Controller
{
    public function index()
    {
        $plans = StripeSubscription::with(['planFeatures' => function ($query) {
            $query->where('status', PlanFeature::STATUS_ACTIVE);
            $query->where('feature_type', PlanFeature::FEATURE_TYPE_MARKETING);
        }])
        ->where('is_active', StripeSubscription::STATUS_ACTIVE)
        ->whereHas('planFeatures', function ($query) {
            $query->where('status', PlanFeature::STATUS_ACTIVE);
        })
        ->orderBy('subscription_rank', 'asc')
        ->take(3)
        ->get();
    
    
        return view('frontend.stripe-subscription-plans', compact('plans'));
    }
}
