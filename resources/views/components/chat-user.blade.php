<li wire:key="chat-user-{{ $user->id }}">
    <div class="list-box">
        <div class="left">
            @if ($showingCheckboxes)
                <div class="form-check me-2">
                    <input class="form-check-input" type="checkbox" wire:model.defer="selectedConversations"
                        wire:change="$toggle('dummyToggle')" value="{{ $user->conversation_id }}"
                        id="checkbox-{{ $user->id }}">
                    <label class="form-check-label" for="checkbox-{{ $user->id }}"></label>
                </div>
            @endif
            <div class="avatar">
                <img src="{{ $user->getFirstMedia('profile_photo') ? $user->getFirstMedia('profile_photo')->getUrl() : asset('images/dashboard/user/profile-pic.png') }}"
                    alt="person" />
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li class="chat not-active">
                            <a class="dropdown-item open-chat" href="#" data-user-id="{{ $user->id }}"
                                data-user-name="{{ $user->name }}" data-user-location="New York"
                                data-bs-toggle="modal" data-bs-target="#chatMessageModal"
                                wire:click="markMessagesAsRead({{ $user->id }})">Open Chat</a>
                        </li>
                        <li><a class="dropdown-item load-dynamic-profile" data-user-id="{{ $user->id }}"
                                href="#">View Profile</a></li>
                        <li><a class="dropdown-item" href="#"
                                wire:click="archiveIndividual({{ $user->conversation_id }})">Archive</a></li>
                        <li><a class="dropdown-item" href="#">Report</a></li>
                        <li>
                            <a href="#" class="dropdown-item"
                                wire:click.prevent="toggleBlock({{ $user->id }})">
                                {{ in_array($user->id, $blockedUserIds) ? 'Unblock' : 'Block' }}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="details">
                <h4>{{ $user->name }}</h4>
                <p>{{ $user->user_company?->position }}{{ $user->user_company?->position ? ' @ ' . $user->company?->name : '' }}
                </p>
                <div class="message">{{ $user->last_message ?? 'No messages yet' }}</div>
            </div>
        </div>
        <div class="right">
            <span
                class="time">{{ $user->last_message_time ? \Carbon\Carbon::parse($user->last_message_time)->format('H:i') : '' }}</span>
            <button class="btn btn-secondary chat @if ($user->unread_count == 0) not-active @endif open-chat"
                data-user-id="{{ $user->id }}" data-user-name="{{ $user->name }}" data-user-location="New York"
                data-bs-toggle="modal" data-bs-target="#chatMessageModal"
                wire:click="markMessagesAsRead({{ $user->id }})">
                <span>chat @if ($user->unread_count > 0)
                        ({{ $user->unread_count }})
                    @endif
                </span>
            </button>
        </div>
    </div>
</li>
