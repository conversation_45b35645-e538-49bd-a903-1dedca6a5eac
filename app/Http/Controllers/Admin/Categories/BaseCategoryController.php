<?php

namespace App\Http\Controllers\Admin\Categories;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ImportCategoryRequest;
use App\Http\Requests\Admin\StoreCategoryRequest;
use App\Http\Requests\Admin\UpdateCategoryRequest;
use App\Imports\CategoryImport;
use App\Models\Category;
use App\Services\CategoryService;
use App\Services\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

/**
 * Handles administrative operations for managing categories.
 */
class BaseCategoryController extends Controller
{
    /**
     * The Category service instance.
     *
     * @var CategoryService
     */
    public $service;

    /**
     * Type of the category
     *
     * int $type
     */
    public $type;

    /**
     * Page title based on the type of categories
     *
     * @var $title
     */
    public $title;

    /**
     * Route prefix based on the type of categories
     *
     * @var $routePrefix
     */
    public $routePrefix;

    /**
     * Create a new controller instance.
     *
     * @param CategoryService $service
     * @param integer $type
     * @param string $routePrefix
     * @param string $title
     * @return void
     */
    public function __construct(CategoryService $service, 
        int $type, 
        string $routePrefix, 
        string $title)
    {
        $this->service = $service;
        $this->type = $type;
        $this->routePrefix = $routePrefix;
        $this->title = $title;
    }

    /**
     * Display a listing of the categories.
     *
     * @param Request $request The current HTTP request instance.
     * @return \Illuminate\Http\JsonResponse|\Illuminate\View\View The DataTable JSON response or the view.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }
        
        $type = $this->type;
        $title = $this->title;
        $routePrefix = $this->routePrefix;        

        return view('admin.categories.index', compact('type', 'title', 'routePrefix'));
    }

    /**
     * Display a listing of the categories.
     *
     * @param Request $request The current HTTP request instance.
     * @return \Illuminate\Http\JsonResponse The DataTable JSON response
     */
    protected function list(Request $request)
    {
        $parentId = $request->parent_id ?? null;
        $params = [];

        if ($parentId) {
            $params['parent_id'] = $parentId;
        }

        $params['type'] = $this->type;
        $params['order_by'] = ['level', 'asc'];
        $params['status'] = $request->status;

        $categories = $this->service->getListForAdmin(true, $params);
        $statuses = $this->service->getStatuses();
        $user = app(UserService::class)->current();

        return DataTables::of($categories)
            ->addColumn('parent', function ($category) {
                return $category->parent?->name;
            })
            ->addColumn('status', function ($category) {
                $status = '';
                if ($category->status == Category::STATUS_INACTIVE) {
                    $status = '<div style="background-color:#d35757;text-align: center;border-radius: 15px;color: #fff">In-Active</div>';
                } elseif ($category->status == Category::STATUS_ACTIVE) {
                    $status = '<div style="background-color:#358b00;text-align: center;border-radius: 15px;color: #fff;">Active</div>';
                } elseif ($category->status == Category::STATUS_SUSPENDED) {
                    $status = '<div style="background-color:#ff0404;text-align: center;border-radius: 15px;color: #fff;">Suspended</div>';
                }

                return $status;
            })
            
            ->addColumn('action', function ($category) use ($user,$statuses) {
                $action = '';

                if ($user->can('manage categories') || $user->can('view categories')) {
                    $action .= '<a href="' . route($this->routePrefix . 'show', $category) . '" class="btn btn-primary">View</a>';
                }

                if ($user->can('manage categories')) {
                    
                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    Action
                                </button>
                                <ul class="dropdown-menu" style="">';
                    $action .= '<li class="dropdown-item">';
                    $action .= '<a href="' . route($this->routePrefix . 'edit', $category) . '" class="btn btn-primary"><i class="fa fa-edit">&nbsp;Edit</i></a> ';
                    $action .= '</li>';

                    foreach ($statuses as $name => $value) {
                        $disabled = ($category->status == $value) ? 'disabled' : '';

                        $action .= '<li class="dropdown-item">';
                        $action .= '    <button class="btn btn-sm btn-primary change-status-btn ' . $disabled . '" data-id="' . $category->id . '" ' . $disabled . ' data-statusname="' . $name . '" data-status="' . $value . '">' . $name . '</button>';
                        $action .= '</li>';
                    }
                    $action .= '</ul>';
                }

                

                return $action;
            })
            ->rawColumns(['status','action'])
            ->toJson();
    }

    /**
     * Display a nested view of the categories.
     *
     * @return \Illuminate\View\View The view containing nested categories.
     */
    public function nested()
    {
        $params = ['type' => $this->type];
        $categories = $this->service->getNestedCategories($params);

        $type = $this->type;
        $title = $this->title;
        $routePrefix = $this->routePrefix;        

        return view('admin.categories.nested', compact(
            'type', 
            'title', 
            'routePrefix', 
            'categories'
        ));
    }

    /**
     * Display the specified category details.
     *
     * @param Category $category The category to display.
     * @return \Illuminate\View\View The view with category details.
     */
    public function parseShow(Category $category)
    {
        if (!Gate::any(['manage categories', 'view categories'])) {
            abort(403);
        }

        $type = $this->type;
        $title = $this->title;
        $routePrefix = $this->routePrefix;        

        return view('admin.categories.show', compact(
            'type', 
            'title', 
            'routePrefix', 
            'category'
        ));
    }

    /**
     * Show the form for creating a new category.
     *
     * @param Request $request The current HTTP request instance.
     * @return \Illuminate\View\View The form view for creating a category.
     */
    public function create(Request $request)
    {
        if (!Gate::allows('manage categories')) {
            abort(403);
        }

        $type = $this->type;
        $title = $this->title;
        $routePrefix = $this->routePrefix;

        $params = [
            'type' => $this->type,
        ];
        $categories = $this->service->getNestedCategories($params);

        $categorySelectHtml = $this->service->generateCategorySelect($categories);
        $parentId = $request->parent_id ?? null;                

        return view('admin.categories.create', compact(
            'type', 
            'title', 
            'routePrefix', 
            'categorySelectHtml',
            'parentId'
        ));
    }

    /**
     * Store a newly created category in storage.
     *
     * @param StoreCategoryRequest $request The validated request for storing a category.
     * @return \Illuminate\Http\RedirectResponse A redirect response to the categories index page with a success message.
     */
    public function store(StoreCategoryRequest $request)
    {
        $data = $request->all();
        $parentId = $request->parent_id ?? null;
        $data['level'] = $this->service->getNewLevel($parentId);
        $data['type'] = $this->type;

        $this->service->create($data);

        return redirect()->route($this->routePrefix . 'index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Show the form for editing the specified category.
     *
     * @param Category $category The category to edit.
     * @return \Illuminate\View\View The form view for editing the category.
     */
    public function parseEdit(Category $category)
    {
        if (!Gate::allows('manage categories')) {
            abort(403);
        }

        $type = $this->type;
        $title = $this->title;
        $routePrefix = $this->routePrefix;

        $params = [
            'type' => $this->type,
            'not_in' => [$category->id]
        ];
        $categories = $this->service->getNestedCategories($params);

        $categorySelectHtml = $this->service->generateCategorySelect($categories, $category->parent_id);

        return view('admin.categories.edit', compact(
            'type', 
            'title', 
            'routePrefix', 
            'categorySelectHtml',
            'category'
        ));
    }

    /**
     * Update the specified category in storage.
     *
     * @param UpdateCategoryRequest $request The validated request for updating a category.
     * @param Category $category The category to update.
     * @return \Illuminate\Http\RedirectResponse A redirect response to the categories index page with a success message.
     */
    public function parseUpdate(UpdateCategoryRequest $request, Category $category)
    {
        $data = $request->all();
        $parentId = $request->parent_id ?? null;
        $data['level'] = $this->service->getNewLevel($parentId);

        $this->service->update($category->id, $data);

        return redirect()->route($this->routePrefix . 'index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category from storage.
     *
     * @param Category $category The category to delete.
     * @return \Illuminate\Http\RedirectResponse A redirect response to the categories index page with a success message.
     */
    public function paraseDestroy(Category $category)
    {
        if (!Gate::allows('manage categories')) {
            abort(403);
        }
        
        $this->service->delete($category->id);
        
        return redirect()->route($this->routePrefix . 'index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Display a import page
     *
     * @return \Illuminate\Http\JsonResponse|\Illuminate\View\View The DataTable JSON response or the view.
     */
    public function import()
    {        
        $type = $this->type;
        $title = $this->title;
        $routePrefix = $this->routePrefix;        

        return view('admin.categories.import', compact('type', 'title', 'routePrefix'));
    }

    public function importAction(ImportCategoryRequest $request) 
    {
        try{
            $import = new CategoryImport($this->service, $this->type);

            Excel::import($import, $request->file('file'));

            return redirect()->route($this->routePrefix . 'index')
            ->with('success', 'Categories imported successfully.');
        }catch(\Exception $ex){
            \Illuminate\Support\Facades\Log::info($ex);
            return redirect()->route($this->routePrefix . 'index')
            ->with('error', 'Some error has occur.');

        }
        
    }

    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;
        $user = $this->service->find($id);

        if (!$user) {
            return response()->json(['message' => 'No Category Found']);
        }

        $this->service->update($id, ['status' => $status]);
            
        return response()->json(['message' => 'Status changed successfully']);
    }
}
