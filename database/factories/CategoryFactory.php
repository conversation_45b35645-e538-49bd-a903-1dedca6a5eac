<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class CategoryFactory extends Factory
{
    protected $model = Category::class;

    public function definition()
    {
        return [
            'name' => $this->faker->words(2, true),
            'slug' => null,
            'parent_id' => null,
            'description' => $this->faker->sentence(),
            'level' => 0,
            'type' => $this->faker->randomElement([1, 2]),
        ];
    }

    /**
     * Indicate the category is a child category.
     */
    public function child(int $parentId, int $level = 1, int $type): Factory
    {
        return $this->state(function () use ($parentId, $level, $type) {
            return [
                'parent_id' => $parentId,
                'level' => $level,
                'type' => $type
            ];
        });
    }
}
