<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AdminBroadcastNotification extends Model
{
    protected $fillable = [
        'admin_id', 'target_type', 'target_ids',
        'reference_number', 'category', 'subject', 'message', 'allow_replies'
    ];

    protected $casts = [
        'target_ids' => 'array',
        'allow_replies' => 'boolean',
    ];

    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }
}
