<?php

namespace App\Models;

use App\Models\User;
use App\Models\Product;
use Illuminate\Support\Str;
use <PERSON>vel\Scout\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * Class Product
 *
 * Represents a product in the system.
 *
 * @package App\Models
 *
 * @property int $id
 * @property string $name Name of the product
 * @property string|null $description Detailed description of the product
 * @property string|null $region_restrictions Regions where supply is restricted or special arrangements are needed
 * @property int $company_id Foreign key referring to the company that owns the product
 * @property string|null $additional_pricing_detail Additional pricing information for the product
 * @property string|null $additional_shiping_rate_detail Additional shipping rate details
 * @property string|null $compliance_summary Summary of product compliance with standards and regulations
 * @property string|null $listing_duration Determines whether the product is published immediately or scheduled
 * @property string $listing_status Status of the listing, either "Active" or "Inactive"
 * @property bool $remove_listing Whether the listing is removed (default: false)
 * @property \Illuminate\Support\Carbon|null $created_at Timestamp when the product was created
 * @property \Illuminate\Support\Carbon|null $updated_at Timestamp when the product was last updated
 */
class SaveProduct extends Model
{
    use HasFactory, Searchable;


    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'product_id',
    ];

    public static array $searchableFields = [
        'name',
        'description',
    ];

    /**
     * Define the index name in Elasticsearch.
     */
    public function searchableAs()
    {
        return 'save_products_index';
    }

    /**
     * Define the data that will be indexed.
     */
    public function toSearchableArray()
    {
        return [
            'id'            => $this->id,
            'user_id'            => (int)$this->user_id,
            'product_id'            => (int)$this->product_id,
            'uuid'          => $this->product->uuid,
            'name'          => $this->product->name,
            'description'   => $this->product->description,
            'company'       => $this->product->company?->name,
            'type'          => $this->product->type,
            'created_at'    => $this->product->created_at,
            'listing_duration'    => $this->product->listing_duration,
            'listing_status'    => $this->product->listing_status,
            'listing_release_date'    => $this->product->listing_release_date,
            'listing_end_date'    => $this->product->listing_end_date,
            'price'         => $this->product->pricings()->exists() ? (float) $this->product->pricings()->min('price') : null,
            'location'      => $this->product->regions()->exists()
                ? implode(', ', $this->product->regions()->get()->map(function ($region) {
                    return $region->code;
                })->toArray())
                : null,
            'country_ids' => $this->product->company->company_countries()->exists()
                ? implode(', ', $this->product->company->company_countries()->get()->pluck('country_id')->toArray())
                : null,
            'region_ids' => $this->product->company->company_regions()->exists()
                ? implode(', ', $this->product->company->company_regions()->get()->pluck('region_id')->toArray())
                : null,
            'categories'    => $this->product->categories()->exists()
                ? implode(', ', $this->product->categories()->pluck('name')->toArray())
                : null,
            'filters'       => $this->product->categories()->exists()
                ? implode(', ', $this->product->categories()->pluck('name')->toArray()) . ', ' . implode(', ', $this->product->regions()->get()->map(function ($region) {
                    return $region->code;
                })->toArray())
                : null,
            'feedback'      => mt_rand(0, 100),
        ];
    }

    /**
     * Get the company that owns the product.
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * 
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the company that owns the product.
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * 
     */
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
