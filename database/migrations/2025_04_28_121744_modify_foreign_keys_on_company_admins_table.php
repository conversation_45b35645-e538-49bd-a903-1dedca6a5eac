<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_admins', function (Blueprint $table) {
             // Drop the existing foreign key constraints
             $table->dropForeign(['user_id']);
             $table->dropForeign(['company_id']);
 
             // Re-add the foreign keys without 'onDelete('cascade')'
             $table->foreign('user_id')->references('id')->on('users');
             $table->foreign('company_id')->references('id')->on('companies');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_admins', function (Blueprint $table) {
            // Drop the foreign keys
            $table->dropForeign(['user_id']);
            $table->dropForeign(['company_id']);

            // Re-add the foreign keys with 'onDelete('cascade')'
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
        });
    }
};
