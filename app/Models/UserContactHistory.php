<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserContactHistory extends Model
{
    protected $table = 'user_contact_history';

    public $timestamps = false;

    protected $fillable = [
        'user_id',
        'contact_type',
        'old_value',
        'new_value',
        'status',
        'user_ip_address',
        'requested_at',
        'processed_at',
        'is_main'
    ];

    /**
     * Get the user associated with the profile.
    */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
