<?php

namespace App\Models\Traits;

use App\Mail\EmailVerificationOtpMail;
use App\Services\MailService;
use App\Services\OtpService;
use Illuminate\Support\Facades\Mail;

trait HasVerifiedEmail
{
    /**
     * Determine if the user has verified their email address.
     *
     * @return bool
     */
    public function isEmailVerified(): bool
    {
        return $this->email_verified_at !== null;
    }

    /**
     * Send an email verification to the user's email address.
     *
     * @return void
     */
    public function sendEmailVerification($updateEmail = null): void
    {
        if($updateEmail){
            $this->email = $updateEmail;
        }
        $otp = app(OtpService::class)->generate($this, 'email');
        MailService::send(new EmailVerificationOtpMail($otp), $this->email);
        // Mail::to($this->email)->send(new EmailVerificationOtpMail($otp));
    }

    /**
     * Mark the user's email as verified.
     *
     * @return void
     */
    public function markEmailAsVerified(): void
    {
        $this->forceFill([
            'email_verified_at' => now(),
        ])->save();
    }
}
