<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class HowToHub
 * 
 * Represents a "How To" hub section.
 *
 * @package App\Models
 */
class HowToHub extends Model
{
    use HasFactory;

    /** @var integer Status identifier active content */
    public const PUBLISHED = 1;

    /** @var integer Status identifier in-active content */
    public const DRAFTED = 0;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'section',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'created_by',
        'updated_by',
        'title',
    ];

    /**
     * Scope for retrieving only published hubs.
     *
     * @param $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Get the files associated with the hub.
     */
    public function files()
    {
        return $this->hasMany(HowToHubFile::class, 'how_to_hub_id');
    }
    
    /**
     * creator
     *
     * @return void
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    
    /**
     * updater
     *
     * @return void
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

}
