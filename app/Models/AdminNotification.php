<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class AdminNotification extends Model implements HasMedia
{
     use InteractsWithMedia;
    protected $fillable = [
        'user_id', 'reference_number', 'category', 'subject', 'message',
        'allow_replies', 'assigned_to', 'status'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assignedAdmin()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function replies()
    {
        return $this->hasMany(AdminNotificationReply::class);
    }
}
