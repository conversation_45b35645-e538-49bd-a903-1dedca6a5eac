<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreHubRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Update based on your authorization logic if necessary
    }

    public function rules()
    {
        return [
            'title' => 'required|string|max:255',
            'section' => 'required|string|max:255',
            'status' => 'required',
            'files' => 'required|array',
            'files.*' => 'file|mimetypes:image/jpeg,image/jpg,image/png,application/pdf,text/html',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:255',
            'meta_keywords' => 'nullable|string',
        ];
    }

    public function messages()
    {
        return [
            'files.*.required' => 'Please select a file',
            'files.*.mimetypes' => 'Please select a jpeg, jpg, png, html or PDF file',
        ];
    }
}
