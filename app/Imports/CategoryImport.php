<?php

namespace App\Imports;

use App\Models\Category;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use App\Services\CategoryService;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CategoryImport implements ToCollection
{
    protected $service;

    protected $type;

    public function __construct(CategoryService $service, int $type)
    {
        $this->service = $service;
        $this->type = $type;
    }

    public function collection(Collection $rows)
    {
        $headers = $rows->first()->filter(function ($header) {
            return $header !== null;
        })->values();

        $levels = $headers->count();

        foreach ($rows as $row) {
            if ($row[0] === 'Level 1') {
                continue; // Skip header
            }

            $parentId = null;
            for ($i=0; $i < $levels; $i++) {
                $level = trim($row[$i]);
                
                if ($level) {
                    $category = Category::firstOrCreate([
                        'name' => $level,
                        'parent_id' => $parentId,
                        'level' => $this->service->getNewLevel($parentId),
                        'type' => $this->type,
                    ]);
                    $parentId = $category->id;
                }
            }
        }
    }
}
