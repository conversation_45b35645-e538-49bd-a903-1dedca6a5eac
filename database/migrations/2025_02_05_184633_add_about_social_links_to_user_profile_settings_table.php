<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_profile_settings', function (Blueprint $table) {
            $table->foreignId('user_id')->constrained('users')->after('id');
            $table->text('about_me')->nullable()->after('user_id');
            $table->string('linkedin_link')->nullable()->after('about_me');
            $table->string('facebook_link')->nullable()->after('linkedin_link');
            $table->string('instagram_link')->nullable()->after('facebook_link');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_profile_settings', function (Blueprint $table) {
            //
        });
    }
};
