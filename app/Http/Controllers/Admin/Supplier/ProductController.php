<?php

namespace App\Http\Controllers\Admin\Supplier;

use App\Models\Product;
use App\Models\User;
use App\Services\ProductService;
use Illuminate\Support\Facades\Gate;

class ProductController extends BaseProductController
{
    /**
     * Create a new controller instance.
     *  
     * @param ProductService $service
     * @return void
     */
    public function __construct(ProductService $service)
    {
        $role = User::ROLE_SUPPLIER;
        $routePrefix = 'admin.products.';
        $title = '';
        
        parent::__construct($service, $role, $routePrefix, $title); 
    }


    public function show(Product $product)
    {
        if (!Gate::any(['manage suppliers', 'view suppliers'])) {
            abort(403);
        }

        return $this->parseShow($product);
    }
}
