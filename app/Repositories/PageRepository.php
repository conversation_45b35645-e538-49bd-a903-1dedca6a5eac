<?php

namespace App\Repositories;

use App\Models\Page;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class PageRepository
 *
 * Handles data operations for the Page model.
 */
class PageRepository extends BaseRepository
{
    /**
     * The Page model instance.
     *
     * @var \App\Models\Page
     */
    protected $model;

    /**
     * PageRepository constructor.
     *
     * @param \App\Models\Page $page
     */
    public function __construct(Page $page)
    {
        $this->model = $page;
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return Collection
     */
    public function getByMixedCondition(array $params) : Collection
    {
        return $this->queryByMixedCondition($params)->get();
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return Builder
     */
    public function queryByMixedCondition(array $params) : Builder
    {
        $query = $this->model->select();

        return $query;
    }

    public function statuses()
    {
       return [
            'Publish' => $this->model::PUBLISHED,
            'Draft' => $this->model::DRAFTED,
       ];
    }
}
