<?php

namespace Database\Factories;

use App\Models\CompanyBusinessSector;
use App\Models\Company;
use App\Models\BusinessSector;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyBusinessSector>
 */
class CompanyBusinessSectorFactory extends Factory
{
    protected $model = CompanyBusinessSector::class;

    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'business_sector_id' => BusinessSector::factory(),
        ];
    }
}
