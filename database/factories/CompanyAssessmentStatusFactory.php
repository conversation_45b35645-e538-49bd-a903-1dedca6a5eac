<?php

namespace Database\Factories;

use App\Models\CompanyAssessmentStatus;
use App\Models\CompanyAssessment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyAssessmentStatus>
 */
class CompanyAssessmentStatusFactory extends Factory
{
    protected $model = CompanyAssessmentStatus::class;

    public function definition(): array
    {
        return [
            'assessment_id' => CompanyAssessment::factory(),
            'updated_by' => User::factory(),
            'status' => $this->faker->randomElement([1, 2, 3]), // Replace with actual statuses
        ];
    }
}
