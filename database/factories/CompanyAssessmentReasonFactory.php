<?php

namespace Database\Factories;

use App\Models\CompanyAssessmentReason;
use App\Models\CompanyAssessment;
use App\Models\AssessmentSection;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyAssessmentReason>
 */
class CompanyAssessmentReasonFactory extends Factory
{
    protected $model = CompanyAssessmentReason::class;

    public function definition(): array
    {
        return [
            'assessment_id' => CompanyAssessment::factory(),
            'section_id' => AssessmentSection::factory(),
            'answered_by' => User::factory(),
            'answer' => $this->faker->sentence,
        ];
    }
}
