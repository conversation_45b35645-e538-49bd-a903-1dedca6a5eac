<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('key_features', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->string('feature'); // Feature name or description
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('key_features');
    }
};

