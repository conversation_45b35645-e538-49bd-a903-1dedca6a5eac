<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\ProductRepository as Repository;
use App\Traits\DBService;
use Illuminate\Support\Facades\Auth;

class ProductService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

    /*
     * Product Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get current loggedin user
     *
     * @return User
     */
    public function current(): User
    {
        return Auth::user();
    }

    public function getListForAdmin(string $status = null, string $type = 'product')
    {
        $conditions = ['type' => $type];
        if (!is_null($status)) {
            $conditions['status'] = $status;
        }


        return $this->repository->getByMixedCondition($conditions);
    }


    /**
     * Get list of all the statuses
     *
     * @return array
     */
    public function getStatuses()
    {
        return $this->repository->statuses();
    }
}
