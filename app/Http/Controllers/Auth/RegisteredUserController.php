<?php

namespace App\Http\Controllers\Auth;

use App\Http\Actions\RegisteredUserAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisteredUserRequest;
use App\Models\Company;
use App\Models\OTP;
use App\Models\User;
use App\Models\UserCompany;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;
use Exception;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(RegisteredUserRequest $request, RegisteredUserAction $action)
    {
        $data = $request->validated();
        try{

            $action($data);
            return \Redirect::back()
                   ->with('modalToShow', $request->nextModalToDisplay());

        }catch (Exception $e) {
            return redirect()->back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', config('settings.modal.signup'));
        }

    }

    /**
     * To show the register modal
     *
     *
     */
    public function show()
    {
        $this->handleUserDestroy(User::where('id', Session::get('registeredUserId'))->first());
        Session::forget('registeredUserId');
        return \Redirect::back()
            ->with('modalToShow', config('settings.modal.signup'));
    }

    /**
     * To delete the user from database and session
     *
     *
     */
    public function handleUserDestroy($email, $user = null){
        $user = $user ?? User::where('email', $email)->withTrashed()->whereNull('username')->first();
        if(isset($user) && $user->is_user){
            $userCompany = UserCompany::where('user_id', $user->id)->first();
            if($userCompany){
                $userCompany->delete();
            }
            if($user->categories()){
                $user->categories()->detach();
            }
            OTP::where('user_id', $user->id)->delete();
            $user->forceDelete();
            Session::forget('registeredUserId');
        }

    }
}
