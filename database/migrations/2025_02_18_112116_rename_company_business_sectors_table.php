<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
        // Update foreign key constraints
        Schema::table('company_business_sectors', function (Blueprint $table) {
            $table->dropForeign(['business_sector_id']); // Drop old foreign key
            $table->dropColumn('business_sector_id');
        });

        // Rename the table
        Schema::rename('company_business_sectors', 'company_business_categories');

        Schema::table('company_business_categories', function (Blueprint $table) {
            // Add the new columns
            $table->unsignedBigInteger('level_2_category_id')->nullable()->after('company_id');
            $table->unsignedBigInteger('level_3_category_id')->nullable()->after('level_2_category_id');

            // Add foreign key constraints
            $table->foreign('level_2_category_id')->references('id')->on('categories')->nullOnDelete();
            $table->foreign('level_3_category_id')->references('id')->on('categories')->nullOnDelete();

        });

        // Droping this because it is not needed.
        Schema::dropIfExists('business_sectors');



    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        
        // Revert changes to the renamed table
        Schema::table('company_business_categories', function (Blueprint $table) {
            // Drop the new foreign key constraints
            $table->dropForeign(['level_2_category_id']);
            $table->dropForeign(['level_3_category_id']);

            // Drop the new columns
            $table->dropColumn('level_2_category_id');
            $table->dropColumn('level_3_category_id');
        });

        // Rename the table back to its original name
        Schema::rename('company_business_categories', 'company_business_sectors');

        // Recreate the dropped column and foreign key in the original table
        Schema::table('company_business_sectors', function (Blueprint $table) {
            $table->unsignedBigInteger('business_sector_id')->after('company_id');

            // Re-add the foreign key for 'business_sector_id'
            $table->foreign('business_sector_id')->references('id')->on('business_sectors')->cascadeOnDelete();
        });
    }

};
