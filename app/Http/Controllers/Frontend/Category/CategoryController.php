<?php

namespace App\Http\Controllers\Frontend\Category;

use App\Http\Actions\CompanyRegistrationAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\CreateCompanyAddressRequest;
use App\Http\Requests\Frontend\CreateCompanyEmailRequest;
use App\Http\Requests\Frontend\CreateCompanyRequest;
use App\Models\Category;
use App\Models\CompanyRegistration;
use App\Models\Country;
use App\Models\OrganisationSize;
use App\Models\OrganisationType;
use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class CategoryController extends Controller
{
    public function getCategoriesBySector($businessType, $level, $parent=null) : JsonResponse{
        if (empty($businessType)) {
            return response()->json(collect()); // Return an empty collection
        }

        $categories = Category::with(['children'])->where([
            'level' => $level,
            'type' => $businessType,
            'status' => 1
        ])->
        when($parent, function($query, $parent){
           $query->where('parent_id', $parent);
        })
        ->orderBy('name', 'ASC')
        ->get();
        return response()->json($categories);
    }

    public function getCategoriesData($businessType, $level, $parent=null)
    {
        if (empty($businessType)) {
            return collect(); // Return an empty collection
        }

        return Category::where([
            'level' => $level,
            'type' => $businessType,
            'status' => 1
        ])->
        when($parent, function($query, $parent){
           $query->where('parent_id', $parent);
        })->get();
    }
    
}
