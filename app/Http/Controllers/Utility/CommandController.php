<?php

namespace App\Http\Controllers\Utility;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Http\JsonResponse;

/**
 * Class CommandController
 *
 * This controller handles the execution of specific Artisan commands.
 * It ensures only a predefined set of commands can be run for security purposes.
 *
 * @package App\Http\Controllers\Utility
 */
class CommandController extends Controller
{
    /**
     * Run a specified Artisan command.
     *
     * This method validates and executes a predefined Artisan command.
     * Only commands listed in the allowedCommands array can be executed.
     *
     * @param string $command The key representing the Artisan command to be executed.
     *                        Example values: 'cache_clear', 'config_clear'.
     * @return JsonResponse Returns a JSON response containing the command output or error message.
     */
    public function runCommand(string $command): JsonResponse
    {
        /**
         * List of allowed commands mapped to their Artisan equivalents.
         * This prevents arbitrary commands from being executed, enhancing security.
         */
        $allowedCommands = [
            'cache_clear' => 'cache:clear',
            'config_clear' => 'config:clear',
            'view_clear' => 'view:clear',
            'route_cache' => 'route:cache',
            'route_clear' => 'route:clear',
            'migrate' => 'migrate',
            'queue_work' => 'queue:work',
            'storage_link' => 'storage:link'
        ];

        // Validate if the provided command is allowed
        if (!isset($allowedCommands[$command])) {
            return response()->json(['error' => 'Command not allowed'], 403);
        }

        // Attempt to run the Artisan command
        try {
            Artisan::call($allowedCommands[$command]);
            $output = Artisan::output();

            return response()->json([
                'message' => 'Command executed successfully',
                'output' => $output,
            ]);
        } catch (\Exception $e) {
            // Handle any exceptions that occur during command execution
            return response()->json([
                'error' => 'Failed to execute command',
                'details' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Run the database seed command.
     *
     * This method runs the `db:seed` Artisan command to populate the database with initial data.
     * Optionally, a specific seeder class can be provided.
     *
     * @param string|null $seeder Optional seeder class to run (e.g., 'UserSeeder').
     * @return JsonResponse Returns a JSON response containing the command output or error message.
     */
    public function runSeeder(?string $seeder = null): JsonResponse
    {
        try {
            // Prepare command with optional seeder class
            $parameters = $seeder ? ['--class' => $seeder] : [];
            
            Artisan::call('db:seed', $parameters);
            $output = Artisan::output();

            return response()->json([
                'message' => 'Seeder executed successfully',
                'output' => $output,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to execute seeder',
                'details' => $e->getMessage(),
            ], 500);
        }
    }
}
