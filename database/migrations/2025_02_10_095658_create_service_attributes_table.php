<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('service_attributes', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('service_id'); // Foreign key to services table
            $table->string('attribute'); // Attribute name (e.g., Color, Size, Weight)
            $table->string('characteristic'); // Attribute value (e.g., Red, Medium, 2kg)
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_attributes');
    }
};
