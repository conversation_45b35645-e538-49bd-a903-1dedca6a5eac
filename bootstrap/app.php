<?php

use Illuminate\Support\MessageBag;
use Illuminate\Foundation\Application;
use App\Http\Middleware\AuthenticateAdmin;
use App\Http\Middleware\CheckSignupSession;
use App\Http\Middleware\RedirectIfNotAuthenticated;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'auth.admin' => AuthenticateAdmin::class,
            'check.signup.session' => CheckSignupSession::class,
            'auth.user' => RedirectIfNotAuthenticated::class,
            'supplier_role_or_permission' => \App\Http\Middleware\CheckFrontendPermission::class,
        ]);

        // // Define the 'web' middleware group (required for broadcasting/auth)
        // $middleware->group('web', [
        //     \Illuminate\Cookie\Middleware\EncryptCookies::class,
        //     \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
        //     \Illuminate\Session\Middleware\StartSession::class,
        //     \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        //     \Illuminate\Routing\Middleware\SubstituteBindings::class,
        //     \Illuminate\Auth\Middleware\Authenticate::class,
        // ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\HttpException $exception) {
            $status = $exception->getStatusCode();
            if ($status === 400){
                // new MessageBag()
                $data = ['errors' => 'Sorry, the server is unable to process a request from the client.', 'status' => '400 - Bad Request']; // Ensure $errors is available
                return response()->view('errors.error', $data);
            } 
            if ($status === 404){
                // new MessageBag()
                $data = ['errors' => 'Sorry, the page you are looking for does not exist.', 'status' => '404 - Page Not Found']; // Ensure $errors is available
                return response()->view('errors.error', $data);
            } 
            if ($status === 500){
                $data = ['errors' => 'Something went wrong on our end. Please try again later.', 'status' => '500 - Server Error']; // Ensure $errors is available
                return response()->view('errors.error', $data);
            }
            if ($status === 405){
                $data = ['errors' => 'You have requested for incorect route type.', 'status' => '405 - Bad Method Request']; // Ensure $errors is available
                return response()->view('errors.error', $data);
            }
        });
    })->create();
