document.addEventListener("DOMContentLoaded", function(){
    document.querySelectorAll('.adm-sidebar .sidebar ul.nav > li >a.nav-link').forEach(function(element){
      
      element.addEventListener('click', function (e) {
  
        let nextEl = element.nextElementSibling;
        let parentEl  = element.parentElement;	
  
          if(nextEl) {
              e.preventDefault();	
              let mycollapse = new bootstrap.Collapse(nextEl);
              if(nextEl.classList.contains('show')){
                mycollapse.hide();
            } else {
                mycollapse.show();
                jQuery('.adm-sidebar .sidebar li.has-submenu').removeClass('open');
                jQuery(parentEl).addClass('open');
                // find other submenus with class=show
                var opened_submenu = parentEl.parentElement.querySelector('.submenu.show');
                // if it exists, then close all of them
                if(opened_submenu){
                    new bootstrap.Collapse(opened_submenu);
                  }
              }
          }
      }); // addEventListener
    }) // forEach
  });


  jQuery('.adm_sidemenu_toggle').on('click',function(){
        jQuery(this).toggleClass('toggled');
        jQuery('.adm-sidebar').toggleClass('closed');
        jQuery('.adm-sidebar').toggleClass('m-view-hide');
        jQuery('.adm-sidebar').toggleClass('m-view-show');
        jQuery('.adm_content_box').toggleClass('with_sidebar');
  });

jQuery(document).ready(function(){
  jQuery('.adm-sidebar nav > ul.nav > li > ul.submenu > li > a').each(function () {
      if (jQuery(jQuery(this))[0].href == String(window.location)){
          jQuery(this).parent().parent().parent().addClass('open');
          jQuery(this).parent().parent().addClass('show');
          jQuery(this).parent().addClass('active');
      }
  });
  jQuery('.adm-sidebar nav > ul.nav > li > ul.submenu > li > ul > li > a ').each(function () {
      if (jQuery(jQuery(this))[0].href == String(window.location)){
          jQuery(this).parent().parent().parent().parent().parent().addClass('open');
          jQuery(this).parent().parent().parent().parent().addClass('show');
          jQuery(this).parent().parent().parent().addClass('active');
          jQuery(this).parent().addClass('active');
      }
  });

  if(jQuery('.progres-bar').length){
    jQuery('.progres-bar').each(function(){
      let min_val=jQuery(this).children().children('.progress').attr('aria-valuemin');
      let current_val=jQuery(this).children().children('.progress').attr('aria-valuenow');
      jQuery(this).children().children().children('.progress-bar').css('width',current_val+'%');
      if(current_val< min_val){
        jQuery(this).children().children().children('.progress-bar').addClass('danger');
      }
      $('head').append('<style>.progress:before{left:'+min_val+'% !important;}</style>');
    });
  }
})