<?php

namespace Database\Seeders;

use App\Models\PlanFeature;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FeatureSeeder extends Seeder
{
    public function run()
    {
        $features = [
            [
                'name' => 'Add Users to Company Profile',
                'description' => 'Allows admins to add additional users to the company profile. Max number varies by subscription plan.',
            ],
            [
                'name' => 'Chat Access',
                'description' => 'Grants access to the Chat tab in ConnectHUB. Only one non-Super Admin user can have Chat Access = Yes at a time.',
            ],
            [
                'name' => 'Inbox Access',
                'description' => 'Grants access to Inbox tab in ConnectHUB. All users with active dashboard access can have Inbox access. No limit unless restricted by subscription.',
            ],
            [
                'name' => 'Honley Support Chat',
                'description' => 'Allows users to initiate a direct chat with the Honley support team via ConnectHUB from the Company Dashboard. Chat',
            ],
            [
                'name' => 'Add Company Awards',
                'description' => "Allows users to add company awards to their profile under the 'Achievements' tab. Awards are displayed publicly on the company's front-end profile page.",
            ],
            [
                'name' => 'Add Company Case Studies',
                'description' => "Allows users to add case studies to their profile under the 'Achievements' tab. Case studies are displayed publicly on the company's front-end profile page.",
            ],
            [
                'name' => 'Company Assessment',
                'description' => "Allows users to complete a company assessment questionnaire via the [Assess Profile] button. Once submitted and approved by Honley Admin, the company's profile shows the '✔️✔️ Assessed' badge for 12 months.",
            ],
            [
                'name' => 'Product and Service Listings',
                'description' => 'Allows companies to create and manage product or service listings under their profile. Listings are displayed publicly after activation. Limits apply by subscription plan.',
            ],
            [
                'name' => 'Advertisement Upload',
                'description' => "Allows companies to upload an advertisement image (available in the 'Logo & Advertising' tab) to promote their company and display it prominently in search results.",
            ],
            [
                'name' => 'Additional Business Sectors',
                'description' => 'Allows companies to associate additional business sectors (categories) with their profile for wider search exposure. (Manage via ‘Overview’ tab)',
            ],
            [
                'name' => 'Additional Locations',
                'description' => 'Allows companies to add multiple office/operational addresses to their profile to improve visibility in search results. (Manage via ‘Locations’ tab.)',
            ],
        ];
        

        $data = [];

        foreach ($features as $feature) {
            $data[] = [
                'name' =>  $feature['name'],
                'slug' => Str::slug( $feature['name']),
                'description' => $feature['description'],
                'feature_type' => PlanFeature::FEATURE_TYPE_PLATFORM,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        DB::table('plan_features')->insert($data);
    }
}
