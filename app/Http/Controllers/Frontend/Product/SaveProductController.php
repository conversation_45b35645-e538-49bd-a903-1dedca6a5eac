<?php

namespace App\Http\Controllers\Frontend\Product;

use App\Models\User;
use App\Models\Company;
use App\Models\Country;
use App\Models\Product;
use App\Models\Category;
use App\Models\SaveProduct;
use App\Services\IPService;
use Faker\Factory as Faker;
use Illuminate\Support\Str;
use App\Models\LocationType;
use Illuminate\Http\Request;
use OpenSearch\ClientBuilder;
use App\Models\ProductPricing;
use App\Models\OrganisationSize;
use App\Models\OrganisationType;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth; // For accessing the logged-in user

class SaveProductController extends Controller
{
    protected $ipApi;

    /**
     * Create a new instance of the controller.
     *
     * @param IpService $ipService
     */
    public function __construct(IPService $ipApi)
    {
        $this->ipApi = $ipApi;
    }
    /**
     * Display a listing of products and services with search and filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $getData = $this->ipApi->getSearchLocation();
        $queryParams = $getData['queryParams'];
        $location = $getData['location'];
        $dropdownItems = config('dropdown.items');
        // Fetch all categories for filtering
        $categories = Category::all();

        $sort = $request->input('sort', '');
        $filter = $request->input('filter', '');
        $quickFilter = request('quick_filter');
        $perPage = config('services.per_page.default'); // Default items per page

        // Perform search with location filter
        $rawResults = SaveProduct::search($queryParams)->raw();
        // Initialize typeCounts array
        $typeCounts = $rawResults['typeCounts'];
        
        // Extract total counts for each type
        $totalProducts = $typeCounts['product'] ?? 0;
        $totalServices = $typeCounts['service'] ?? 0;
        // Extract all product IDs from rawResults
        $productIds = collect($rawResults['results'])->pluck('_source.product_id')->toArray();
        
        // Convert raw results into Laravel Collection for pagination
        if($quickFilter) {
            // Fetch products from the Product model using the extracted IDs
            $searchResults = Product::whereIn('id', $productIds)->where('type', $quickFilter);
        } else {
            // Fetch products from the Product model using the extracted IDs
            $searchResults = Product::whereIn('id', $productIds);
        }
        $idsString = implode(',', $productIds);
        if ($idsString !== '') {
            // Ensure $idsString is properly escaped and safe
            $searchResults = $searchResults->orderByRaw("FIELD(id, $idsString)");
        }
        $searchResults = $searchResults->paginate($perPage);

        // Attach additional metadata
        $searchResults->totalProducts = $totalProducts;
        $searchResults->totalServices = $totalServices;

        $products = $searchResults;

        $countryFilterItems = [];

        if ($products) {
            foreach ($products as $product) {
                foreach ($product->company->company_locations ?? [] as $thisLocation) {
                    $code = $thisLocation->geo_country->code ?? null;
                    if ($code && !in_array($code, $countryFilterItems)) {
                        $countryFilterItems[] = $code;
                    }
                }
            }
        }

        // Return the view with search results
        return view('frontend.product.index', compact('products', 'categories', 'dropdownItems', 'countryFilterItems', 'queryParams', 'location'));
    }

    /**
     * Save a product for the current logged-in user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveProduct(Request $request)
    {
        // Ensure the user is logged in
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
        }

        // Get the logged-in user
        $user = Auth::user();

        // Validate the request
        $request->validate([
            'product_id' => 'required|exists:products,id', // Ensure product_id exists in the products table
        ]);

        // Check if the product is already saved
        $alreadySaved = SaveProduct::where('user_id', $user->id)
            ->where('product_id', $request->product_id)
            ->exists();

        if ($alreadySaved) {
            return response()->json(['success' => false, 'message' => 'Product already saved.']);
        }

        // Save the product
        SaveProduct::create([
            'user_id' => $user->id,
            'product_id' => $request->product_id,
        ]);

        return response()->json(['success' => true, 'message' => 'Product saved successfully.']);
    }

    /**
     * Unsave a product for the current logged-in user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unsaveProduct(Request $request)
    {
        // Ensure the user is logged in
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
        }

        // Get the logged-in user
        $user = Auth::user();

        // Validate the request
        $request->validate([
            'product_id' => 'required|exists:products,id', // Ensure product_id exists in the products table
        ]);

        // Check if the product is saved
        $savedProduct = SaveProduct::where('user_id', $user->id)
            ->where('product_id', $request->product_id)
            ->first();

        if (!$savedProduct) {
            return response()->json(['success' => false, 'message' => 'Product not found in saved list.']);
        }

        // Delete the saved product
        $savedProduct->delete();

        return response()->json(['success' => true, 'message' => 'Product unsaved successfully.']);
    }
}
