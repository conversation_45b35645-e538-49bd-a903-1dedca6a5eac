<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\PasswordResetEmailRequest;
use App\Mail\EmailVerificationOtpMail;
use App\Models\OTP;
use App\Models\User;
use App\Services\OtpService;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     */
    public function create(): View
    {
        return view('auth.forgot-password');
    }

    public function showForgotPassword() : RedirectResponse {
        return \Redirect::back()
                ->with('modalToShow', config('settings.modal.forgot_password_one'));
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @param PasswordResetEmailRequest $request
     * @return RedirectResponse
     */
    public function store(PasswordResetEmailRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $user = User::firstWhere('email', $data['email']);

        if (!$user) {
            return \Redirect::back()
                ->with('modalToShow', $request->currentModalToDisplay())
                ->withInput($request->only('email'))
                ->withErrors(['email' => 'Email not found']);
        }

        $user->sendEmailVerification();
        \Session::put('email', $user->email);
        return \Redirect::back()->with('modalToShow', $request->nextModalToDisplay());
    }

    /**
     * Handle an incoming request to verify the OTP sent for password reset email.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function verify(Request $request): RedirectResponse
    {
        $request->validate([
            'otp' => ['required', 'array', 'size:6'],
            'otp.*' => ['required', 'numeric', 'digits:1'],
        ]);

        $currentModal = config('settings.modal.forgot_password_two');
        $nextModal = config('settings.modal.forgot_password_three');

        $code = (int) implode('', $request->otp);
        $email = \Session::get('email');

        $otp = OTP::firstWhere(['email' => $email, 'code' => $code]);

        if (!$otp) {
            return \Redirect::back()->withErrors(['otp' => 'Invalid OTP'])
                ->with('modalToShow', $currentModal);
        }
        if (Carbon::now()->gt($otp->expires_at)) {
            return \Redirect::back()->withErrors(['otp' => 'OTP has expired'])
                ->with('modalToShow', $currentModal);
        }

        $otp->delete();
        return \Redirect::back()
            ->with('modalToShow', $nextModal);
    }
}
