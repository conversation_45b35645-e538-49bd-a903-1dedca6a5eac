<?php

namespace App\Http\Controllers\Frontend\Location;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\MapboxService;

class LocationController extends Controller
{
    public function getSuggestions(Request $request, MapboxService $mapbox)
    {
        $request->validate([
            'query' => 'required|string|min:2',
        ]);

        return response()->json($mapbox->getCitySuggestions($request->query('query')));
    }
}

