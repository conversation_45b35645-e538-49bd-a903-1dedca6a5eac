<?php

namespace App\Http\Actions;

use App\Models\User;
use Illuminate\Support\Facades\Session;
use <PERSON><PERSON>\Socialite\Contracts\User as SocialUser;
use <PERSON><PERSON>\Socialite\Facades\Socialite;

class SocialAuthAction
{
    /**
     * Execute the social authentication action.
     *
     * @param SocialUser $social
     * @param string $platform
     * @return User
     */
    public function execute(SocialUser $social, string $platform): User
    {
        $attribute = (string) $platform . '_id';

        $user = User::where('email', $social->email)
            ->orWhere($attribute, $social->id)
            ->first();

        if ($user && is_null($user->$attribute)) {
            $user->update([$attribute => $social->id]);
        }
        if (!$user) {
            $user = \DB::transaction(function () use ($social, $attribute) {

                $name = explode(' ', $social->name);
                $user = User::create([
                    'first_name' => $name[0] ?? null,
                    'last_name' => $name[1] ?? null,
                    'email' => $social->email,
                    $attribute => $social->id,
                ]);

                $user->markEmailAsVerified();
                $user->assignRole(User::ROLE_USER);
                return $user;
            });
        }

        \Auth::login($user);
        Session::forget('registeredUserId');
        return $user;
    }
}
