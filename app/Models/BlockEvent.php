<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BlockEvent extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'blocker_type',
        'blocker_id',
        'blocked_type',
        'blocked_id',
        'action',
        'action_at',
    ];



    public function blocker()
    {
        return $this->morphTo('blocker');
    }

    public function blocked()
    {
        return $this->morphTo('blocked');
    }
}
