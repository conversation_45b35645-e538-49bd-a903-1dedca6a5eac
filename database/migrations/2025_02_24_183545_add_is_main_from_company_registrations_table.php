<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_registrations', function (Blueprint $table) {
            $table->dropForeign(['level_3_category_id']);
            $table->dropColumn('level_3_category_id');
            $table->unsignedBigInteger('country_id')->nullable()->change();
            $table->renameColumn('level_2_category_id', 'business_sector_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_registrations', function (Blueprint $table) {
            //
        });
    }
};
