<?php

namespace App\Repositories;

use App\Models\SubscriptionFeature;

/**
 * Class SubscriptionFeatureRepository
 *
 * Handles data operations for the SubscriptionFeature model.
 */
class SubscriptionFeatureRepository extends BaseRepository
{
    /**
     * The SubscriptionFeature model instance.
     *
     * @var \App\Models\SubscriptionFeature
     */
    protected $model;

    /**
     * SubscriptionFeatureRepository constructor.
     *
     * @param \App\Models\SubscriptionFeature $subscriptionFeature
     */
    public function __construct(SubscriptionFeature $subscriptionFeature)
    {
        $this->model = $subscriptionFeature;
    }
}
