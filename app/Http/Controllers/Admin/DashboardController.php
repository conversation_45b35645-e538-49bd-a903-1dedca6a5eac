<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GlobalSetting;
use App\Models\Products;
use App\Models\Quotation;
use Illuminate\Http\Request;
use App\Models\UserAttributes;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Stevebauman\Location\Facades\Location;

class DashboardController extends Controller
{
    protected $barChartMonthCount;

    public function __construct()
    {
        // Select last showing month numbers for BarChart record
        $this->barChartMonthCount = 3;
    }
    public function index()
    {
        $countUser = User::withoutRole('admin')->count();

        // $pedingUserCount = User::whereHas('userAttributes', function ($query) {
        //     $query->where('status', 0);
        // })->count();

        // $approvedUserCount = User::whereHas('userAttributes', function ($query) {
        //     $query->where('status', 1);
        // })->count();

        // $pausedUserCount = User::whereHas('userAttributes', function ($query) {
        //     $query->where('status', 2);
        // })->count();

        // $deletedUserCount = User::whereHas('userAttributes', function ($query) {
        //     $query->where('status', 3);
        // })->count();

        // $blockedUserCount = User::whereHas('userAttributes', function ($query) {
        //     $query->where('status', 4);
        // })->count();

        // $businessAccountCount = User::whereHas('companyInfo', function ($query) {
        //     $query->where('user_id', '>', 0);
        // })->count();

        $pedingUserCount = null;
        $approvedUserCount = null;
        $pausedUserCount = null;
        $deletedUserCount = null;
        $blockedUserCount = null;
        $businessAccountCount = null;

        return view('admin.dashboard.index', compact('countUser', 'pedingUserCount', 'approvedUserCount', 'pausedUserCount', 'deletedUserCount', 'blockedUserCount', 'businessAccountCount'));
    }    

    // public function getUserProfilesRegister()
    // {
    //     $barChartMonthCount = $this->barChartMonthCount;
    //     $currentDate = Carbon::now()->startOfMonth();

    //     $monthNames = $ukUsersByMonth = $scotlandUsersByMonth = $irelandUsersByMonth = $ukUsersCountArray = $scotlandUsersCountArray = $irelandUsersCountArray = [];

    //     for ($i = 1; $i <= $barChartMonthCount; $i++) {
    //         $monthNumber = $currentDate->month;
    //         $year = $currentDate->format('y');
    //         $fullYear = $currentDate->format('Y');
    //         $monthName = $currentDate->format('F');
    //         $monthNames[] = $monthName . "'" . $year;

    //         //Count UK Users
    //         $ukUsersByMonth[] = User::select(
    //             DB::raw('YEAR(created_at) as year'),
    //             DB::raw('MONTH(created_at) as month'),
    //             DB::raw('COUNT(*) as user_count')
    //         )
    //             ->with('userAttributes')
    //             ->whereHas('userAttributes', function ($query) {
    //                 $query->where('country_name', '=', 'United Kingdom');
    //             })
    //             ->whereYear('created_at', $fullYear)
    //             ->whereMonth('created_at', $monthNumber)
    //             ->whereNull('deleted_at')
    //             ->groupBy('year', 'month')
    //             ->get();

    //         //Count Scotland Users
    //         $scotlandUsersByMonth[] = User::select(
    //             DB::raw('YEAR(created_at) as year'),
    //             DB::raw('MONTH(created_at) as month'),
    //             DB::raw('COUNT(*) as user_count')
    //         )
    //             ->with('userAttributes')
    //             ->whereHas('userAttributes', function ($query) {
    //                 $query->where('region_name', '=', 'Scotland');
    //             })
    //             ->whereYear('created_at', $fullYear)
    //             ->whereMonth('created_at', $monthNumber)
    //             ->whereNull('deleted_at')
    //             ->groupBy('year', 'month')
    //             ->get();


    //         //Count Scotland Users
    //         $irelandUsersByMonth[] = User::select(
    //             DB::raw('YEAR(created_at) as year'),
    //             DB::raw('MONTH(created_at) as month'),
    //             DB::raw('COUNT(*) as user_count')
    //         )
    //             ->with('userAttributes')
    //             ->whereHas('userAttributes', function ($query) {
    //                 $query->where('country_name', '=', 'Ireland');
    //             })
    //             ->whereYear('created_at', $fullYear)
    //             ->whereMonth('created_at', $monthNumber)
    //             ->whereNull('deleted_at')
    //             ->groupBy('year', 'month')
    //             ->get();

    //         $currentDate->subMonths(1)->startOfMonth();
    //     }
    //     foreach ($ukUsersByMonth as $ukUser) {
    //         if (isset($ukUser[0]->user_count)) {
    //             $ukUsersCountArray[] = $ukUser[0]->user_count;
    //         } else {
    //             $ukUsersCountArray[] = 0;
    //         }
    //     }
    //     foreach ($scotlandUsersByMonth as $scotlandUser) {
    //         if (isset($scotlandUser[0]->user_count)) {
    //             $scotlandUsersCountArray[] = $scotlandUser[0]->user_count;
    //         } else {
    //             $scotlandUsersCountArray[] = 0;
    //         }
    //     }
    //     foreach ($irelandUsersByMonth as $irelandUser) {
    //         if (isset($irelandUser[0]->user_count)) {
    //             $irelandUsersCountArray[] = $irelandUser[0]->user_count;
    //         } else {
    //             $irelandUsersCountArray[] = 0;
    //         }
    //     }
    //     return response()->json(['monthNames' => $monthNames, 'ukUsersCountArray' => $ukUsersCountArray, 'scotlandUsersCountArray' => $scotlandUsersCountArray, 'irelandUsersCountArray' => $irelandUsersCountArray]);
    // }

    // public function getProductsAndServices()
    // {
    //     $barChartMonthCount = $this->barChartMonthCount;
    //     $currentDate = Carbon::now()->startOfMonth();

    //     $monthNames = $productsByMonth = $servicesByMonth = $productCountArray = $serviceCountArray = [];

    //     for ($i = 1; $i <= $barChartMonthCount; $i++) {
    //         $monthNumber = $currentDate->month;
    //         $year = $currentDate->format('y');
    //         $fullYear = $currentDate->format('Y');
    //         $monthName = $currentDate->format('F');
    //         $monthNames[] = $monthName . "'" . $year;

    //         //Count Products
    //         $productsByMonth[] = Products::select(
    //             DB::raw('YEAR(created_at) as year'),
    //             DB::raw('MONTH(created_at) as month'),
    //             DB::raw('COUNT(*) as product_count')
    //         )
    //             ->with('category')
    //             ->whereHas('category', function ($query) {
    //                 $query->where('business_category_id', '=', '2');
    //             })
    //             ->whereYear('created_at', $fullYear)
    //             ->whereMonth('created_at', $monthNumber)
    //             ->groupBy('year', 'month')
    //             ->get();

    //         //Count Service
    //         $servicesByMonth[] = Products::select(
    //             DB::raw('YEAR(created_at) as year'),
    //             DB::raw('MONTH(created_at) as month'),
    //             DB::raw('COUNT(*) as service_count')
    //         )
    //             ->with('category')
    //             ->whereHas('category', function ($query) {
    //                 $query->where('business_category_id', '=', '1');
    //             })
    //             ->whereYear('created_at', $fullYear)
    //             ->whereMonth('created_at', $monthNumber)
    //             ->groupBy('year', 'month')
    //             ->get();

    //         $currentDate->subMonths(1)->startOfMonth();
    //     }
    //     foreach ($productsByMonth as $product) {
    //         if (isset($product[0]->product_count)) {
    //             $productCountArray[] = $product[0]->product_count;
    //         } else {
    //             $productCountArray[] = 0;
    //         }
    //     }
    //     foreach ($servicesByMonth as $service) {
    //         if (isset($service[0]->service_count)) {
    //             $serviceCountArray[] = $service[0]->service_count;
    //         } else {
    //             $serviceCountArray[] = 0;
    //         }
    //     }

    //     return response()->json(['monthNames' => $monthNames, 'productCountArray' => $productCountArray, 'serviceCountArray' => $serviceCountArray]);
    // }

    // public function getBusinessProfilesRegistered()
    // {
    //     $barChartMonthCount = $this->barChartMonthCount;
    //     $currentDate = Carbon::now()->startOfMonth();

    //     $monthNames = $businessProfilesByMonth = $businessProfileCountArray = [];

    //     for ($i = 1; $i <= $barChartMonthCount; $i++) {
    //         $monthNumber = $currentDate->month;
    //         $year = $currentDate->format('y');
    //         $fullYear = $currentDate->format('Y');
    //         $monthName = $currentDate->format('F');
    //         $monthNames[] = $monthName . "'" . $year;

    //         //Count Products
    //         $businessProfilesByMonth[] = User::select(
    //             DB::raw('YEAR(created_at) as year'),
    //             DB::raw('MONTH(created_at) as month'),
    //             DB::raw('COUNT(*) as business_profile_count')
    //         )
    //             ->role('supplier')
    //             ->whereYear('created_at', $fullYear)
    //             ->whereMonth('created_at', $monthNumber)
    //             ->whereNull('deleted_at')
    //             ->groupBy('year', 'month')
    //             ->get();


    //         $currentDate->subMonths(1)->startOfMonth();
    //     }
    //     foreach ($businessProfilesByMonth as $businessProfile) {
    //         if (isset($businessProfile[0]->business_profile_count)) {
    //             $businessProfileCountArray[] = $businessProfile[0]->business_profile_count;
    //         } else {
    //             $businessProfileCountArray[] = 0;
    //         }
    //     }

    //     return response()->json(['monthNames' => $monthNames, 'businessProfileCountArray' => $businessProfileCountArray]);
    // }

    // public function getSubscriptionsRegistered()
    // {
    //     $barChartMonthCount = $this->barChartMonthCount;
    //     $currentDate = Carbon::now()->startOfMonth();

    //     $monthNames = $subscriptionsByMonth = $subscriptionCountArray = [];

    //     for ($i = 1; $i <= $barChartMonthCount; $i++) {
    //         $monthNumber = $currentDate->month;
    //         $year = $currentDate->format('y');
    //         $fullYear = $currentDate->format('Y');
    //         $monthName = $currentDate->format('F');
    //         $monthNames[] = $monthName . "'" . $year;

    //         //Count Products
    //         $subscriptionsByMonth[] = User::select(
    //             DB::raw('YEAR(subscriptions.created_at) as year'),
    //             DB::raw('MONTH(subscriptions.created_at) as month'),
    //             DB::raw('COUNT(*) as subscription_count')
    //         )
    //             ->leftJoin('subscriptions', 'users.id', '=', 'subscriptions.user_id')
    //             ->whereYear('subscriptions.created_at', $fullYear)
    //             ->whereMonth('subscriptions.created_at', $monthNumber)
    //             ->groupBy('year', 'month')
    //             ->get();
    //         $currentDate->subMonths(1)->startOfMonth();
    //     }
    //     foreach ($subscriptionsByMonth as $subscription) {
    //         if (isset($subscription[0]->subscription_count)) {
    //             $subscriptionCountArray[] = $subscription[0]->subscription_count;
    //         } else {
    //             $subscriptionCountArray[] = 0;
    //         }
    //     }

    //     return response()->json(['monthNames' => $monthNames, 'subscriptionCountArray' => $subscriptionCountArray]);
    // }

    // public function getUsersAndBusinessProfilesTotal()
    // {
    //     $barChartMonthCount = $this->barChartMonthCount;
    //     $currentDate = Carbon::now()->startOfMonth();

    //     $monthNames = $businessProfilesByMonth = $businessProfileCountArray = $usersByMonth = $userProfileCountArray = [];

    //     //Count UK Users
    //     $ukUsersCount = User::select(
    //         DB::raw('COUNT(*) as uk_user_count')
    //     )
    //         ->with('userAttributes')
    //         ->whereHas('userAttributes', function ($query) {
    //             $query->where('country_name', '=', 'United Kingdom');
    //         })
    //         ->role('user')
    //         ->whereNull('deleted_at')
    //         ->get();

    //     $ukBusinessProfilesCount = User::select(
    //         DB::raw('COUNT(*) as uk_business_profile_count')
    //     )
    //         ->with('userAttributes')
    //         ->whereHas('userAttributes', function ($query) {
    //             $query->where('country_name', '=', 'United Kingdom');
    //         })
    //         ->role('supplier')
    //         ->whereNull('deleted_at')
    //         ->get();

    //     //Count Scotland Users
    //     $scotlandUsersCount = User::select(
    //         DB::raw('COUNT(*) as scotland_user_count')
    //     )
    //         ->with('userAttributes')
    //         ->whereHas('userAttributes', function ($query) {
    //             $query->where('region_name', '=', 'Scotland');
    //         })
    //         ->role('user')
    //         ->whereNull('deleted_at')
    //         ->get();

    //     $scotlandBusinessCount = User::select(
    //         DB::raw('COUNT(*) as scotland_business_profile_count')
    //     )
    //         ->with('userAttributes')
    //         ->whereHas('userAttributes', function ($query) {
    //             $query->where('region_name', '=', 'Scotland');
    //         })
    //         ->role('supplier')
    //         ->whereNull('deleted_at')
    //         ->get();


    //     //Count Ireland Users
    //     $irelandUsersCount = User::select(
    //         DB::raw('COUNT(*) as ireland_user_count')
    //     )
    //         ->with('userAttributes')
    //         ->whereHas('userAttributes', function ($query) {
    //             $query->where('country_name', '=', 'Ireland');
    //         })
    //         ->role('user')
    //         ->whereNull('deleted_at')
    //         ->get();

    //     $irelandBusinessUserCount = User::select(
    //         DB::raw('COUNT(*) as ireland_business_profile_count')
    //     )
    //         ->with('userAttributes')
    //         ->whereHas('userAttributes', function ($query) {
    //             $query->where('country_name', '=', 'Ireland');
    //         })
    //         ->role('supplier')
    //         ->whereNull('deleted_at')
    //         ->get();

    //     $totalUsersCount = User::select(
    //         DB::raw('COUNT(*) as total_user_count')
    //     )
    //         ->role('user')
    //         ->whereNull('deleted_at')
    //         ->get();
    //     $totalBusinessUserCount = User::select(
    //         DB::raw('COUNT(*) as total_business_count')
    //     )
    //         ->role('supplier')
    //         ->whereNull('deleted_at')
    //         ->get();


    //             $users[] = $ukUsersCount[0]->uk_user_count;
    //             $users[] = $scotlandUsersCount[0]->scotland_user_count;
    //             $users[] = $irelandUsersCount[0]->ireland_user_count;
    //             $users[] = $totalUsersCount[0]->total_user_count;

    //             $businessProfiles[] = $ukBusinessProfilesCount[0]->uk_business_profile_count;
    //             $businessProfiles[] = $scotlandBusinessCount[0]->scotland_business_profile_count;
    //             $businessProfiles[] = $irelandBusinessUserCount[0]->ireland_business_profile_count;
    //             $businessProfiles[] = $totalBusinessUserCount[0]->total_business_count;


    //             $places= ['UK', 'Scotland', 'Ireland', 'Total'];

    //         // dd($users, $businessProfiles, $places);
    //     return response()->json(['places' => $places, 'users' => $users, 'businessProfiles' => $businessProfiles]);
    // }


    // public function getQuotesRequested()
    // {
    //     $barChartMonthCount = $this->barChartMonthCount;
    //     $currentDate = Carbon::now()->startOfMonth();

    //     $monthNames = $quotationsByMonth = $quotationCountArray = [];

    //     for ($i = 1; $i <= $barChartMonthCount; $i++) {
    //         $monthNumber = $currentDate->month;
    //         $year = $currentDate->format('y');
    //         $fullYear = $currentDate->format('Y');
    //         $monthName = $currentDate->format('F');
    //         $monthNames[] = $monthName . "'" . $year;

    //         //Count Products
    //         $quotationsByMonth[] = Quotation::select(
    //             DB::raw('YEAR(created_at) as year'),
    //             DB::raw('MONTH(created_at) as month'),
    //             DB::raw('COUNT(*) as quotation_count')
    //         )
    //             ->whereYear('created_at', $fullYear)
    //             ->whereMonth('created_at', $monthNumber)
    //             ->groupBy('year', 'month')
    //             ->get();


    //         $currentDate->subMonths(1)->startOfMonth();
    //     }
    //     foreach ($quotationsByMonth as $quotation) {
    //         if (isset($quotation[0]->quotation_count)) {
    //             $quotationCountArray[] = $quotation[0]->quotation_count;
    //         } else {
    //             $quotationCountArray[] = 0;
    //         }
    //     }


    //     return response()->json(['monthNames' => $monthNames, 'quotationCountArray' => $quotationCountArray]);
    // }
}
