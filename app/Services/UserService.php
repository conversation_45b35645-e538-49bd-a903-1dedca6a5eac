<?php

namespace App\Services;

use App\Repositories\UserRepository as Repository;
use App\Traits\DBService;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class UserService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

     /*
     * User Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }
    
    /**
     * Get current loggedin user
     *
     * @return App\Models\User
     */
    public function current()
    {
        return Auth::user();
    }

    public function getListForAdmin(string $status = null, string $role = null, array|int $exceptUser = null)
    {
        $conditions = [];
        if (!is_null($status)) {
            $conditions['status'] = $status;
        }

        if ($role) {
            $conditions['role'] = $role;
        }

        if ($exceptUser) {
            $conditions['except_user'] = $exceptUser;
        }

        return $this->repository->getByMixedCondition($conditions);
    }
    
    public function setPermissions(User $user, array $permissions = [])
    {
        $user->syncPermissions($permissions);
    }

    public function getPermissions(User $user)
    {
        return $user->getPermissionNames()->toArray();
    }

    /**
     * Get list of all the statuses
     *
     * @return array
     */
    public function getStatuses()
    {
        return $this->repository->statuses();
    }
}
