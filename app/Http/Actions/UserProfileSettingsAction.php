<?php

namespace App\Http\Actions;

use App\Models\OTP;
use App\Models\User;
use App\Models\UserProfileSetting;
use Carbon\Carbon;
use Illuminate\Support\Facades\Session;

class UserProfileSettingsAction
{

    /**
     * Handles the submission of profile settings.
     *
     * @param array $data for profile settings.
     * @param $userId $userProfileSetting The user instance.
     * @return $userProfileSetting
     */
    public function executeProfileSettings(array $data, $userId): UserProfileSetting
    {
        return UserProfileSetting::updateOrCreate(
            [
                'user_id' => $userId,
            ],
            $data
        );
    }

    public function sendUpdateEmailVerification(array $data): User
    {
        $user = auth()->user();
        Session::put('verifyType', 'emailUpdate');
        $user->user_contact_history()->create([
            'user_id' => auth()->user()->id,
            'contact_type' => 'email',
            'old_value' => $user->email,
            'new_value' => $data['email'],
            'status' => 'pending',
            'user_ip_address' => request()->ip(),
            'requested_at' => now(),
            'is_main' => 0
        ]);
        $user->sendEmailVerification($data['email']);
        return $user;
    }

    public function storeUpdateDetails($data) : OTP {
        $user = auth()->user();
        $user->user_contact_history()->create([
            'user_id' => auth()->user()->id,
            'contact_type' => 'phone',
            'old_value' => $user->phone,
            'new_value' => $data['phone_prefix'].$data['number'],
            'status' => 'pending',
            'user_ip_address' => request()->ip(),
            'requested_at' => now(),
            'is_main' => 0
        ]);
        $duration = (int) config('settings.otp_expire_time');
        return OTP::updateOrCreate(
            [
                'user_id' => auth()->user()->id,
            ],
            [
                'phone' => $data['phone_prefix'].$data['number'],
                'code' => rand(100000, 999999),
                'expires_at' => Carbon::now()->addMinutes($duration)
            ]
        );
    }

    public function sendLinkToOauthUser(){
        $user = auth()->user();
        Session::put('verifyType', 'oAuth');
        $user->sendEmailVerification();

    }

    public function storeCloseAccountReason($data){

        $user = auth()->user();
        $user->update([
            'closure_reason' => $data['closure_reason'],
            'closure_text' => $data['closure_text']
        ]);
    }

    public function closeUserAccount($data){

        $user = auth()->user();
        $user->update([
            'status' => 2,
        ]);
    }

}