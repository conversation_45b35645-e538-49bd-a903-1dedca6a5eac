<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyAssessment
 *
 * @property int $id
 * @property int $company_id
 */
class CompanyAssessment extends Model
{
    protected $fillable = [
        'company_id',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function statuses()
    {
        return $this->hasMany(CompanyAssessmentStatus::class);
    }
}
