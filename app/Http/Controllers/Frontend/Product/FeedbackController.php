<?php

namespace App\Http\Controllers\Frontend\Product;

use App\Mail\FeedbackSubmittedMailable;
use App\Models\Product;
use App\Models\Feedback;
use App\Services\MailService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class FeedbackController extends Controller
{
    public function index($productId, Request $request)
    {
        $product = Product::where('uuid', $productId)->first();

        // Get sort and filter from query string
        $sort = $request->query('feedback_sort', 'new_to_old');
        $filter = $request->query('feedback_filter', null);

        $feedbackQuery = Feedback::with('replies.user', 'user')
            ->where('product_id', $product->id)
            ->whereNull('parent_id');

        // Apply filter
        if ($filter) {
            $filterArray = explode(',', $filter);
            $typeMap = [
                'question' => 'question',
                'positive' => 'like',
                'negative' => 'dislike',
            ];
            $types = [];
            foreach ($filterArray as $f) {
                if (isset($typeMap[$f])) {
                    $types[] = $typeMap[$f];
                }
            }
            if (!empty($types)) {
                $feedbackQuery->whereIn('type', $types);
            }
        }

        // Apply sort
        if ($sort === 'new_to_old') {
            $feedbackQuery->orderBy('created_at', 'desc');
        } elseif ($sort === 'old_to_new') {
            $feedbackQuery->orderBy('created_at', 'asc');
        } elseif ($sort === 'most_replies') {
            $feedbackQuery->withCount('replies')->orderBy('replies_count', 'desc');
        }

        $feedback = $feedbackQuery->get();

        return response()->json($feedback);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,uuid',
            'type' => 'nullable|in:like,dislike,question',
            'comment' => 'required|string',
            'parent_id' => 'nullable|exists:feedback,uuid',
        ]);

        $parentFeedback = null;
        $product = Product::where('uuid', $validated['product_id'])->first();
        if(isset($validated['parent_id'])) {
            $parentFeedback = Feedback::where('uuid', $validated['parent_id'])->first();
            $validated['parent_id'] = $parentFeedback->id;
        }
        $feedback = Feedback::create([
            'product_id' => $product->id,
            'user_id' => Auth::user()->id,
            'type' => $validated['type'] ?? null,
            'comment' => $validated['comment'] ?? null,
            'parent_id' => $validated['parent_id'] ?? null,
        ]);
        $product->searchable();
        
        // Notify to company via email if it is a direct product feedback
        if(empty($validated['parent_id'])) {
            $emailData['subject'] = 'Feedback Received: ' 
                . Auth::user()->name . ', ' 
                . now()->format('Y-m-d H:i:s') . ', ' 
                . route('frontend.product.show', ['uuid' => $product->uuid]);
            
            $emailData['entity_type'] = 'Product';
            $emailData['entity_name'] = $product->name; // Company/Product
            $emailData['url'] = 'URL: ' . route('frontend.product.show', ['uuid' => $product->uuid]); // [Profile/Product link]
            $emailData['user_name'] = 'From: ' . Auth::user() ? Auth::user()->name : 'annonymous';
            $emailData['time'] = 'Time: ' . now()->format('H:i') . ', ' . now()->format('Y-M-d'); // [Formatted Time] 16:15 2025-Jun-17
            $emailData['feedback_type'] = Str::title($validated['type']); // [Q/👍/👎]
            $emailData['message'] = $validated['comment']; // [Message text]

            try {
                MailService::send(new FeedbackSubmittedMailable($emailData), $product->company->email);
            } catch (\Exception $e) {
                \Log::error("Failed to notify by email for {$product->company->email}: " . $e->getMessage());
                $failed[] = $product->company->email;
            }
        } else if (!empty($validated['parent_id'])) {
            // Notify user via email if this is a reply to feedback
            if ($parentFeedback && $parentFeedback->user && $parentFeedback->user->email) {
                $parentFeedbackUserEmail = $parentFeedback->user->email;

                $emailData['subject'] = 'Feedback Received: ' 
                . Auth::user()->name . ', ' 
                . now()->format('Y-m-d H:i:s') . ', ' 
                . route('frontend.product.show', ['uuid' => $product->uuid]);
            
                $emailData['entity_type'] = 'Product';
                $emailData['entity_name'] = $product->name; // Company/Product
                $emailData['url'] = 'URL: ' . route('frontend.product.show', ['uuid' => $product->uuid]); // [Profile/Product link]
                $emailData['user_name'] = 'From: ' . Auth::user() ? Auth::user()->name : 'annonymous';
                $emailData['time'] = 'Time: ' . now()->format('H:i') . ', ' . now()->format('Y-M-d'); // [Formatted Time] 16:15 2025-Jun-17
                $emailData['feedback_type'] = 'Reply'; // [Q/👍/👎]
                $emailData['message'] = $validated['comment']; // [Reply text]

                try {
                    MailService::send(new FeedbackSubmittedMailable($emailData), $parentFeedbackUserEmail);
                } catch (\Exception $e) {dd(982516);
                    \Log::error("Failed to notify by email for {$parentFeedbackUserEmail}: " . $e->getMessage());
                    $failed[] = $parentFeedbackUserEmail;
                }
            }
        }

        return response()->json(['success' => true, 'feedback' => $feedback->load('user')]);
    }

    public function update(Request $request, $feedbackId)
    {
        $validated = $request->validate([
            'comment' => 'required|string',
            'user_id' => 'required|integer',
        ]);
        $feedback = Feedback::where('uuid', $feedbackId)->where('user_id', $validated['user_id'])->first();
        if (!$feedback) {
            return response()->json(['success' => false, 'message' => 'Feedback not found'], 404);
        }

        $feedback->update([
            'comment' => $validated['comment'] ?? null,
        ]);

        return response()->json(['success' => true, 'feedback' => $feedback->load('user')]);
    }

    public function destroy(Request $request, $feedbackId)
    {
        $validated = $request->validate([
            'user_id' => 'required|integer',
        ]);
        $feedback = Feedback::where('uuid', $feedbackId)->where('user_id', $validated['user_id'])->first();
        if (!$feedback) {
            return response()->json(['success' => false, 'message' => 'Feedback not found'], 404);
        }

        $feedback->delete();

        return response()->json(['success' => true]);
    }

}

