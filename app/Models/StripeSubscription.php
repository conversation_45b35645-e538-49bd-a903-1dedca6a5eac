<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StripeSubscription extends Model
{

    const STATUS_ACTIVE = 1;

    const STATUS_INACTIVE = 0;


    protected $fillable = [
        'stripe_product_id',
        'name',
        'description',
        'features_list',
        'gbp_price_monthly',
        'gbp_price_annual',
        'euro_price_monthly',
        'euro_price_annual',
        'usd_price_monthly',
        'usd_price_annual',
        'subscription_rank',
        'is_active',
        'is_assigned',
    ];

    protected $casts = [
        'features_list' => 'array',
        'gbp_price_monthly' => 'decimal:2',
        'gbp_price_annual' => 'decimal:2',
        'euro_price_monthly' => 'decimal:2',
        'euro_price_annual' => 'decimal:2',
        'usd_price_monthly' => 'decimal:2',
        'usd_price_annual' => 'decimal:2',
        'is_active' => 'boolean',
        'is_assigned' => 'boolean',
    ];

    public static function getStatusLabelAndStyle($status)
    {
        $statuses = [
            self::STATUS_ACTIVE => [
                'label' => 'Active',
                'style' => 'background-color:#28a745; color:#fff; text-align:center; border-radius:15px; padding:5px 10px; display:inline-block;'
            ],
            self::STATUS_INACTIVE => [
                'label' => 'Inactive',
                'style' => 'background-color:#dc3545; color:#fff; text-align:center; border-radius:15px; padding:5px 10px; display:inline-block;'
            ],
        ];


        return $statuses[$status] ?? ['label' => 'Unknown', 'style' => 'background-color:#6c757d;'];
    }

    /**
     * planFeatures
     *
     * @return HasMany
     */
    public function planFeatures(): HasMany
    {
        return $this->hasMany(PlanFeature::class, 'stripe_subscription_id');
    }

    public function features(): BelongsToMany
    {
        return $this->belongsToMany(PlanFeature::class, 'subscription_features', 'subscription_id', 'feature_id')
            ->withPivot('enabled', 'limit')
            ->withTimestamps();
    }

}
