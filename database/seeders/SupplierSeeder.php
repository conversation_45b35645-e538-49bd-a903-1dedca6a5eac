<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\CompanyLocation;
use App\Models\User;
use App\Models\UserCompany;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {

            //get role
            $role = Role::findByName(User::ROLE_SUPPLIER);

            for ($i=0; $i < 10; $i++) { 
                $user = User::factory()->create();
                
                // Assign role
                $user->assignRole($role);

                // Create company
                $company = Company::factory()->create();

                // CompanyLocation
                CompanyLocation::factory()->create();

                // Assign user to company
                UserCompany::factory()->create([
                    'user_id' => $user->id,
                    'company_id' => $company->id,
                ]);
            }
        });
    }
}
