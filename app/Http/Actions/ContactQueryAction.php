<?php

namespace App\Http\Actions;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use App\Mail\QuerySubmittedMail;
use App\Models\ContactQuery;
use App\Models\AdminNotification;
use App\Models\QueryCategory;
use App\Services\MailService;
use Illuminate\Support\Str;

use function PHPUnit\Framework\throwException;

class ContactQueryAction
{
    public function __invoke(array $data)
    {
        $user = Auth::user();
        $queryCategory = QueryCategory::find($data['query_name']);
        //Saving data to database 
        \DB::transaction(function () use ($data, $user, $queryCategory) {
            if($user){
                $address = $user->area ? $user->area->name.' '.$user->area->country->country_name : '';
                $queryData = [
                    'query_category' => $queryCategory ? $queryCategory->title : '',
                    'query_subject' => $data['query_subject'],
                    'message' => $data['query_message'],
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'profile_username' => $user->username,
                    'company' => $user->company ? $user->company->name : '',
                    'address' => $address,
                    'email' => $user->email
                ];
            } else {
                $queryData = [
                    'query_category' => $queryCategory ? $queryCategory->title : '',
                    'query_subject' => $data['query_subject'],
                    'message' => $data['query_message'],
                    'first_name' => $data['first_name'],
                    'last_name' => $data['last_name'],
                    'profile_username' => $data['username'],
                    'company' => $data['company'],
                    'address' => $data['address'],
                    'email' => $data['your_email'],
                ];
            }

            if ($data['query_name'] == 'Flag Issue' && $user) {
                AdminNotification::create([
                    'user_id' => $user->id,
                    'reference_number' => 'ADM-' . strtoupper(Str::random(6)),
                    'category' => $data['query_name'],
                    'subject' => $data['query_subject'],
                    'message' => $data['query_message'],
                    'allow_replies' => 1,
                    'assigned_to' => Auth::id(),
                    'status' => 'open',
                ]);
            } else {
                ContactQuery::create($queryData);
            }
        });
        // Prepare email data
        $emailData = [
            'query_name' => $queryCategory ? $queryCategory->title : '',
            'query_subject' => $data['query_subject'],
            'query_message' => $data['query_message'],
            'user_name' => auth()->check() ? $user->name : $data['first_name'].' '.$data['last_name'],
            'user_email' =>auth()->check() ? $user->email : $data['your_email'],
            'user_company' => auth()->check() && $user->company ? $user->company->name : $data['company'],
        ];
        MailService::send(new QuerySubmittedMail($emailData), config('mail.admin_email'));
    }
}
