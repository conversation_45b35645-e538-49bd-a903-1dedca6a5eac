<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Permissions
        $permissions = [
            'super-user' => 'super user',
            'manage-settings' => 'manage settings',
            'manage-users' => 'manage users',
            'manage-supplier-requests' => 'manage suppliers requests',
            'manage-suppliers' => 'manage suppliers',
            'manage-categories' => 'manage categories',
            'manage-roles-permissions' => 'manage roles permissions',
            'manage-profile' => 'manage profile',
            'manage-plans' => 'manage plans',
            'manage-subscriptions' => 'manage subscriptions',
            'manage-contents' => 'manage contents',
            'manage-quotations' => 'manage quotations',
            'manage-connect-hub' => 'manage connect hub',
            'manage-subscription-features' => 'manage subscription features',

            'view-users' => 'view users',
            'view-supplier-requests' => 'view suppliers requests',
            'view-suppliers' => 'view suppliers',
            'view-categories' => 'view categories',
            'view-dashboard' => 'view dashboard',
            'view-plans' => 'view plans',
            'view-subscriptions' => 'view subscriptions',
            'view quotations' => 'view quotations',
            'view-connect-hub' => 'view connect hub',

        ];

        foreach ($permissions as $key => $value) {
            Permission::firstOrCreate(
                [
                    'name' => $value,
                    'guard_name' => 'web',
                ],
                [
                    'slug' => $key,
                    'type' => 'admin',
                ]
            );
        }
    }
}
