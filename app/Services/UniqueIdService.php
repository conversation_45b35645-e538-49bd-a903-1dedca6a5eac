<?php

namespace App\Services;

use App\Models\User;

class UniqueIdService
{
    /**
     * Generates a unique username based on the user's initials, country code, and year.
     *
     * @param string $firstName   The first name of the user.
     * @param string $lastName    The last name of the user.
     * @param string $countryCode The country code of the user.
     * @param string $year        The year associated with the username.
     * @return string
     */
    function generate($firstName, $lastName, $countryCode, $year = null): string
    {
        $initials = strtoupper(substr($firstName, 0, 1) . substr($lastName, 0, 1));
        $countryCode = strtoupper($countryCode);
        $year = $year ?? date('Y');
        $year = substr($year, -2);

        $username = '';
        do {
            $randomSequence = $this->generateRandomSequence();
            $username = "{$initials}{$randomSequence}@{$countryCode}{$year}";
        } while ($this->usernameExists($username));

        return $username;
    }

    /**
     * Generates a random 4-character sequence from a given set of characters
     *
     * @return string
     */
    function generateRandomSequence(): string
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%';
        return substr(str_shuffle($characters), 0, 3);
    }

    /**
     * Checks if a given username already exists in the database.
     *
     * @param string $username The username to check.
     * @return bool
     */
    function usernameExists($username): bool
    {
        return User::where('username', $username)->exists();
    }
}
