<?php

namespace Database\Factories;

use App\Models\CompanyAssessment;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyAssessment>
 */
class CompanyAssessmentFactory extends Factory
{
    protected $model = CompanyAssessment::class;

    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
        ];
    }
}
