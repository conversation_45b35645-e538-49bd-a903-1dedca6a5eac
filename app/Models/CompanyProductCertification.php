<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyProductCertification
 *
 * Represents the product certifications associated with a company.
 *
 * @package App\Models
 */
class CompanyProductCertification extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'title',
        'body',
        'number',
        'notes',
    ];

    /**
     * Get the company associated with the product certification.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
