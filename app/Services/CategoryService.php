<?php

namespace App\Services;

use App\Models\Category;
use App\Traits\DBService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Database\Eloquent\Builder;
use App\Repositories\CategoryRepository as Repository;

class CategoryService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

    /*
     * Category Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get query or collection for the category list
     *
     * @param boolean $query
     * @param array $params
     * @return Builder|Collection
     */
    public function getListForAdmin(bool $query = false, array $params = []): Builder|Collection
    {
        if ($query) {
            return $this->repository->queryByMixedCondition($params);
        } else {
            return $this->repository->getByMixedCondition($params);
        }
    }

    /**
     * Get a nested list of categories.
     *
     * @param array $params
     * @param int $level The current depth level (default is 1).
     * @return array
     */
    public function getNestedCategories(array $params = [], $level = 1): array
    {
        $params['level'] = $level;

        $categories = $this->repository->getByMixedCondition($params);

        $nested = [];

        foreach ($categories as $category) {
            $params['parent_id'] = $category->id;
            $children = $this->getNestedCategories($params, $level + 1);
            $nested[] = [
                'id' => $category->id,
                'name' => $category->name,
                'level' => $level,
                'children' => $children,
            ];
        }

        return $nested;
    }

    public function generateCategorySelect(array $categories, int $parentId = null): string
    {
        $html = '';
        foreach ($categories as $category) {
            $indent = str_repeat('&nbsp;', $category['level'] * 4);
            $selected = ($parentId == $category['id']) ? 'selected' : '';
            $html .= '<option value="' . $category['id'] . '" ' . $selected . '>' . $indent . $category['name'] . ' (Level' . $category['level'] . ')</option>';
            if (!empty($category['children'])) {
                $html .= $this->generateCategorySelect($category['children'], $parentId);
            }
        }

        return $html;
    }

    /**
     * Get new level value considering the parent category level
     *
     * @param integer|null $parent_id
     * @return integer
     */
    public function getNewLevel(int $parent_id = null): int
    {
        return $parent_id ? ($this->find($parent_id)->level + 1) : 1;
    }

    /**
     * Get list of all the statuses
     *
     * @return array
     */
    public function getStatuses()
    {
        return $this->repository->statuses();
    }

    /**
     * Retrieve category data based on business type, level, and optional parent category.
     *
     * This function fetches active categories that match the specified business type and level.
     * If a parent category is provided, it filters categories under that parent.
     *
     * @param string $businessType The type of business category.
     * @param int $level The level of the category.
     * @param int|null $parent The parent category ID (optional).
     * @return \Illuminate\Support\Collection A collection of matching categories.
     */
    public function getCategoriesData($businessType, $level, $parent = null)
    {
        if (empty($businessType)) {
            return collect(); // Return an empty collection
        }

        return Category::where([
            'level' => $level,
            'type' => $businessType,
            'status' => 1
        ])->when($parent, function ($query, $parent) {
            $query->where('parent_id', $parent);
        })->get();
    }

    /**
     * Retrieve categories by sector and return them as a JSON response.
     *
     * This function fetches category data based on business type, level, and optional parent category.
     * The retrieved categories are returned in a JSON response.
     *
     * @param string $businessType The type of business category.
     * @param int $level The level of the category.
     * @param int|null $parent The parent category ID (optional).
     * @return \Illuminate\Http\JsonResponse JSON response containing the category data.
     */
    public function getCategoriesBySector($businessType, $level, $parent = null): JsonResponse
    {
        $categories = $this->getCategoriesData($businessType, $level, $parent);
        return response()->json($categories);
    }


    // public function getCategoryFilterData()
    // {
    //     // Step 1: Get all category-product mappings for level 5 categories
    //     $categoryProductPairs = DB::table('category_product')
    //         ->join('categories as c5', 'category_product.category_id', '=', 'c5.id')
    //         ->where('c5.level', 5)
    //         ->select('category_product.product_id', 'category_product.category_id as c5_id')
    //         ->get();

    //     // Step 2: Load all parent categories up to L1
    //     $level5CategoryIds = $categoryProductPairs->pluck('c5_id')->unique();

    //     $categories = Category::with([
    //         'parent.parent.parent.parent' // L5 → L4 → L3 → L2 → L1
    //     ])
    //         ->whereIn('id', $level5CategoryIds)
    //         ->get()
    //         ->flatMap(function ($cat) {
    //             return collect([
    //                 $cat,
    //                 $cat->parent,
    //                 $cat->parent?->parent,
    //                 $cat->parent?->parent?->parent,
    //                 $cat->parent?->parent?->parent?->parent,
    //             ])->filter();
    //         })
    //         ->keyBy('id');

    //     // Step 3: Build product sets for all levels
    //     $productSets = [1 => [], 2 => [], 3 => [], 4 => [], 5 => []];

    //     foreach ($categoryProductPairs as $pair) {
    //         $productId = $pair->product_id;
    //         $categoryL5 = $categories[$pair->c5_id] ?? null;
    //         $categoryL4 = $categoryL5?->parent;
    //         $categoryL3 = $categoryL4?->parent;
    //         $categoryL2 = $categoryL3?->parent;
    //         $categoryL1 = $categoryL2?->parent;

    //         if (!$categoryL5 || !$categoryL4 || !$categoryL3 || !$categoryL2 || !$categoryL1) continue;

    //         $productSets[5][$categoryL5->id] ??= collect();
    //         $productSets[4][$categoryL4->id] ??= collect();
    //         $productSets[3][$categoryL3->id] ??= collect();
    //         $productSets[2][$categoryL2->id] ??= collect();
    //         $productSets[1][$categoryL1->id] ??= collect();

    //         $productSets[5][$categoryL5->id]->push($productId);
    //         $productSets[4][$categoryL4->id]->push($productId);
    //         $productSets[3][$categoryL3->id]->push($productId);
    //         $productSets[2][$categoryL2->id]->push($productId);
    //         $productSets[1][$categoryL1->id]->push($productId);
    //     }

    //     // Step 4: Build full tree structure from L1 → L5
    //     $structure = [];

    //     foreach ($productSets[1] as $l1Id => $l1Products) {
    //         $l1Cat = $categories[$l1Id] ?? null;
    //         if (!$l1Cat) continue;
    //         $l1Children = [];

    //         foreach ($productSets[2] as $l2Id => $l2Products) {
    //             $l2Cat = $categories[$l2Id] ?? null;
    //             if ($l2Cat?->parent_id !== $l1Id) continue;
    //             $l2Children = [];

    //             foreach ($productSets[3] as $l3Id => $l3Products) {
    //                 $l3Cat = $categories[$l3Id] ?? null;
    //                 if ($l3Cat?->parent_id !== $l2Id) continue;
    //                 $l3Children = [];

    //                 foreach ($productSets[4] as $l4Id => $l4Products) {
    //                     $l4Cat = $categories[$l4Id] ?? null;
    //                     if ($l4Cat?->parent_id !== $l3Id) continue;
    //                     $l4Children = [];

    //                     foreach ($productSets[5] as $l5Id => $l5Products) {
    //                         $l5Cat = $categories[$l5Id] ?? null;
    //                         if ($l5Cat?->parent_id !== $l4Id) continue;

    //                         $l4Children[] = [
    //                             'id' => $l5Cat->id,
    //                             'name' => $l5Cat->name,
    //                             'slug' => $l5Cat->slug,
    //                             'level' => $l5Cat->level,
    //                             'products_count' => $l5Products->unique()->count(),
    //                             'children' => [],
    //                         ];
    //                     }

    //                     $l3Children[] = [
    //                         'id' => $l4Cat->id,
    //                         'name' => $l4Cat->name,
    //                         'slug' => $l4Cat->slug,
    //                         'level' => $l4Cat->level,
    //                         'products_count' => $l4Products->unique()->count(),
    //                         'children' => $l4Children,
    //                     ];
    //                 }

    //                 $l2Children[] = [
    //                     'id' => $l3Cat->id,
    //                     'name' => $l3Cat->name,
    //                     'slug' => $l3Cat->slug,
    //                     'level' => $l3Cat->level,
    //                     'products_count' => $l3Products->unique()->count(),
    //                     'children' => $l3Children,
    //                 ];
    //             }

    //             $l1Children[] = [
    //                 'id' => $l2Cat->id,
    //                 'name' => $l2Cat->name,
    //                 'slug' => $l2Cat->slug,
    //                 'level' => $l2Cat->level,
    //                 'products_count' => $l2Products->unique()->count(),
    //                 'children' => $l2Children,
    //             ];
    //         }

    //         $structure[] = [
    //             'id' => $l1Cat->id,
    //             'name' => $l1Cat->name,
    //             'slug' => $l1Cat->slug,
    //             'level' => $l1Cat->level,
    //             'products_count' => $l1Products->unique()->count(),
    //             'children' => $l1Children,
    //         ];
    //     }
    //     dd($structure);
    //     return $structure;
    // }
    public function getCategoryFilterData($excludeEmpty = false, $type = null)
    {
        // Step 1: Load all categories (up to level 5)
        $allCategories = Category::where('level', '<=', 5)
            ->with('parent') // get parent to trace up the hierarchy
            ->get()
            ->keyBy('id');

        // Step 2: Load all category-product pairs
        $productPairsQuery = DB::table('category_product')
            ->select('category_id', 'product_id');

        // Filter by type if specified
        if ($type) {
            $productPairsQuery->join('products', 'category_product.product_id', '=', 'products.id')
                ->where('products.type', $type);
        }

        $productPairs = $productPairsQuery->get();

        // Step 3: Map product IDs to all levels (L1 to L5)
        $productSets = [];

        foreach ($productPairs as $pair) {
            $productId = $pair->product_id;
            $current = $allCategories[$pair->category_id] ?? null;

            while ($current) {
                $productSets[$current->id] ??= collect();
                $productSets[$current->id]->push($productId);
                $current = $current->parent_id ? $allCategories[$current->parent_id] ?? null : null;
            }
        }

        // Step 4: Attach product counts to categories
        foreach ($allCategories as $category) {
            $category->products_count = isset($productSets[$category->id])
                ? $productSets[$category->id]->unique()->count()
                : 0;
        }

        // Step 4: Assign product count to each category
        foreach ($allCategories as $cat) {
            $cat->products_count = isset($productSets[$cat->id])
                ? $productSets[$cat->id]->unique()->count()
                : 0;
        }

        // Step 5: Group by parent for tree-building
        $grouped = $allCategories->groupBy('parent_id');

        // Step 6: Recursive tree builder
        $buildNode = function ($category) use (&$buildNode, $grouped, $excludeEmpty) {
            $node = [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'level' => $category->level,
                'products_count' => $category->products_count,
                'children' => [],
            ];
            
            // Get children
            if (isset($grouped[$category->id])) {
                foreach ($grouped[$category->id] as $child) {
                    $childNode = $buildNode($child);
                    
                    // If excluding empty categories, only add children with products or grandchildren
                    if (!$excludeEmpty || $childNode['products_count'] > 0 || !empty($childNode['children'])) {
                        $node['children'][] = $childNode;
                    }
                }
            }
            
            return $node;
        };

        // Step 7: Build final tree from level 1
        $tree = [];
        $rootCategories = $allCategories->where('level', 1);
        
        foreach ($rootCategories as $cat) {
            $node = $buildNode($cat);
            
            // If excluding empty categories, only add root nodes with products or children
            if (!$excludeEmpty || $node['products_count'] > 0 || !empty($node['children'])) {
                $tree[] = $node;
            }
        }
        
        return $tree;
    }
}
