<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_broadcast_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('admin_id')->constrained('users'); // Who sent it
            $table->enum('target_type', ['user', 'business']);
            $table->json('target_ids'); // list of user/business ids
            $table->string('reference_number')->unique();
            $table->string('category');
            $table->string('subject');
            $table->text('message');
            $table->boolean('allow_replies')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_broadcast_notifications');
    }
};
