<?php

namespace App\Repositories;

use App\Models\CompanyLocation;

/**
 * Class CompanyLocationRepository
 *
 * Handles data operations for the CompanyLocation model.
 */
class CompanyLocationRepository extends BaseRepository
{
    /**
     * The CompanyLocation model instance.
     *
     * @var \App\Models\CompanyLocation
     */
    protected $model;

    /**
     * CompanyLocationRepository constructor.
     *
     * @param \App\Models\CompanyLocation $companyLocation
     */
    public function __construct(CompanyLocation $companyLocation)
    {
        $this->model = $companyLocation;
    }
}
