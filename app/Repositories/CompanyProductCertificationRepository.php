<?php

namespace App\Repositories;

use App\Models\CompanyProductCertification;

/**
 * Class CompanyProductCertificationRepository
 *
 * Handles data operations for the CompanyProductCertification model.
 */
class CompanyProductCertificationRepository extends BaseRepository
{
    /**
     * The CompanyProductCertification model instance.
     *
     * @var \App\Models\CompanyProductCertification
     */
    protected $model;

    /**
     * CompanyProductCertificationRepository constructor.
     *
     * @param \App\Models\CompanyProductCertification $companyProductCertification
     */
    public function __construct(CompanyProductCertification $companyProductCertification)
    {
        $this->model = $companyProductCertification;
    }
}
