<?php

namespace App\Http\Controllers\Admin\Plan;

use App\Http\Controllers\Controller;
use App\Models\PlanFeature;
use App\Models\StripeSubscription;
use App\Models\SubscriptionFeature;
use App\Services\SubscriptionFeatureService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Yajra\DataTables\Facades\DataTables;

class SubscriptionFeatureController extends Controller
{

    /**
     * Subscription Feature service instance.
     *
     * @var SubscriptionFeatureService
     */
    public $service;

    /**
     * routePrefix
     *
     * @var mixed
     */
    public $routePrefix;

    /**
     * Instantiate a new controller instance.
     *
     * @param SubscriptionFeatureService $service The service layer for managing plan feature.
     * @return void
     */
    public function __construct(SubscriptionFeatureService $service)
    {
        $this->service = $service;
        $this->routePrefix = 'admin.subscription-features.';
    }
    /**
     * Display a listing of HowToHub entries.
     *
     * @return View|JsonResponse The view displaying the list of hubs.
     */
    public function index(Request $request): View|JsonResponse
    {

        if ($request->ajax()) {
            return $this->list($request);
        }
        $status = $request->status ?? null;
        $routePrefix = $this->routePrefix;
        return view('admin.subscriptionFeature.index', [
            'routePrefix' => $routePrefix,
            'title' => 'Subscription Feature',
            'status' => $status,
        ]);

    }

    /**
     * Return a JSON response with the list of pages for DataTables.
     *
     * @param Request $request The current HTTP request instance.
     * @return JsonResponse The DataTable JSON response.
     */
    protected function list(Request $request): JsonResponse
    {
        $user = $this->service->current();
        $status = $request->status;
        $subscriptionFeatures = $this->service->getSubscriptionFeatures($status);
        
        return DataTables::of($subscriptionFeatures)
            
            ->addColumn('enabled', function ($subscriptionFeature) {
                $statusDetails = SubscriptionFeature::getStatusLabelAndStyle($subscriptionFeature->enabled);
                return '<div style="' . $statusDetails['style'] . '">' . $statusDetails['label'] . '</div>';
            })

            ->addColumn('stripe_subscription', function ($subscriptionFeature) {
               return $subscriptionFeature->subscription->name;
            })
            ->addColumn('plan_name', function ($subscriptionFeature) {
                return $subscriptionFeature->feature->name;
            })
            ->addColumn('limit', function ($subscriptionFeature) {
        
                return $subscriptionFeature->limit??'N/A';
            })

            ->addColumn('created_at', function ($subscriptionFeature) {
                return $subscriptionFeature->created_at->format('Y-M-d H:i:s');
            })

            ->addColumn('updated_at', function ($subscriptionFeature) {
                return $subscriptionFeature->updated_at->format('Y-M-d H:i:s');
            })
            
            ->addColumn('updated_by', function ($subscriptionFeature) {
                return $subscriptionFeature->user?->username?:$subscriptionFeature->user?->name;
            })
            

            ->addColumn('action', function ($subscriptionFeature) use ($user) {
                $action = '';
                if ($user->can('manage subscription features')) {
                    $action .= '<a href="' . route('admin.subscription-features.edit', $subscriptionFeature) . '" class="btn btn-primary">Edit</a> ';
                }

                return $action;
            })
            ->rawColumns(['enabled','plan_name','action'])
            ->toJson();
    }



    /**
     * Display the plan feature edit form.
     */
    public function edit(SubscriptionFeature $subscriptionFeature): View
    {
        
        $routePrefix = 'admin.subscription-features.';
        $title = 'Subscription Feature';
        $stripeSubscriptions = StripeSubscription::all();
        return view('admin.subscriptionFeature.edit', [
            'subscriptionFeature' => $subscriptionFeature,
            'routePrefix' => $routePrefix,
            'title' => $title,
            'stripeSubscriptions' => $stripeSubscriptions,
        ]);
    }


    /**
     * Update the plan feature information.
     */
    public function update(Request $request, SubscriptionFeature $subscriptionFeature): RedirectResponse
    {
        $rules = [
            'limit' => 'nullable|integer',
            'enabled' => 'required|boolean',
        ];
    

        $validated = $request->validate($rules);
        $validated['updated_by'] = $this->service->current()->id;
        $subscriptionFeature->update($validated);
    
        return back()->with('status', 'Subscription feature updated successfully!');
    }
    


}
