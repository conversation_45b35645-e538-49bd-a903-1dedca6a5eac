//Javascript Mousehover Method
document.addEventListener("DOMContentLoaded", function(){
    // make it as accordion for smaller screens
    if (window.innerWidth > 992) {

        document.querySelectorAll('.navbar .nav-item').forEach(function(everyitem){

            everyitem.addEventListener('mouseover', function(e){

                let el_link = this.querySelector('a[data-bs-toggle]');

                if(el_link != null){
                    let nextEl = el_link.nextElementSibling;
                    el_link.classList.add('show');
                    nextEl.classList.add('show');
                }

            });
            everyitem.addEventListener('mouseleave', function(e){
                let el_link = this.querySelector('a[data-bs-toggle]');

                if(el_link != null){
                    let nextEl = el_link.nextElementSibling;
                    el_link.classList.remove('show');
                    nextEl.classList.remove('show');
                }


            })
        });

    }
    // end if innerWidth
});

//Clients carousel
$('.c-clients-slide').owlCarousel({
    loop:true,
    margin:10,
    responsiveClass:true,
    items:4,
    nav: true,
    navText: ["<img src='images/icons/prev-arr.svg'>","<img src='images/icons/next-arr.svg'>"],
    dots: true,
    responsive:{
        0:{
            items:3
        },
        600:{
            items:3
        },
        1000:{
            items:7
        }
    }
})
//Clients carousel
$('.client-slider').owlCarousel({
    loop:true,
    responsiveClass:true,
    items:4,
    margin: 10,
    nav: true,
    navText: ["<img src='images/icons/prev-arr.svg'>","<img src='images/icons/next-arr.svg'>"],
    dots: true,
    responsive:{
        0:{
            items:2
        },
        600:{
            items:3
        },
        1000:{
            items:4
        }
    }
})

//awards carousel
$('.awards-slider').owlCarousel({
    loop:true,
    margin:10,
    responsiveClass:true,
    items:4,
    nav: true,
    arrows: false,
    dots: false,
    autoplay: true,
    autoplaySpeed: 3000,
    responsive:{
        0:{
            items:4
        },
        600:{
            items:5
        },
        1000:{
            items:6
        }
    }
})


// modal slider

$('.modal-image-sllider').owlCarousel({
    loop:false,
    margin:10,
    responsiveClass:true,
    items:3,
    nav: true,
    navText: ["<img src='images/icons/prev-arr.svg'>","<img src='images/icons/next-arr.svg'>"],
    dots: false,
    responsive:{
        0:{
            items:2
        },
        600:{
            items:3
        },
        1000:{
            items:3
        }
    }
})

// Navigation script start

$(function() {
    $('.navbar .navbar-toggler').addClass('collapsed');
});


if ($(window).width() < 700) {
    }

// scroll down to bottom

$(document).ready(function(){
    $('.cta-chat.contact-mobile').click(function() {
      $('html, body').animate({
        scrollTop: $('#contact-section').offset().top
      }, 800);  // 800 is the duration of the animation in milliseconds
    });
  });