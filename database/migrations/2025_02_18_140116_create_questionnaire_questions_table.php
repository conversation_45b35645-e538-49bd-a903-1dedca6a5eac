<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questionnaire_questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('section_id');
            $table->unsignedBigInteger('parent_question_id')->nullable();
            $table->string('title', 255);
            $table->text('question_text');
            $table->string('action_text', 255)->nullable();
            $table->enum('answer_type', ['yes_no', 'dropdown', 'text', 'file_upload']);
            $table->json('dropdown_options')->nullable();
            $table->boolean('requires_upload')->default(false);
            $table->boolean('display_on_open')->default(false);
            $table->json('action_yes')->nullable();
            $table->json('action_no')->nullable();
            $table->timestamps();

            $table->foreign('section_id')->references('id')->on('questionnaire_sections')->onDelete('cascade');
            $table->foreign('parent_question_id')->references('id')->on('questionnaire_questions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questionnaire_questions');
    }
};
