<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSettingsTable extends Migration
{
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->string('key')->unique();
            $table->string('name')->nullable();
            $table->text('value')->nullable();
            $table->string('type')
                ->default('input')->comment('input, textarea, editor, image, dropdown');
            $table->string('section')
                ->default('site')->comment('site, social, contact, api_keys');
            $table->text('info')->nullable();
            $table->tinyInteger('editable')->default('1')->comment('0: No, 1: Yes');
            $table->timestamps();

            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::dropIfExists('settings');
    }
}

