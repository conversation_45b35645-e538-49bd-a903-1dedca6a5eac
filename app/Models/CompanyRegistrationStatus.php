<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class Company
 *
 * @package App\Models
 * 
 */
class CompanyRegistrationStatus extends Model
{
    use HasFactory;
    
    /**
     * table
     *
     * @var string
     */
    protected $table = 'company_registration_status';

    /**
     * @var int Status identifier for inactive company.
     */
    public const STATUS_PENDING = 0;

    /**
     * @var int Status identifier for active company.
     */
    public const STATUS_APPROVED  = 1;

    /**
     * @var int Status identifier for rejected company.
     */
    public const STATUS_REJECTED = 2;

    /**
     * @var int Status identifier for approved company.
     */
    public const STATUS_ACTIVE = 3;

    /**
     * @var int Status identifier for company account closure.
     */
    public const STATUS_CLOSED = 4;

    /**
     * @var int Status identifier for freeze company.
     */
    public const STATUS_FREEZED = 5;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'registration_id',
        'company_id',
        'status',
        'admin_comments',
        'attempt_number',
        'reviewed_at',
    ];

    public static function getStatusLabels()
    {
        return [
            self::STATUS_PENDING => ['text' => 'Pending', 'class' => 'btn-danger'],
            self::STATUS_ACTIVE => ['text' => 'Active', 'class' => 'btn-success'],
            self::STATUS_REJECTED => ['text' => 'Rejected', 'class' => 'btn-warning'],
            self::STATUS_APPROVED => ['text' => 'Approved', 'class' => 'btn-primary'],
            self::STATUS_CLOSED => ['text' => 'Closed', 'class' => 'btn-danger'],
            self::STATUS_FREEZED => ['text' => 'Freezed', 'class' => 'btn-warning'],
        ];
    }

    /**
     * Get the company associated with the user.
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class);
    }

    /**
     * Get the user associated with the supplier request.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


    /**
     * Scope a query to filter users by their status.
     *
     * @param \Illuminate\Database\Eloquent\Builder
     * @param int $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

}
