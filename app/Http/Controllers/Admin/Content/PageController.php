<?php

namespace App\Http\Controllers\Admin\Content;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StorePageRequest;
use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\Content\PageService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Page;
use Illuminate\Support\Facades\Storage;

class PageController extends Controller
{
    /**
     * Page service instance.
     *
     * @var PageService
     */
    public $service;

    /**
     * Instantiate a new controller instance.
     *
     * @param PageService $service The service layer for managing pages.
     * @return void
     */
    public function __construct(PageService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the pages.
     *
     * @param Request $request The current HTTP request instance.
     * @return JsonResponse|View The DataTable JSON response or the view.
     */
    public function index(Request $request): View|JsonResponse
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        $user = app(UserService::class)->current();

        return view('admin.contents.pages.index', compact('user'));
    }

    /**
     * Return a JSON response with the list of pages for DataTables.
     *
     * @param Request $request The current HTTP request instance.
     * @return JsonResponse The DataTable JSON response.
     */
    protected function list(Request $request): JsonResponse
    {
        $pages = $this->service->getListForAdmin(true);
        $statuses = $this->service->getStatuses();

        return DataTables::of($pages)
        ->addColumn('status', function ($page) {
            $status = '';
            if ($page->status == Page::DRAFTED) {
                $status = '<div style="background-color:#d35757;text-align: center;border-radius: 15px;color: #fff;">Drafted</div>';
            } else {
                $status = '<div style="background-color:#358b00;text-align: center;border-radius: 15px;color: #fff;">Published</div>';
            }

            return $status;
        })
            ->addColumn('action', function ($page) use ($statuses) {
                $action = '';
                $action .= '<a href="' . route('admin.contents.pages.edit', $page) . '" class="btn btn-primary">Edit</a> ';

                $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                Action
            </button>
            <ul class="dropdown-menu" style="">';

                foreach ($statuses as $name => $value) {
                    $disabled = ($page->status == $value) ? 'disabled' : '';

                    $action .= '<li class="dropdown-item">';
                    $action .= '    <button class="btn btn-sm btn-primary change-status-btn ' . $disabled . '" data-id="' . $page->id . '" ' . $disabled . ' data-statusname="' . $name . '" data-status="' . $value . '">' . $name . '</button>';
                    $action .= '</li>';
                }


                $action .= '<li class="dropdown-item">';
                $action .= '<a href="javascript:void(0);" class="btn btn-danger delete-btn" data-url="' . route('admin.contents.pages.destroy', $page) . '"><i class="fa fa-trash-alt"></i> Delete</a> ';
                $action .= '</li>';
                $action .= '</ul>';

                return $action;

            })
            ->addColumn('created_at', function ($page) {
                return $page->created_at;
            })
            ->addColumn('created_by', function ($page) {
                return $page->creator ? $page->creator->name : 'N/A';
            })
            ->addColumn('updated_at', function ($page) {
                return $page->updated_at ? $page->updated_at->format('Y-m-d H:i:s') : 'N/A';
            })
            ->addColumn('updated_by', function ($page) {
                return $page->updater ? $page->updater->name : 'N/A';
            })
            ->rawColumns(['action','status'])
            ->toJson();
    }

    /**
     * Show the form for creating a new page.
     *
     * @return View The form view for creating a page.
     */
    public function create(): View
    {
        return view('admin.contents.pages.create');
    }

    /**
     * Store a newly created page in storage.
     *
     * @param StorePageRequest $request The validated request data for storing a page.
     * @return RedirectResponse A redirect response to the pages index with a success message.
     */
    public function store(StorePageRequest $request): RedirectResponse
    {
        $data = $request->all();

        $this->service->create($data);

        return redirect()->route('admin.contents.pages.index')->with('success', 'Page created successfully.');
    }

    /**
     * Show the form for editing a specific page.
     *
     * @param Page $page The page to edit.
     * @return View The form view for editing the page.
     */
    public function edit(Page $page): View
    {
        return view('admin.contents.pages.edit', compact('page'));
    }

    /**
     * Update the specified page in storage.
     *
     * @param StorePageRequest $request The validated request data for updating a page.
     * @param Page $page The page to update.
     * @return RedirectResponse A redirect response to the pages index with a success message.
     */
    public function update(StorePageRequest $request, Page $page): RedirectResponse
    {
        $this->service->update($page->id, $request->all());

        return redirect()->route('admin.contents.pages.index')->with('success', 'Page updated successfully.');
    }

    /**
     * Remove the specified page from storage.
     *
     * @param Page $page The page to delete.
     * @return RedirectResponse A redirect response to the pages index with a success message.
     */
    public function destroy(Page $page): RedirectResponse
    {
        $page->delete();

        return redirect()->route('admin.contents.pages.index')->with('success', 'Page deleted successfully.');
    }

    /**
     * Upload an image for the editor.
     *
     * @param Request $request The current HTTP request instance.
     * @return JsonResponse A JSON response with the upload result.
     */
    public function uploadEditorImage(Request $request): JsonResponse
    {
        if ($request->hasFile('upload')) {
            $destinationPath = 'images/content';

            $file = $request->file('upload');
            if (!Storage::disk('public')->exists($destinationPath)) {
                Storage::disk('public')->makeDirectory($destinationPath);
            }

            $filename = uniqid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storePubliclyAs($destinationPath, $filename, 'public');
            $url = asset('storage/' . $path);

            return response()->json([
                'fileName' => $filename,
                'uploaded' => 1,
                'url' => $url,
            ]);
        }

        return response()->json([
            'error' => [
                'message' => 'No file was uploaded.',
            ]
        ], 400);
    }

    
    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;
        $record = $this->service->find($id);

        if (!$record) {
            return response()->json(['message' => 'No Record Found']);
        }

        $this->service->update($id, ['status' => $status]);
            
        return response()->json(['message' => 'Status changed successfully']);
    }
}
