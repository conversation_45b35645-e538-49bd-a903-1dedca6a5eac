<?php

namespace App\Repositories;

use App\Models\Area;

/**
 * Class AreaRepository
 *
 * Handles data operations for the Area model.
 */
class AreaRepository extends BaseRepository
{
    /**
     * The Area model instance.
     *
     * @var \App\Models\Area
     */
    protected $model;

    /**
     * AreaRepository constructor.
     *
     * @param \App\Models\Area $area
     */
    public function __construct(Area $area)
    {
        $this->model = $area;
    }
}
