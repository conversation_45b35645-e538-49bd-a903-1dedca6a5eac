<?php

namespace App\Services;

use App\Repositories\SubscriptionRepository as Repository;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class SubscriptionService
{
    protected $repository;

    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    
    /**
     * Get current loggedin user
     *
     * @return App\Models\User
     */
    public function current()
    {
        return Auth::user();
    }

    public function getListForAdmin(string $status = null)
    {
        $conditions = [];
        if (!is_null($status)) {
            $conditions['status'] = $status;
        }

        return $this->repository->getByMixedCondition($conditions);
    }
    

}