<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BusinessConversation extends Model
{
    protected $fillable = [
        'user_id',
        'business_id',
        'assigned_to',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function business()
    {
        return $this->belongsTo(Company::class, 'business_id');
    }

    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function messages()
    {
        return $this->hasMany(BusinessMessage::class, 'conversation_id');
    }
}

