<?php

namespace App\Services;



use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use App\Models\SubscriptionFeature;


class SubscriptionFeatureService
{


    /**
     * getPlanFeatures
     *
     * @return Collection
     */
    public function getSubscriptionFeatures(?bool $status = null): Collection
    {
        $query = SubscriptionFeature::with(['feature', 'subscription']);

        if (!is_null($status)) {
            $query->where('enabled', $status);
        }

        return $query->get();
    }


    /**
     * Get current loggedin user
     *
     * @return App\Models\User
     */
    public function current()
    {
        return Auth::user();
    }

    public function create(array $data): PlanFeature
    {
        return PlanFeature::create($data);
    }
}