<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class AssessmentReason
 *
 * @property int $id
 * @property int $section_id
 * @property string $reason
 */
class AssessmentReason extends Model
{
    protected $fillable = [
        'section_id',
        'reason',
    ];

    public function section()
    {
        return $this->belongsTo(AssessmentSection::class);
    }
}
