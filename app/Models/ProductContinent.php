<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductContinent extends Model
{
    public $timestamps = false;

    protected $fillable = ['product_id', 'continent_id'];

    public function continent()
    {
        return $this->belongsTo(GeoContinent::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function product_continent_countries()
    {
        return $this->hasMany(ProductCountry::class, 'product_continent_id');
    }
}
