<?php

namespace App\Services;

use App\Repositories\SettingRepository as Repository;
use App\Traits\DBService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

    /*
     * Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

     /**
     * Retrieve all setting grouped by section
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function groupBySection(): Collection
    {
        return $this->repository->getByGroupBy('section');
    }

    /**
     * Get list of all the sections
     *
     * @return array
     */
    public function getSections()
    {
        return $this->repository->sections();
    }

    /**
     * Update setting 
     *
     * @param string $name
     * @param string $value
     * @param string|null $info
     * @return void
     */
    public function update(string $key, string $value = null, string $info = null)
    {
        $condition = ['key' => $key];
        $setting = $this->repository->findByCondition($condition);

        if (!is_null($setting)) {
            $setting->value = $value;
            $setting->info = $info;
            $setting->updated_by = Auth::user()->id;
            $setting->save();
        }
    }

    /**
     * Handle file upload and store it in the specified directory.
     *
     * Checks if the given file input exists in the request and processes the upload. 
     * Ensures that the destination directory exists, creates it if necessary, and 
     * generates a unique filename for the uploaded file. The file is stored publicly.
     *
     * @param \Illuminate\Http\Request $request The HTTP request instance containing the file input.
     * @param string $name The name of the file input field.
     * @return string|null The path of the uploaded file or null if no file was uploaded.
     */
    public function uploadFile(Request $request, string $name)
    {
        $path = null;     
        if ($request->hasFile($name)) {
            $destinationPath = 'images/global';

            $file = $request->file($name);
            if (!Storage::disk('public')->exists($destinationPath)) {
                Storage::disk('public')->makeDirectory($destinationPath);
            }

            $filename = uniqid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storePubliclyAs($destinationPath, $filename, 'public');
        }

        return $path;
    }

    public function findByKey(string $key)
    {
        $condition = ['key' => $key];
        return $this->repository->findByCondition($condition);
    }

    public function valueByKey(string $key)
    {
        $condition = ['key' => $key];
        $result = $this->repository->findByCondition($condition);

        return $result->value ?? null;
    }

    public function valueByKeys(array $keys)
    {
        $params = ['keys' => $keys];
        $results = $this->repository->getByMixedCondition($params);
        
        return $results->pluck('value', 'key')->toArray();
    }
}
