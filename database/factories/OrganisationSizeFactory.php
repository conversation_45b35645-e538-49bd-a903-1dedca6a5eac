<?php

namespace Database\Factories;

use App\Models\OrganisationSize;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<OrganisationSize>
 */
class OrganisationSizeFactory extends Factory
{
    protected $model = OrganisationSize::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'range_from' => $this->faker->numberBetween(1, 50),
            'range_to' => $this->faker->numberBetween(51, 500),
        ];
    }
}
