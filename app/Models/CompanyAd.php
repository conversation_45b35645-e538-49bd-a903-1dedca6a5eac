<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyAd
 *
 * Represents the ads associated with a company.
 *
 * @package App\Models
 */
class CompanyAd extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'image',
        'start_date',
        'end_date',
    ];

    /**
     * Get the company associated with the ad.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
