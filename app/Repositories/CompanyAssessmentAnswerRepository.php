<?php

namespace App\Repositories;

use App\Models\CompanyAssessmentAnswer;

/**
 * Class CompanyAssessmentAnswerRepository
 *
 * Handles data operations for the CompanyAssessmentAnswer model.
 */
class CompanyAssessmentAnswerRepository extends BaseRepository
{
    /**
     * The CompanyAssessmentAnswer model instance.
     *
     * @var \App\Models\CompanyAssessmentAnswer
     */
    protected $model;

    /**
     * CompanyAssessmentAnswerRepository constructor.
     *
     * @param \App\Models\CompanyAssessmentAnswer $companyAssessmentAnswer
     * @return void
     */
    public function __construct(CompanyAssessmentAnswer $companyAssessmentAnswer)
    {
        $this->model = $companyAssessmentAnswer;
    }
}
