<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class CompanyCaseStudyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'case_study_id' => ['nullable'],
            'org_case_study_image.*' => [request()->case_study_id ?'nullable' : 'required'],
            'crp_case_study_image.*' => [request()->case_study_id ? 'nullable' : 'required'],
            'case_study_title' => ['required', 'string', 'max:100'],
            'date' => ['nullable', 'string'],
            'client_name' => ['nullable', 'string', 'max:100'],
            'city' => ['nullable', 'string', 'max:100'],
            'country' => ['nullable', 'string', 'max:100'],
            'country_code' => ['nullable', 'string', 'max:10'],
            'case_study_story' => ['required', 'string', 'max:1000'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'crp_case_study_image.required' => 'Atleast one case study image is required.',
        ];
    }

    /**
     * Get the ID of the modal that should be shown after validation failure.
     *
     * @return string
     */
    public function currentModalToDisplay(): string
    {
        return config('settings.modal.case-study');
    }

    /**
     * Get the ID of the modal that should be shown after the request is successful.
     *
     * @return string
     */
    public function nextModalToDisplay(): string
    {
        return config('settings.modal.confirmation_modal');
    }
}
