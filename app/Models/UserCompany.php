<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class UserCompany
 *
 * Represents the relationship between users and companies.
 *
 * @package App\Models
 */
class UserCompany extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'company_name',
        'user_id',
        'position',
        'author',
    ];

    /**
     * Get the company associated with the user company.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the user associated with the user company.
     */
    public function user()
    {
        
        return $this->belongsTo(User::class);
    }
}
