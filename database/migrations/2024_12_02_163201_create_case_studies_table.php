<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('case_studies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained('companies'); // References 'companies.id'
            $table->string('title');
            $table->date('date')->nullable();
            $table->string('client')->nullable();
            $table->foreignId('country_id')->nullable()->constrained('countries'); // References 'countries.id'
            $table->string('city')->nullable();
            $table->text('story')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('case_studies');
    }
};
