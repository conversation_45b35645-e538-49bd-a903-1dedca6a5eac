<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;


class RoleAndPermissionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Add auth logic if needed
    }

    public function rules(): array
    {
        return [
            'position' => 'required|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string|exists:permissions,name',
        ];
    }
}
