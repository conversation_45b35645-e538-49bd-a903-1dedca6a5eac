<?php

namespace Database\Factories;

use App\Models\AssessmentQuestion;
use App\Models\AssessmentSection;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<AssessmentQuestion>
 */
class AssessmentQuestionFactory extends Factory
{
    protected $model = AssessmentQuestion::class;

    public function definition(): array
    {
        return [
            'section_id' => AssessmentSection::factory(),
            'question' => $this->faker->sentence,
            'required' => $this->faker->boolean,
        ];
    }
}
