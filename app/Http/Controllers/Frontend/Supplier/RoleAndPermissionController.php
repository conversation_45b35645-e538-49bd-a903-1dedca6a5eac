<?php

namespace App\Http\Controllers\Frontend\Supplier;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\RoleAndPermissionRequest;
use App\Mail\CompanyAdminInviteMail;
use App\Models\Company;
use App\Models\CompanyAdmin;
use App\Models\User;
use App\Services\MailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Session;

class RoleAndPermissionController extends Controller
{
    public function index()
    {
        $companyId = getCompanyId();

        $roles = CompanyAdmin::with([
            'user.permissions',
            'company'
        ])
            ->where('company_id', $companyId)
            ->whereHas('user', function ($query) {
                $query->where('status', User::STATUS_ACTIVE);
            })
            ->get();

        return view('frontend.roles.index', compact('roles'));
    }



    public function edit($id)
    {
        $role = CompanyAdmin::with([
            'user.permissions',
            'company'
        ])
            ->where('user_id', $id)
            ->first();

        if (!$role) {
            return response()->json(['message' => 'User not found'], 404);
        }

        return response()->json($role);
    }


    public function update(RoleAndPermissionRequest $request, $id)
    {
        
        $companyAdmin = CompanyAdmin::where('user_id', $id)->first();

        $companyAdmin->update([
            'position' => $request->position,
            'permissions' => json_encode($request->permissions ?? []),
            'status' => $request->status,
        ]);

        $companyAdmin->user->syncPermissions($request->permissions ?? []);

        return redirect()->back()->with('success', 'User updated successfully.');
    }

    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->permissions()->detach();
        $user->roles()->detach();
        $user->delete();
        CompanyAdmin::where('user_id', $id)->delete();
        return redirect()->back()->with('success', 'User deleted successfully.');
    }

    public function revokePermission($id, $permissionId)
    {
        $role = Role::findOrFail($id);
        $permission = Permission::findOrFail($permissionId);

        $role->revokePermissionTo($permission);

        return redirect()->route('user-permissions.index')->with('success', 'Permission revoked successfully.');
    }


    public function inviteCompanyAdmin(Request $request)
    {
        $request->validate([
            'email' => 'required|email|unique:users,email',
            'position' => 'required|string|max:255',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $companyId = getCompanyId();

        // Check if the email is already invited as a Company Admin
        $existingInvite = CompanyAdmin::where('email', $request->email)
            ->where('company_id', $companyId)
            ->first();

        if ($existingInvite) {
            return back()->withErrors(['email' => 'This email has already been invited for this company.'])->withInput();
        }

        try {
            $companyAdmin = new CompanyAdmin();
            $companyAdmin->email = $request->email;
            $companyAdmin->company_id = $companyId;
            $companyAdmin->status = 'draft';
            $companyAdmin->position = $request->position;
            $companyAdmin->permissions = json_encode($request->permissions);
            $companyAdmin->save();
        } catch (\Exception $e) {
            \Log::error("Failed to save company admin invitation: " . $e->getMessage());
            return back()->with('error', 'Failed to invite company admin. Please try again.');
        }

        // Send the invitation email
        $company = Company::find($companyId);

        if (!$company) {
            return back()->with('error', 'Company not found.');
        }

        try {
            $mailable = new CompanyAdminInviteMail($request->email, $company);
            MailService::send($mailable, $request->email);
        } catch (\Exception $e) {
            \Log::error("Email sending failed: " . $e->getMessage());
            return back()->with('error', 'Failed to send invitation email.');
        }

        return back()->with('success', 'Invitation sent successfully!');
    }



    public function invitationAccepted(Request $request)
    {
        // Validate and find the corresponding CompanyAdmin record
        $companyAdmin = CompanyAdmin::where('email', $request->email)->first();

        if (!$companyAdmin) {
            return redirect()->route('home')->with('error', 'Invalid or expired invitation token.');
        }

        // Update the status to accepted and assign the role
        // $companyAdmin->status = 'inactive';
        // $companyAdmin->save();

        Session::put('invitationAccepted', $request->email);
        Session::save();


        return redirect()->url('/')->with('success', 'Invitation accepted successfully!');
    }



}