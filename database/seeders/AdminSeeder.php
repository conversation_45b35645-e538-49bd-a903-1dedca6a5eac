<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $admin = User::create([
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('12345678'),
                'status' => User::STATUS_ACTIVE
            ]);
    
            $role = Role::findByName(User::ROLE_ADMIN);
    
            $admin->assignRole($role);

            // Assign super user permission to admin
            $admin->givePermissionTo('super user');
        });
    }
}
