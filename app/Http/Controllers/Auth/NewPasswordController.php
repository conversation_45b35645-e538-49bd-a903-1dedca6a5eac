<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\NewPasswordRequest;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class NewPasswordController extends Controller
{
    /**
     * Display the password reset view.
     */
    public function create(Request $request): View
    {
        return view('auth.reset-password', ['request' => $request]);
    }

    /**
     * Handle an incoming new password update request.
     * 
     * @param NewPasswordRequest $request
     * @return RedirectResponse
     */
    public function store(NewPasswordRequest $request): RedirectResponse
    {
        $data = $request->validated();

        $email = auth()->check() ? auth()->user()->email : \Session::get('email');
        $user = User::firstWhere('email', $email);
        $user->update([
            'password' => \Hash::make($data['password'])
        ]);

        \Session::forget('email');

        return \Redirect::back()
            ->with('status', 'Password updated successfully')
            ->with('modalToShow', $request->nextModalToDisplay());
    }
}
