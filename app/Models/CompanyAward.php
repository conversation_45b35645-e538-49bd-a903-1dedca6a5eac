<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * Class CompanyAward
 *
 * Represents the awards associated with a company.
 *
 * @package App\Models
 */
class CompanyAward extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'company_id',
        // 'image',
        'title',
        'year',
        'description',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('org_award_image')->singleFile();
        $this->addMediaCollection('crp_award_image')->singleFile();
    }

    public function getCrpAwardImageAttribute()
    {
        if ($this->hasMedia('crp_award_image')) {
            return $this->getFirstMediaUrl('crp_award_image');
        }
    }

    /**
     * Get the company associated with the award.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
