<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('block_events', function (Blueprint $table) {
            $table->id();
            $table->enum('blocker_type', ['user', 'company']);
            $table->unsignedBigInteger('blocker_id');
            $table->enum('blocked_type', ['user', 'company']);
            $table->unsignedBigInteger('blocked_id');
            $table->enum('action', ['block', 'unblock']);
            $table->timestamp('action_at');

            $table->index(['blocker_type', 'blocker_id']);
            $table->index(['blocked_type', 'blocked_id']);
            $table->index(['action']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('block_events');
    }
};
