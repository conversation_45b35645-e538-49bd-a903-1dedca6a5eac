document.addEventListener('DOMContentLoaded', () => {
    const currentUserId = document.querySelector('meta[name="user-id"]')?.content;
    let selectedContactId = null;

    function appendUserToUserMessage(message) {
        const container = document.getElementById("user-to-user-chat-messages");
        if (!container) return;

        const html = `<div class="${message.sender_id == currentUserId ? 'sent' : 'recieve'}">
            <div class="${message.sender_id == currentUserId ? 'sent-message' : 'recieve-message'}">
                <div class="flex-between">
                    <input type="checkbox">
                    <p class="message">${message.message}</p>
                </div>
                <span class="time">${message.time}</span>
            </div>
        </div>`;
        container.insertAdjacentHTML('beforeend', html);
        container.scrollTop = container.scrollHeight;
    }

    // subscribe only to the logged-in user's own private channel
    function subscribeToOwnChannel() {
        window.Echo.private(`chat.user.${currentUserId}`)
            .listen('UserToUserChatEvent', (e) => {
                Livewire.dispatch('chat-updated');
                appendUserToUserMessage(e.message);
            });
    }

    const sendBtn = document.getElementById("user-to-user-send-message-btn");
    if (sendBtn) {
        sendBtn.addEventListener("click", function () {
            const messageInput = document.getElementById("user-to-user-message");
            const message = messageInput.value.trim();
            if (!message || !selectedContactId) return;

            fetch('/connect-hub/send-user-to-user-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    receiver_id: selectedContactId,
                    message: message
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        // don't append here; the real-time echo will handle it
                        appendUserToUserMessage(data.message);
                        Livewire.dispatch('chat-updated');
                        messageInput.value = "";
                    } else {
                        alert('Failed to send message');
                    }
                })
                .catch(err => {
                    console.error(err);
                    alert('An error occurred while sending message');
                });
        });
    }

    window.userToUserLoadConversation = function (contactId, contactName = '', location = '') {
        selectedContactId = contactId;
        document.getElementById("chatUserName").innerText = contactName;
        document.getElementById("chatUserLocation").innerText = location;

        fetch(`/connect-hub/load-user-to-user-chat/${contactId}`)
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById("user-to-user-chat-messages");
                container.innerHTML = "";
                data.messages.forEach(msg => appendUserToUserMessage(msg));

                const modal = new bootstrap.Modal(document.getElementById('chatMessageModal'));
                modal.show();
            })
            .catch(err => {
                console.error(err);
                alert('Failed to load conversation');
            });
    };

    const chatModal = document.getElementById('chatMessageModal');
    if (chatModal) {
        chatModal.addEventListener('shown.bs.modal', () => {
            const input = document.getElementById('user-to-user-message');
            if (input) input.focus();
        });

        chatModal.addEventListener('hidden.bs.modal', () => {
            const container = document.getElementById("user-to-user-chat-messages");
            container.innerHTML = "";
            selectedContactId = null;
        });
    }


    document.addEventListener('click', function (event) {
        const btn = event.target.closest('.open-chat');
        if (!btn) return;

        const modalEl = document.getElementById("profileModal");
        if (modalEl) {
            const modalInstance = bootstrap.Modal.getInstance(modalEl) || new bootstrap.Modal(modalEl);
            modalInstance.hide();
        }
        const userId = btn.dataset.userId;
        const userName = btn.dataset.userName;
        const userLocation = btn.dataset.userLocation;
        
        Livewire.dispatch('chat-updated');

        userToUserLoadConversation(userId, userName, userLocation);
    });


    document.addEventListener('click', function (event) {
        const btn = event.target.closest('.load-dynamic-profile');
        if (!btn) return;

        const userId = btn.dataset.userId;
        if (userId) {
            loadUserProfile(userId);
        } else {
            alert('User ID not found');
        }
    });


    function loadUserProfile(userId) {
        fetch(`/ajax-get-profile/${userId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.querySelector('#dynamic-modal-content').innerHTML = data.html;
                    const modalID = document.getElementById('profileModal');
                    const targetModal = new bootstrap.Modal(modalID);
                    targetModal.show();
                } else {
                    alert(data.error || 'Failed to load user profile');
                }
            })
            .catch(error => {
                console.error('Error fetching profile:', error);
            });
    }

    const countElement = document.querySelector(".total-user-count");
    const chatListCountElement = document.getElementById("chat-list-count");

    if (countElement && chatListCountElement) {
        const count = countElement.dataset.userCount;
        if (count) {
            chatListCountElement.innerHTML = `Chat (${count})`;
        }
    }



    // Always subscribe once on page load
    subscribeToOwnChannel();
});
