<?php

namespace App\Repositories;

use App\Models\PlanFeature;
use App\Models\StripeSubscription;
use App\Models\SubscriptionActive;
use App\Models\SubscriptionDowngrade;
use App\Models\SubscriptionLog;
use App\Models\User;
use App\Services\MailService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Stripe\Price;
use Stripe\Stripe;
use Stripe\Subscription;
use App\Notifications\SubscriptionDowngradeNotification;


class StripeRepository
{

    public function handlePaymentSucceeded($invoice)
    {
        $billingReason = $invoice->billing_reason ?? null;
        \Log::debug('billing reason', ['billing' => $billingReason]);

        switch ($billingReason) {
            case 'subscription_create':
                $this->handleSubscriptionCreated($invoice); // create new subscription
                break;

            case 'subscription_cycle':
                $this->handleSubscriptionRenewed($invoice); // recurring payment
                break;

            case 'subscription_update':
                $this->handleSubscriptionSwitch($invoice); // plan switch (upgrade/downgrade)
                break;

            default:
                // Handle other cases
                break;
        }

    }


    /**
     * Handle payment failed event.
     *
     * @param  \Stripe\Invoice  $invoice
     * @return void
     */
    public function handlePaymentFailed($invoice)
    {

        \Log::debug('payment failed fired', ['payment' => $invoice]);
        $subscriptionId = $invoice->subscription;
        $customerId = $invoice->customer;

        Stripe::setApiKey(env('STRIPE_SECRET'));
        $subscription = Subscription::retrieve($subscriptionId);

        $user = User::where('stripe_customer_id', $customerId)
            ->with('company')
            ->first();

        if (!$user) {
            \Log::error("User not found for Stripe customer ID: {$customerId}");
            return;
        }



        $companyId = $user->company->id;
        $gracePeriodEnd = now()->addDays(14); // 14-day grace period

        // Create a temporary subscription record with a 14-day grace period
        $stripeProduct = collect($invoice['lines']['data'])
            ->sortByDesc(fn($lineItem) => $lineItem['period']['end']) // Sort by latest period
            ->pluck('price.product')
            ->first();

        $stripeProduct = StripeSubscription::where('stripe_product_id', $stripeProduct)->first();

        SubscriptionActive::create([
            'company_id' => $companyId,
            'stripe_subscription_id' => $stripeProduct->id,
            'subscription_id' => $subscriptionId,
            'stripe_customer_id' => $customerId,
            'user_id' => $user->id,
            'billing_cycle' => 'grace_period',
            'currency' => $invoice->currency ?? null,
            'cost' => $invoice->total / 100,
            'last_payment_status' => 'failed',
            'last_payment_date' => Carbon::createFromTimestamp($invoice->created),
            'ended_at' => $gracePeriodEnd,
        ]);

        // Schedule downgrade
        SubscriptionDowngrade::create([
            'company_id' => $companyId,
            'current_subscription_id' => $subscriptionId,
            'new_subscription_id' => 'free', // Assuming downgrade to free tier
            'stripe_customer_id' => $customerId,
            'downgrade_date' => $gracePeriodEnd,
            'downgrade_status' => 'pending'
        ]);

        \Log::info("Scheduled downgrade after 14-day grace period", [
            'company_id' => $companyId,
            'downgrade_date' => $gracePeriodEnd
        ]);

        // Notify the user about the failed payment
        if ($subscription->status === 'incomplete') {
            $this->notifyUserPaymentFailed($customerId, 'initial_payment_failed');
        } elseif ($subscription->status === 'past_due') {
            $this->notifyUserPaymentFailed($customerId, 'renewal_payment_failed');
        }


    }


    /**
     * Handle the very first subscription payment.
     */
    private function handleSubscriptionCreated($invoice, $switchProductItems = [])
    {
        \Log::info("New subscription created", ['invoice' => $invoice]);

        $currentSubscription = '';
        $subscriptionId = $invoice->subscription ?? null;
        $user = User::where('stripe_customer_id', $invoice->customer)
            ->with('company')
            ->first();

        if (!$user || !$subscriptionId) {
            \Log::error("User or subscription ID not found for invoice", ['invoice' => $invoice]);
            return;
        }


        if (!empty($switchProductItems)) {
            $stripeProduct = $switchProductItems['product'];
            $interval = $switchProductItems['interval'];
            $periodEnd = $switchProductItems['period_end'];
            $currentSubscription = SubscriptionActive::where('company_id', $user->company->id)
                ->orderBy('id', 'desc')
                ->first();

        } else {
            $stripeProduct = $invoice->lines->data[0]->plan->product;
            $interval = $invoice->lines->data[0]->price->recurring->interval ?? null;
            $periodEnd = $invoice->lines->data[0]->period->end;
        }

        // Mark all active subscriptions as ended
        SubscriptionActive::where('company_id', $user->company->id)
            ->update(['ended_at' => now()]);

        $stripeProduct = StripeSubscription::where('stripe_product_id', $stripeProduct)->first();

        if (!empty($switchProductItems)) {
            // Log the subscription upgrade
            $this->logSubscriptionChanges($currentSubscription, $stripeProduct->id, 'upgrade');
        }

        // Create a fresh subscription record
        // SubscriptionActive::create([
        //     'company_id' => $user->company->id,
        //     'stripe_subscription_id' => $stripeProduct->id,
        //     'subscription_id' => $subscriptionId,
        //     'stripe_customer_id' => $invoice->customer,
        //     'user_id' => $user->id,
        //     'billing_cycle' => $interval ?? null,
        //     'currency' => $invoice->currency ?? null,
        //     'cost' => $invoice->total / 100,
        //     'last_payment_status' => $invoice->status === 'paid' ? 'paid' : 'failed',
        //     'last_payment_date' => Carbon::createFromTimestamp($invoice->created),
        //     'ended_at' => Carbon::createFromTimestamp($periodEnd),
        // ]);


        SubscriptionActive::updateOrCreate(
            ['company_id' => $user->company->id],
            [
                'stripe_subscription_id' => $stripeProduct->id,
                'subscription_id' => $subscriptionId,
                'stripe_customer_id' => $invoice->customer,
                'user_id' => $user->id,
                'billing_cycle' => $interval ?? null,
                'currency' => $invoice->currency ?? null,
                'cost' => $invoice->total / 100,
                'last_payment_status' => $invoice->status === 'paid' ? 'paid' : 'failed',
                'last_payment_date' => Carbon::createFromTimestamp($invoice->created),
                'ended_at' => Carbon::createFromTimestamp($periodEnd),
            ]
        );


        // Notify the user about the payment successful
        $this->notifyUserPaymentFailed($invoice->customer, 'renewal_payment_failed');


        \Log::info("Subscription created successfully", ['subscription_id' => $subscriptionId]);
    }


    /**
     * Handle subscription renewals (recurring payments).
     */
    private function handleSubscriptionRenewed($invoice)
    {
        try {
            $subscriptionId = $invoice->subscription ?? null;
            $user = User::where('stripe_customer_id', $invoice->customer)
                ->with('company')
                ->first();

            if (!$user || !$subscriptionId) {
                \Log::error("User or subscription ID not found for invoice", ['invoice' => $invoice]);
                return;
            }

            $company = $user->company;
            // Check for pending downgrade
            $pendingDowngrade = SubscriptionDowngrade::where('company_id', $company->id)
                //->where('stripe_subscription_id', $subscriptionId)
                ->where('downgrade_status', 'pending')
                ->first();

            if ($pendingDowngrade) {
                // Cancel the downgrade status
                $pendingDowngrade->update(['downgrade_status' => 'cancelled']);

                \Log::info("Cancelled pending downgrade for company", ['company_id' => $company->id]);

                // Call handleSubscriptionCreated and return
                $this->handleSubscriptionCreated($invoice);
                return;
            }

            // Mark the existing subscription record as ended
            // SubscriptionActive::where('company_id', $company->id)
            //     ->update(['ended_at' => now()]);

            $billingInterval = $invoice->lines->data[0]->price->recurring->interval ?? 'month';

            // Create a fresh subscription record with ended at based on interval
            $stripeProduct = StripeSubscription::where('stripe_product_id', $invoice->lines->data[0]->plan->product)->first();
            // SubscriptionActive::create([
            //     'company_id' => $company->id,
            //     'stripe_subscription_id' => $stripeProduct->id,
            //     'subscription_id' => $subscriptionId,
            //     'stripe_customer_id' => $invoice->customer,
            //     'user_id' => $user->id,
            //     'billing_cycle' => $billingInterval,
            //     'currency' => $invoice->currency ?? null,
            //     'cost' => $invoice->total / 100,
            //     'last_payment_status' => $invoice->status === 'paid' ? 'paid' : 'failed',
            //     'last_payment_date' => Carbon::createFromTimestamp($invoice->created),
            //     'ended_at' => Carbon::createFromTimestamp($invoice->lines->data[0]->period->end),
            // ]);

            SubscriptionActive::updateOrCreate(
                ['company_id' => $company->id],
                [
                    'stripe_subscription_id' => $stripeProduct->id,
                    'subscription_id' => $subscriptionId,
                    'stripe_customer_id' => $invoice->customer,
                    'user_id' => $user->id,
                    'billing_cycle' => $billingInterval,
                    'currency' => $invoice->currency ?? null,
                    'cost' => $invoice->total / 100,
                    'last_payment_status' => $invoice->status === 'paid' ? 'paid' : 'failed',
                    'last_payment_date' => Carbon::createFromTimestamp($invoice->created),
                    'ended_at' => Carbon::createFromTimestamp($invoice->lines->data[0]->period->end),
                ]
            );
        } catch (\Exception $e) {
            Log::error("Error handling subscription renewal: " . $e->getMessage(), [
                'invoice' => $invoice,
                'trace' => $e->getTraceAsString()
            ]);
        }

    }


    private function handleSubscriptionSwitch($invoice)
    {
        $user = User::where('stripe_customer_id', $invoice->customer)->first();
        if (!$user) {
            return;
        }

        $activeSubscription = SubscriptionActive::where('user_id', $user->id)
            ->with('subscription_plan')
            ->orderBy('id', 'desc')
            ->first();

        if (!$activeSubscription) {
            return;
        }

        $currentProduct = $activeSubscription->subscription_plan->stripe_product_id;
        if (!$currentProduct) {
            return;
        }


        // Get switched product from invoice lines

        $switchedProduct = collect($invoice['lines']['data'])
            ->filter(fn($lineItem) => $lineItem['price']['product'] !== $currentProduct)
            ->pluck('price.product')
            ->last();



        $switchedProductLine = collect($invoice['lines']['data'])
            ->last(fn($lineItem) => $lineItem['price']['product'] !== $currentProduct);

        $interval = $switchedProductLine['price']['recurring']['interval'] ?? null;
        $periodEnd = $switchedProductLine['period']['end'] ?? null;


        $switchProductItems = [
            'product' => $switchedProduct,
            'interval' => $interval,
            'period_end' => $periodEnd
        ];

        if (!$switchedProduct) {
            return;
        }

        // Retrieve subscription rank for both products
        $productRanks = StripeSubscription::whereIn('stripe_product_id', [$currentProduct, $switchedProduct])
            ->pluck('subscription_rank', 'stripe_product_id');

        if (
            isset($productRanks[$currentProduct], $productRanks[$switchedProduct]) &&
            $productRanks[$currentProduct] > $productRanks[$switchedProduct]
        ) {
            \Log::debug("Subscription downgrading...");
            $this->handleSubscriptionDowngrade($invoice, $switchProductItems);
        } else {
            \Log::debug("Subscription creating...");
            $this->handleSubscriptionCreated($invoice, $switchProductItems);
        }
    }



    public function handleSubscriptionDowngrade($invoice, $switchProductItems = [])
    {
        $customer = $invoice->customer;
        //$newSubscriptionId = $invoice->subscription;

        //$stripeProduct = StripeSubscription::where('stripe_product_id', $invoice->lines->data[0]->plan->product)->first();

        $stripeProduct = StripeSubscription::where('stripe_product_id', $switchProductItems['product'])->first();

        // Fetch the current active subscription
        $currentSubscription = SubscriptionActive::where('stripe_customer_id', $customer)
            ->where('last_payment_status', 'paid')
            ->orderBy('id', 'desc')
            ->first();

        $user = User::with('company')->where('stripe_customer_id', $customer)->first();

        if (!$currentSubscription) {
            Log::warning("No active subscription found for customer: {$customer}");
            return;
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            // Retrieve the current subscription from Stripe
            //$stripeSubscription = Subscription::retrieve($currentSubscription->subscription_id);
            //$currentPeriodEnd = Carbon::createFromTimestamp($stripeSubscription->current_period_end);

            // Log downgrade details
            SubscriptionDowngrade::create([
                'company_id' => $currentSubscription->company_id,
                'current_subscription_id' => $currentSubscription->stripe_subscription_id,
                'new_subscription_id' => $stripeProduct->id,
                'subscription_id' => $currentSubscription->subscription_id,
                'stripe_customer_id' => $customer,
                'downgrade_date' => $currentSubscription->ended_at,
                'downgrade_status' => 'pending'
            ]);
            //notify the downgrade
            $this->notifyDowngradePlanToCompany($user, $stripeProduct->id);

            // Log the downgrade
            $this->logSubscriptionChanges($currentSubscription, $stripeProduct->id, 'downgrade');

        } catch (\Exception $e) {
            Log::error("Error retrieving subscription from Stripe for customer {$customer}: " . $e->getMessage());
        }
    }


    protected function logSubscriptionChanges($currentSubscription, $newSubscriptionId, $changeType)
    {
        SubscriptionLog::create([
            'company_id' => $currentSubscription->company_id,
            'previous_subscription_id' => $currentSubscription->stripe_subscription_id,
            'new_subscription_id' => $newSubscriptionId,
            'stripe_customer_id' => $currentSubscription->stripe_customer_id,
            'user_id' => $currentSubscription->user_id,
            'change_type' => $changeType,
            'change_initiator' => 'system',
            'change_date' => now(),
        ]);
    }






    public function notifyUserPaymentFailed($customerId, $type)
    {
        try {
            // Retrieve customer details from Stripe
            Stripe::setApiKey(env('STRIPE_SECRET'));
            $stripeCustomer = \Stripe\Customer::retrieve($customerId);
            $user = User::where('email', $stripeCustomer->email)->first();

            if ($user) {
                // Send notification to user
                $user->notify(new \App\Notifications\PaymentFailed($type));
            } else {
                Log::warning("User not found for Stripe customer ID: {$customerId} ({$stripeCustomer->email}).");
            }
        } catch (\Exception $e) {
            Log::error("Error sending payment failure notification: " . $e->getMessage());
        }
    }


    public function notifyDowngradePlanToCompany($user, $subscriptionId)
    {
        try {
            $user->notify(new SubscriptionDowngradeNotification($user->company?->name, $subscriptionId));
        } catch (\Exception $e) {
            Log::error("Error sending payment failure notification: " . $e->getMessage());
        }
    }


    public function handleProductCreated($product)
    {
        \Log::info("Product created", ['product' => $product]);
        $prices = $this->extractPrices($product);

        $stripeProduct = StripeSubscription::create([
            'stripe_product_id' => $product->id,
            'name' => $product->name,
            'description' => $product->description ?? null,
            'features_list' => json_encode($product->marketing_features ?? []),
            'gbp_price_monthly' => $prices['gbp_price_monthly'],
            'gbp_price_annual' => $prices['gbp_price_annual'],
            'euro_price_monthly' => $prices['euro_price_monthly'],
            'euro_price_annual' => $prices['euro_price_annual'],
            'usd_price_monthly' => $prices['usd_price_monthly'],
            'usd_price_annual' => $prices['usd_price_annual'],
            'subscription_rank' => $product->metadata->rank ?? 0,
        ]);

        if ($stripeProduct && !empty($product->marketing_features)) {

            foreach ($product->marketing_features as $feature) {
                PlanFeature::create([
                    'stripe_subscription_id' => $stripeProduct->id,
                    'name' => $feature['name'],
                    'description' => $feature['name']
                ]);
            }
        }
    }


    public function handleProductUpdated($product)
    {
        $subscription = StripeSubscription::where('stripe_product_id', $product->id)->first();

        $prices = $this->extractPrices($product);

        if ($subscription) {
            $subscription->update([
                'name' => $product->name,
                'description' => $product->description ?? null,
                'features_list' => json_encode($product->marketing_features ?? []),
                'gbp_price_monthly' => $prices['gbp_price_monthly'],
                'gbp_price_annual' => $prices['gbp_price_annual'],
                'euro_price_monthly' => $prices['euro_price_monthly'],
                'euro_price_annual' => $prices['euro_price_annual'],
                'usd_price_monthly' => $prices['usd_price_monthly'],
                'usd_price_annual' => $prices['usd_price_annual'],
                'subscription_rank' => $product->metadata->rank ?? 0,
            ]);

            if ($product && !empty($product->marketing_features)) {
                foreach ($product->marketing_features as $feature) {
                    PlanFeature::updateOrCreate(
                        [
                            'stripe_subscription_id' => $subscription->id,
                            'name' => $feature['name'],
                        ],
                        [
                            'description' => $feature['name']
                        ]
                    );
                }

                PlanFeature::where('stripe_subscription_id', $subscription->id)
                    ->whereNotIn('name', array_column($product->marketing_features, 'name'))
                    ->delete();
            }


        } else {
            Log::warning("Product update received, but no matching record found: {$product->id}");
        }
    }

    public function handleProductDeleted($product)
    {
        $subscription = StripeSubscription::where('stripe_product_id', $product->id)->first();

        if ($subscription) {
            $subscription->update(['is_active' => false]);
            Log::info("Product marked as inactive: {$product->id}");
        } else {
            Log::warning("Product delete event received, but no matching record found: {$product->id}");
        }
    }


    /**
     * Extracts and formats price data from the product object.
     */
    private function extractPrices($product)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $prices = Price::all(['product' => $product->id, 'expand' => ['data.currency_options']]);
        $priceMap = [
            'GBP' => ['month_gbp' => 'gbp_price_monthly', 'year_gbp' => 'gbp_price_annual'],
            'EUR' => ['month_eur' => 'euro_price_monthly', 'year_eur' => 'euro_price_annual'],
            'USD' => ['month_usd' => 'usd_price_monthly', 'year_usd' => 'usd_price_annual'],
        ];

        $formattedPrices = array_fill_keys(array_merge(...array_values($priceMap)), null);


        if (!empty($prices['data'][0]['currency_options'])) {
            $currencyOptions = json_decode(json_encode($prices['data'][0]['currency_options']), true);

            foreach ($currencyOptions as $currency => $price) {
                $currency = strtoupper($currency);
                $interval = strtolower($prices['data'][0]['recurring']['interval'] ?? '');

                $amount = isset($price['unit_amount_decimal']) ? $price['unit_amount_decimal'] / 100 : null;

                if (isset($priceMap[$currency][$interval . '_' . strtolower($currency)])) {
                    $formattedPrices[$priceMap[$currency][$interval . '_' . strtolower($currency)]] = $amount;
                }
            }
        } else {
            Log::warning("No prices found for product.");
        }

        return $formattedPrices;
    }


}
