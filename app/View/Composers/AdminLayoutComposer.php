<?php

namespace App\View\Composers;

use Illuminate\View\View;
use App\Services\SettingService;

class AdminLayoutComposer
{
    public function compose(View $view)
    {
        $settingKeys = ['title', 'favicon', 'logo'];
        $global = app(SettingService::class)->valueByKeys($settingKeys);

        $view->with('site_title', $global['title'] ?? 'Listing Website');
        $view->with('site_logo', $global['logo'] ?? null);
        $view->with('favicon', $global['favicon'] ?? null);
    }
}
