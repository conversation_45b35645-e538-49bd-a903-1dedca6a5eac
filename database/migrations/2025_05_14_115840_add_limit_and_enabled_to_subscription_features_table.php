<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_features', function (Blueprint $table) {
            $table->tinyInteger('enabled')
            ->default(0)
            ->after('feature_id')
            ->comment('1 = Feature enabled, 0 = Feature disabled');

            $table->integer('limit')
            ->nullable()
            ->after('enabled')
            ->comment('Limit value if applicable (e.g., 3 additional users, 50 products)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_features', function (Blueprint $table) {
            $table->dropColumn(['enabled', 'limit']);
        });
    }
};
