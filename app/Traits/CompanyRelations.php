<?php

namespace App\Traits;

use App\Models\Category;
use App\Models\Company;
use App\Models\CompanyLocation;
use App\Models\Country;
use App\Models\LocationType;
use App\Models\OrganisationSize;
use App\Models\OrganisationType;
use App\Models\User;
use App\Models\UserCompany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait CompanyRelations
{
    /**
     * Get the organisation type that owns the company.
     */
    public function organisationType()
    {
        return $this->belongsTo(OrganisationType::class);
    }

    /**
     * Get the organisation size that owns the company.
     */
    public function organisationSize()
    {
        return $this->belongsTo(OrganisationSize::class);
    }

    /**
     * Get the company associated with the user.
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class);
    }

    /**
     * Get the user associated with the supplier request.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


    /**
     * Get the business sector associated with the supplier request.
     */

    public function businessSectors(): BelongsToMany
    {
        return $this->belongsToMany(
            Category::class,
            'company_business_categories',
            'company_id',
            'level_2_category_id'
        );
    }

    /**
     * Get the location type associated with the supplier request.
     */
    public function locationType(): BelongsTo
    {
        return $this->belongsTo(LocationType::class);
    }

    /**
     * Get the locations associated with the supplier request.
     */
    public function locations(): HasMany
    {
        return $this->hasMany(CompanyLocation::class, 'company_id', 'id');
    }

    /**
     * Get the company country associated with the supplier request.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get the supplier  company associated with the supplier request.
     */
    public function supplier_company(): BelongsTo
    {
        return $this->belongsTo(UserCompany::class, 'user_id', 'user_id');
    }

    /**
     * Get the supplier position associated with the supplier request.
     */
    public function supplier_position(): BelongsTo
    {
        return $this->belongsTo(UserCompany::class, 'user_id', 'user_id');
    }
}
