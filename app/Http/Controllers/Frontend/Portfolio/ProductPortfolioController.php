<?php

namespace App\Http\Controllers\Frontend\Portfolio;

use Carbon\Carbon;
use App\Models\Unit;
use App\Models\Company;
use App\Models\Country;
use App\Models\Product;
use App\Models\Category;
use App\Models\GeoRegion;
use App\Models\GeoCountry;
use App\Models\KeyFeature;
use Faker\Factory as Faker;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\ProductPricing;
use App\Models\ProductShipping;
use App\Models\AfterSaleSupport;
use App\Models\ProductAttribute;
use App\Services\CategoryService;
use Illuminate\Support\Facades\DB;
use App\Search\ElasticsearchEngine;
use App\Http\Controllers\Controller;
use App\Models\ProductCertification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use App\Models\ProductStandardsCompliance;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Requests\Frontend\Portfolio\CreateProductPortfolioRequest;
use App\Http\Requests\Frontend\Portfolio\UpdateProductPortfolioRequest;

class ProductPortfolioController extends Controller
{
    protected $categoryService;
    /**
     * Display a listing of products and services with search and filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;;
    }
    public function index(Request $request)
    {        
        $dropdownItems = [
            'id' => 'ID',
            'price_low_high' => 'Price (Low-to-High)',
            'price_high_low' => 'Price (High-to-Low)',
            'newly_added' => 'Newly Added',
        ];
        $queryParams = request()->all();
        $queryParams['type'] = 'product';
        $queryParams['company_id'] = Auth::user()->company->id;
            
        $rawResults = Product::search($queryParams)->raw();

        $activeCount = collect($rawResults['results'])
                        ->where('listing_status', 'active')
                        ->count();
        $draftCount = collect($rawResults['results'])
                            ->where('listing_status', 'inactive')
                            ->count();
        $totalCount = collect($rawResults['results'])->count();
        // Extract all product IDs from rawResults
        $productIds = collect($rawResults['results'])->pluck('_source.id')->toArray();        
        $idsString = implode(',', $productIds);
        // Fetch products from the Product model using the extracted IDs
        $products = Product::whereIn('id', $productIds);
        if ($idsString !== '') {
            // Ensure $idsString is properly escaped and safe
            $products = $products->orderByRaw("FIELD(id, $idsString)");
        }
        $products = $products->get();
        $getCategoryFilterData = $this->categoryService->getCategoryFilterData(true, 'product');

        return view('frontend.portfolio.product.index', compact('products', 'totalCount', 'activeCount', 'draftCount', 'dropdownItems', 'queryParams', 'getCategoryFilterData'));
    }



    // Show a single product
    public function show($uuid)
    {
        $product = Product::where('uuid', $uuid)->with(['categories', 'keyFeatures', 'afterSaleSupports', 'pricings', 'shippings', 'attributes', 'certifications', 'standardsCompliances'])->firstOrFail();
        // dd($product->categories);
        $user = Auth::user();
        $company = $user->company;

        $categories = Category::where('type', 1)
            ->where('status', 1)
            ->get();

        $features = KeyFeature::all();
        $afterSaleSupport = AfterSaleSupport::all();
        $regions = Country::all();
        $unit = Unit::all();
        return view('frontend.portfolio.product.show', compact('product', 'categories', 'features', 'afterSaleSupport', 'regions', 'company', 'unit'));
    }

    public function create()
    {
        $user = Auth::user();
        $company = $user->company;

        $categories = Category::where('type', 1)
            ->where('status', 1)
            ->get();
        $groupedCategories = $categories->groupBy('level');
        $features = KeyFeature::all();
        $afterSaleSupport = AfterSaleSupport::all();
        $unit = Unit::all();
        $regions = Country::all();

        return view('frontend.portfolio.product.create', compact('categories', 'groupedCategories', 'features', 'afterSaleSupport', 'regions', 'company', 'unit'));
    }


    public function store(CreateProductPortfolioRequest $request)
    {
        $featureIds = [];

        DB::beginTransaction();

        try {
            $existingProduct = Product::where('name', $request->name)
                ->where('company_id', $request->company_id)
                ->first();
            if ($existingProduct) {
                $product = $existingProduct;
            } else {
                $product = new Product();
            }

            $product->name = $request->name ?? null;
            $product->description = $request->product_description ?? null;
            $product->region_restrictions = $request->region_restrictions ?? null;
            $product->company_id = $request->company_id ?? null;
            $product->additional_pricing_detail = $request->additional_pricing_detail ?? null;
            $product->additional_shiping_rate_detail = $request->additional_shiping_rate_detail ?? null;
            $product->compliance_summary = $request->compliance_summary ?? null;
            $product->listing_duration = $request->listing_duration ?? null;

            if ($request->status == "save") {
                $product->listing_status = "Inactive";
            } else if ($request->status == "publish") {
                $product->listing_status = "Active";
            }
            if ($request->listing_duration === "Publish") {
                $product->listing_release_date = Carbon::now()->format('Y-m-d'); // Set to today's date
            } else {
                $product->listing_release_date = $request->listing_release_date
                    ? Carbon::createFromFormat('m/d/Y', $request->listing_release_date)->format('Y-m-d')
                    : null;
            }
            $product->listing_end_date = $request->listing_end_date ? Carbon::createFromFormat('m/d/Y', $request->listing_end_date)->format('Y-m-d') : null;
            $product->type = "product";
            $product->save();

            foreach ($request->key_feature as $featureName) {
                $feature = KeyFeature::firstOrCreate(['feature' => $featureName]); // Create if not exists
                $featureIds[] = $feature->id; // Collect feature IDs
            }
            // $regionIds[] = $request->regions;

            $product->categories()->sync((array) $request->product_category);
            $product->afterSaleSupports()->sync($request->after_sales_support);
            $product->keyFeatures()->sync($featureIds);
            // $product->regions()->sync($regionIds);

            // Store document files with titles
            if ($request->hasFile('upload_document')) {
                foreach ($request->file('upload_document') as $index => $document) {
                    $media = $product->addMedia($document)
                        ->toMediaCollection('documents');

                    // Add custom property for document title if provided
                    if (!empty($request->document_title[$index])) {
                        $media->setCustomProperty('document_title', $request->document_title[$index]);
                        $media->save();
                    }
                }
            }

            // Get the array of part numbers from the request
            $submittedPartNumbers = collect($request->product_prices)->pluck('part_number')->toArray();

            // Delete product prices that are not in the request
            ProductPricing::where('product_id', $product->id)
                ->whereNotIn('part_number', $submittedPartNumbers)
                ->delete();

            // Insert or update product pricing records
            if (!empty($request->product_prices)) {
                foreach ($request->product_prices as $price) {

                    ProductPricing::updateOrCreate(
                        [
                            'product_id' => $product->id, // Search criteria
                            'part_number' => $price['part_number']
                        ],
                        [
                            'price' => $price['price'] ?? null,
                            'per_unit' => $price['unit'] ?? null,
                            'min_order' => $price['min_order'] ?? null,
                            'payment_method' => $price['payment_terms'] ?? null,
                            'notes' => $price['notes'] ?? null
                        ]
                    );
                }
            }

            // Get the array of destination names from the request
            $submittedDestinations = collect($request->shipping_details)->pluck('destination')->toArray();
            // Delete product shipping records that are not in the request
            ProductShipping::where('product_id', $product->id)
                ->whereNotIn('destination', $submittedDestinations)
                ->delete();

            if (!empty($request->shipping_details)) {
                // Insert or update product shiping records
                foreach ($request->shipping_details as $shipping) {
                    ProductShipping::updateOrCreate(
                        [
                            'product_id' => $product->id,
                            'destination' => $shipping['destination']
                        ],
                        [
                            'shipping_method' => $shipping['shipping_method'] ?? null,
                            'estimated_delivery_time' => $shipping['shipping_time'] ?? null,
                            'shipping_rate' => $shipping['shipping_rate'] ?? null,
                            'notes' => $shipping['notes'] ?? null
                        ]
                    );
                }
            }

            // Get submitted attribute names
            $submittedAttributes = collect($request->product_attributes)->pluck('name')->toArray();

            // Delete attributes that are not in the request
            ProductAttribute::where('product_id', $product->id)
                ->whereNotIn('attribute', $submittedAttributes)
                ->delete();

            if (!empty($request->product_attributes)) {
                // Insert or update attributes
                foreach ($request->product_attributes as $attribute) {
                    ProductAttribute::updateOrCreate(
                        [
                            'product_id' => $product->id,
                            'attribute' => $attribute['name']
                        ],
                        [
                            'characteristic' => $attribute['characteristic'] ?? null
                        ]
                    );
                }
            }

            // Get the array of certification titles from the request
            $submittedCertificates = collect($request->product_certificates)->pluck('title')->toArray();

            // Delete certifications that are not in the request
            ProductCertification::where('product_id', $product->id)
                ->whereNotIn('certificate_title', $submittedCertificates)
                ->delete();

            if (!empty($request->product_certificates)) {
                // Insert or update certifications
                foreach ($request->product_certificates as $certificate) {
                    ProductCertification::updateOrCreate(
                        [
                            'product_id' => $product->id, // Search criteria
                            'certificate_title' => $certificate['title']
                        ],
                        [
                            'certification_body' => $certificate['body'] ?? null,
                            'certification_number' => $certificate['number'] ?? null,
                            'notes' => $certificate['notes'] ?? null
                        ]
                    );
                }
            }

            // Get the array of standard names from the request
            $submittedStandards = collect($request->product_compliance)->pluck('standard')->toArray();

            // Delete standards that are not in the request
            ProductStandardsCompliance::where('product_id', $product->id)
                ->whereNotIn('standard_name', $submittedStandards)
                ->delete();

            if (!empty($request->product_compliance)) {
                // Insert or update compliance records
                foreach ($request->product_compliance as $compliance) {
                    ProductStandardsCompliance::updateOrCreate(
                        [
                            'product_id' => $product->id, // Search criteria
                            'standard_name' => $compliance['standard']
                        ],
                        [
                            'notes' => $compliance['details'] ?? null // Update notes if standard exists
                        ]
                    );
                }
            }

            if ($request->useCompanySupplyLocation == "true") {
                $product->product_continents()->delete();
                $product->product_countries()->delete();
                $product->product_regions()->delete();
                $company = Company::find($request->company_id);
                $allRegions = $company->company_regions();

                // Saving continents, countries and regions from company
                $insertedContinents = [];

                foreach ($allRegions->get() as $regionData) {
                    $region = GeoRegion::find($regionData->region_id);
                
                    if (!$region) {
                        continue; // Skip if region is not found
                    }
            
                    $country = GeoCountry::find($region->country_id);
            
                    if (!$country) {
                        continue; // Skip if country is not found
                    }
            
                    // Ensure continent is inserted only once
                    if (!isset($insertedContinents[$country->continent_id])) {
                        $productContinent = $product->product_continents()->create([
                            'product_id' => $product->id,
                            'continent_id' => $country->continent_id
                        ]);
                        $insertedContinents[$country->continent_id] = $productContinent->id;
                    }
            
                    // Ensure country is inserted only once
                    if (!isset($insertedCountries[$region->country_id])) {
                        $productCountry = $product->product_countries()->create([
                            'product_id' => $product->id,
                            'country_id' => $region->country_id,
                            'product_continent_id' => $insertedContinents[$country->continent_id] // Link to continent
                        ]);
                        $insertedCountries[$region->country_id] = $productCountry->id;
                    }
            
                    // Insert region
                    $product->product_regions()->create([
                        'product_id' => $product->id,
                        'region_id' => $regionData->region_id,
                        'product_country_id' => $insertedCountries[$region->country_id] // Link to country
                    ]);
                }
            } else {
                //Saving continents, countries and regions
                if(isset($request->continents)){
                    $product->product_continents()->delete();
                    $product->product_countries()->delete();
                    $product->product_regions()->delete();
                    for($i=0;$i<count($request->continents);$i++){
                        $productContinent = $product->product_continents()->create([
                            'product_id' => $product->id,
                            'continent_id' => $request->continents[$i]
                        ]);
                        GeoCountry::where('continent_id', $request->continents[$i])->get()->each(function($country) use ($product, $productContinent){
                            $productCountry = $product->product_countries()->create([
                                'product_id' => $product->id,
                                'country_id' => $country->id,
                                'product_continent_id' => $productContinent->id
                            ]);
                            GeoRegion::where('country_id', $country->id)->get()->each(function($region) use ($product,$productCountry){
                                $product->product_regions()->create([
                                    'product_id' => $product->id,
                                    'region_id' => $region->id,
                                    'product_country_id' => $productCountry->id
                                ]);
                            });
                        });
                    }
                }
                if(isset($request->countries)){
                    $product->product_continents()->delete();
                    $product->product_countries()->delete();
                    $product->product_regions()->delete();
                    $insertedContinents = [];
                    for($j=0;$j<count($request->countries);$j++){
                        $country = GeoCountry::find($request->countries[$j]);
                         // Ensure continent is inserted only once
                         if (!isset($insertedContinents[$country->continent_id])) {
                            $productContinent = $product->product_continents()->create([
                                'product_id' => $product->id,
                                'continent_id' => $country->continent_id
                            ]);
                            $insertedContinents[$country->continent_id] = $productContinent->id;
                        }
                        $productCountry = $product->product_countries()->create([
                            'product_id' => $product->id,
                            'country_id' => $request->countries[$j],
                            'product_continent_id' => $insertedContinents[$country->continent_id]
    
                        ]);
    
                        GeoRegion::where('country_id', $request->countries[$j])->get()->each(function($region) use ($product, $productCountry){
                            $product->product_regions()->create([
                                'product_id' => $product->id,
                                'region_id' => $region->id,
                                'product_country_id' => $productCountry->id
                            ]);
                        });
                    }
                    
                }
                if (isset($request->regions)) {
                    // Delete old data
                    $product->product_continents()->delete();
                    $product->product_countries()->delete();
                    $product->product_regions()->delete();
                
                    // Arrays to track inserted continents and countries
                    $insertedContinents = [];
                    $insertedCountries = [];
                
                    foreach ($request->regions as $regionId) {
                        $region = GeoRegion::find($regionId);
                
                        if (!$region) {
                            continue; // Skip if region is not found
                        }
                
                        $country = GeoCountry::find($region->country_id);
                
                        if (!$country) {
                            continue; // Skip if country is not found
                        }
                
                        // Ensure continent is inserted only once
                        if (!isset($insertedContinents[$country->continent_id])) {
                            $productContinent = $product->product_continents()->create([
                                'product_id' => $product->id,
                                'continent_id' => $country->continent_id
                            ]);
                            $insertedContinents[$country->continent_id] = $productContinent->id;
                        }
                
                        // Ensure country is inserted only once
                        if (!isset($insertedCountries[$region->country_id])) {
                            $productCountry = $product->product_countries()->create([
                                'product_id' => $product->id,
                                'country_id' => $region->country_id,
                                'product_continent_id' => $insertedContinents[$country->continent_id] // Link to continent
                            ]);
                            $insertedCountries[$region->country_id] = $productCountry->id;
                        }
                
                        // Insert region
                        $product->product_regions()->create([
                            'product_id' => $product->id,
                            'region_id' => $regionId,
                            'product_country_id' => $insertedCountries[$region->country_id] // Link to country
                        ]);
                    }
                }
            }
            DB::commit(); // ✅ Commit the transaction if everything is successful
        } catch (\Exception $e) {
            DB::rollBack(); // ❌ Rollback on error
            return response()->json([
                'success' => false,
                'message' => $e->getMessage() . $e->getLine()
            ], 500);
        }

        // Store all image after all other data has been saved
        
        // Store both original and cropped images
        if ($request->hasFile('org_product_main_image') && $request->hasFile('crp_product_main_image')) {
            // Store original image / 
            $product->addMediaFromRequest('org_product_main_image')
                    ->toMediaCollection('org_product_main_image');
                    
            // Store cropped image
            $product->addMediaFromRequest('crp_product_main_image')
                    ->toMediaCollection('crp_product_main_image');
            // Store cropped image as main image
            // $product->addMediaFromRequest('crp_product_main_image')
            //         ->toMediaCollection('main_image');
        }

        // Store main image
        if ($request->hasFile('main_image')) {
            $product->addMedia($request->file('main_image'))
                ->toMediaCollection('main_image');
        }

        // Store thumbnail images
        if ($request->hasFile('thumb_images')) {
            foreach ($request->file('thumb_images') as $thumb) {
                $product->addMedia($thumb)
                    ->toMediaCollection('thumb_images');
            }
        }
        if ($request->status == "save") {
            $status = "drafted";
        } else {
            $status = "published";
        }

        return response()->json([
            'success' => true,
            'message' => "Product " . $status . " successfully.",
            'redirect_url' => route('frontend.portfolio.portfolio-product.index'),
            'modalToShow' => config('settings.modal.confirmation_modal')
        ], 201);
    }


    public function edit($uuid)
    {
        $product = Product::where('uuid', $uuid)->with(['categories', 'keyFeatures', 'afterSaleSupports', 'pricings', 'shippings', 'attributes', 'certifications', 'standardsCompliances'])->firstOrFail();

        $user = Auth::user();
        $company = $user->company;

        $categories = Category::where('type', 1)
            ->where('status', 1)
            ->get();
        $groupedCategories = $categories->groupBy('level');



        $features = KeyFeature::all();
        $afterSaleSupport = AfterSaleSupport::all();
        $regions = Country::all();
        $unit = Unit::all();
        return view('frontend.portfolio.product.edit', compact('product', 'categories', 'groupedCategories', 'features', 'afterSaleSupport', 'company', 'regions', 'unit'));
    }

    public function update(UpdateProductPortfolioRequest $request, $uuid)
    {
        $featureIds = $regionIds = [];
        DB::beginTransaction();
        $product = Product::where('uuid', $uuid)->firstOrFail();
        try {
            $product->name = $request->name ?? null;
            $product->description = $request->product_description ?? null;
            $product->region_restrictions = $request->region_restrictions ?? null;
            $product->company_id = $request->company_id ?? null;
            $product->additional_pricing_detail = $request->additional_pricing_detail ?? null;
            $product->additional_shiping_rate_detail = $request->additional_shiping_rate_detail ?? null;
            $product->compliance_summary = $request->compliance_summary ?? null;
            $product->listing_duration = $request->listing_duration ?? null;
            $product->listing_status = $request->listing_status ?? null;
            if ($request->status == "save") {
                $product->listing_status = "Inactive";
            } else if ($request->status == "publish") {
                $product->listing_status = "Active";
            }
            if ($request->listing_duration === "Publish") {
                $product->listing_release_date = Carbon::now()->format('Y-m-d'); // Set to today's date
            } else {
                $product->listing_release_date = $request->listing_release_date
                    ? Carbon::createFromFormat('m/d/Y', $request->listing_release_date)->format('Y-m-d')
                    : null;
            }
            $product->listing_end_date = $request->listing_end_date ? Carbon::createFromFormat('m/d/Y', $request->listing_end_date)->format('Y-m-d') : null;
            $product->type = "product";
            $product->save();

            foreach ($request->key_feature as $featureName) {
                $feature = KeyFeature::firstOrCreate(['feature' => $featureName]); // Create if not exists
                $featureIds[] = $feature->id; // Collect feature IDs
            }
            // $regionIds[] = $request->regions;

            $product->categories()->sync((array) $request->product_category);
            $product->afterSaleSupports()->sync($request->after_sales_support);
            $product->keyFeatures()->sync($featureIds);
            // $product->regions()->sync($regionIds);

            // Store document files with titles
            if ($request->hasFile('upload_document')) {
                foreach ($request->file('upload_document') as $index => $document) {
                    $media = $product->addMedia($document)
                        ->toMediaCollection('documents');

                    // Add custom property for document title if provided
                    if (!empty($request->document_title[$index])) {
                        $media->setCustomProperty('document_title', $request->document_title[$index]);
                        $media->save();
                    }
                }
            }

            // Get the array of part numbers from the request
            $submittedPartNumbers = collect($request->product_prices)->pluck('part_number')->toArray();

            // Delete product prices that are not in the request
            ProductPricing::where('product_id', $product->id)
                ->whereNotIn('part_number', $submittedPartNumbers)
                ->delete();
            if (!empty($request->product_prices)) {
                // Insert or update product pricing records
                foreach ($request->product_prices as $price) {
                    ProductPricing::updateOrCreate(
                        [
                            'product_id' => $product->id, // Search criteria
                            'part_number' => $price['part_number']
                        ],
                        [
                            'price' => $price['price'] ?? null,
                            'per_unit' => $price['unit'] ?? null,
                            'min_order' => $price['min_order'] ?? null,
                            'payment_method' => $price['payment_terms'] ?? null,
                            'notes' => $price['notes'] ?? null
                        ]
                    );
                }
            }

            // Get the array of destination names from the request
            $submittedDestinations = collect($request->shipping_details)->pluck('destination')->toArray();
            // Delete product shipping records that are not in the request
            ProductShipping::where('product_id', $product->id)
                ->whereNotIn('destination', $submittedDestinations)
                ->delete();
            if (!empty($request->shipping_details)) {
                // Insert or update product pricing records
                foreach ($request->shipping_details as $shipping) {
                    ProductShipping::updateOrCreate(
                        [
                            'product_id' => $product->id,
                            'destination' => $shipping['destination']
                        ],
                        [
                            'shipping_method' => $shipping['shipping_method'] ?? null,
                            'estimated_delivery_time' => $shipping['shipping_time'] ?? null,
                            'shipping_rate' => $shipping['shipping_rate'] ?? null,
                            'notes' => $shipping['notes'] ?? null
                        ]
                    );
                }
            }


            // Get submitted attribute names
            $submittedAttributes = collect($request->product_attributes)->pluck('name')->toArray();

            // Delete attributes that are not in the request
            ProductAttribute::where('product_id', $product->id)
                ->whereNotIn('attribute', $submittedAttributes)
                ->delete();
            if (!empty($request->product_attributes)) {
                // Insert or update attributes
                foreach ($request->product_attributes as $attribute) {
                    ProductAttribute::updateOrCreate(
                        [
                            'product_id' => $product->id,
                            'attribute' => $attribute['name']
                        ],
                        [
                            'characteristic' => $attribute['characteristic'] ?? null
                        ]
                    );
                }
            }


            // Get the array of certification titles from the request
            $submittedCertificates = collect($request->product_certificates)->pluck('title')->toArray();

            // Delete certifications that are not in the request
            ProductCertification::where('product_id', $product->id)
                ->whereNotIn('certificate_title', $submittedCertificates)
                ->delete();
            if (!empty($request->product_certificates)) {
                // Insert or update certifications
                foreach ($request->product_certificates as $certificate) {
                    ProductCertification::updateOrCreate(
                        [
                            'product_id' => $product->id, // Search criteria
                            'certificate_title' => $certificate['title']
                        ],
                        [
                            'certification_body' => $certificate['body'] ?? null,
                            'certification_number' => $certificate['number'] ?? null,
                            'notes' => $certificate['notes'] ?? null
                        ]
                    );
                }
            }


            // Get the array of standard names from the request
            $submittedStandards = collect($request->product_compliance)->pluck('standard')->toArray();

            // Delete standards that are not in the request
            ProductStandardsCompliance::where('product_id', $product->id)
                ->whereNotIn('standard_name', $submittedStandards)
                ->delete();
            if (!empty($request->product_compliance)) {
                // Insert or update compliance records
                foreach ($request->product_compliance as $compliance) {
                    ProductStandardsCompliance::updateOrCreate(
                        [
                            'product_id' => $product->id, // Search criteria
                            'standard_name' => $compliance['standard']
                        ],
                        [
                            'notes' => $compliance['details'] ?? null // Update notes if standard exists
                        ]
                    );
                }
            }

            if($request->useCompanySupplyLocation == "true"){
                $company = Company::find($request->company_id);
                $allContinents = $company->company_continents();
                $allCountries = $company->company_countries();
                $allRegions = $company->company_regions();
                // Saving continents, countries and regions from company
                foreach ($allContinents->get() as $continent) {
                    $product->product_continents()->create([
                        'product_id' => $product->id,
                        'continent_id' => $continent->continent_id
                    ]);
                }

                foreach ($allCountries->get() as $country) {
                    $productCountry = $product->product_countries()->create([
                        'product_id' => $product->id,
                        'country_id' => $country->country_id,
                        'product_continent_id' => $country->company_continent_id
                    ]);
                }
                foreach ($allRegions->get() as $region) {
                    $product->product_regions()->create([
                        'product_id' => $product->id,
                        'region_id' => $region->region_id,
                        'product_country_id' => $region->company_country_id
                    ]);
                }
            } else {
                //Saving continents, countries and regions
                if(isset($request->continents)){
                    $product->product_continents()->delete();
                    $product->product_countries()->delete();
                    $product->product_regions()->delete();
                    for($i=0;$i<count($request->continents);$i++){
                        $productContinent = $product->product_continents()->create([
                            'product_id' => $product->id,
                            'continent_id' => $request->continents[$i]
                        ]);
                        GeoCountry::where('continent_id', $request->continents[$i])->get()->each(function($country) use ($product, $productContinent){
                            $productCountry = $product->product_countries()->create([
                                'product_id' => $product->id,
                                'country_id' => $country->id,
                                'product_continent_id' => $productContinent->id
                            ]);
                            GeoRegion::where('country_id', $country->id)->get()->each(function($region) use ($product,$productCountry){
                                $product->product_regions()->create([
                                    'product_id' => $product->id,
                                    'region_id' => $region->id,
                                    'product_country_id' => $productCountry->id
                                ]);
                            });
                        });
                    }
                }
                if(isset($request->countries)){
                    $product->product_continents()->delete();
                    $product->product_countries()->delete();
                    $product->product_regions()->delete();
                    $insertedContinents = [];
                    for($j=0;$j<count($request->countries);$j++){
                        $country = GeoCountry::find($request->countries[$j]);
                         // Ensure continent is inserted only once
                         if (!isset($insertedContinents[$country->continent_id])) {
                            $productContinent = $product->product_continents()->create([
                                'product_id' => $product->id,
                                'continent_id' => $country->continent_id
                            ]);
                            $insertedContinents[$country->continent_id] = $productContinent->id;
                        }
                        $productCountry = $product->product_countries()->create([
                            'product_id' => $product->id,
                            'country_id' => $request->countries[$j],
                            'product_continent_id' => $insertedContinents[$country->continent_id]
    
                        ]);
    
                        GeoRegion::where('country_id', $request->countries[$j])->get()->each(function($region) use ($product, $productCountry){
                            $product->product_regions()->create([
                                'product_id' => $product->id,
                                'region_id' => $region->id,
                                'product_country_id' => $productCountry->id
                            ]);
                        });
                    }
                    
                }
                if (isset($request->regions)) {
                    // Delete old data
                    $product->product_continents()->delete();
                    $product->product_countries()->delete();
                    $product->product_regions()->delete();
                
                    // Arrays to track inserted continents and countries
                    $insertedContinents = [];
                    $insertedCountries = [];
                
                    foreach ($request->regions as $regionId) {
                        $region = GeoRegion::find($regionId);
                
                        if (!$region) {
                            continue; // Skip if region is not found
                        }
                
                        $country = GeoCountry::find($region->country_id);
                
                        if (!$country) {
                            continue; // Skip if country is not found
                        }
                
                        // Ensure continent is inserted only once
                        if (!isset($insertedContinents[$country->continent_id])) {
                            $productContinent = $product->product_continents()->create([
                                'product_id' => $product->id,
                                'continent_id' => $country->continent_id
                            ]);
                            $insertedContinents[$country->continent_id] = $productContinent->id;
                        }
                
                        // Ensure country is inserted only once
                        if (!isset($insertedCountries[$region->country_id])) {
                            $productCountry = $product->product_countries()->create([
                                'product_id' => $product->id,
                                'country_id' => $region->country_id,
                                'product_continent_id' => $insertedContinents[$country->continent_id] // Link to continent
                            ]);
                            $insertedCountries[$region->country_id] = $productCountry->id;
                        }
                
                        // Insert region
                        $product->product_regions()->create([
                            'product_id' => $product->id,
                            'region_id' => $regionId,
                            'product_country_id' => $insertedCountries[$region->country_id] // Link to country
                        ]);
                    }
                }
            }
            DB::commit(); // ✅ Commit the transaction if everything is successful
        } catch (\Exception $e) {
            DB::rollBack(); // ❌ Rollback on error
            return response()->json([
                'success' => false,
                'message' => $e->getMessage() . $e->getLine()
            ], 500);
        }

        // Store all image after all other data has been saved

        // Store both original and cropped images
        if ($request->hasFile('org_product_main_image')) {
            // Remove old original image if a new one is uploaded
            if ($product->hasMedia('org_product_main_image')) {
                $product->clearMediaCollection('org_product_main_image');
            }
            
            // Store original image
            $product->addMediaFromRequest('org_product_main_image')
                    ->toMediaCollection('org_product_main_image');
        }
        
        if ($request->hasFile('crp_product_main_image')) {
            // Remove old cropped image if a new one is uploaded
            if ($product->hasMedia('crp_product_main_image')) {
                $product->clearMediaCollection('crp_product_main_image');
            }
            
            // Store cropped image
            $product->addMediaFromRequest('crp_product_main_image')
                    ->toMediaCollection('crp_product_main_image');
        }

        // Store main image
        if ($request->hasFile('main_image')) {
            $product->addMedia($request->file('main_image'))
                ->toMediaCollection('main_image');

            $selectedMainIds = $request->selected_pre_main_image ?? [];
            $product->getMedia('main_image')->each(function ($media) use ($selectedMainIds) {
                if (!in_array($media->id, $selectedMainIds)) {
                    $media->delete(); // Delete media if not in selected_pre_thumb_image
                }
            });
        }

        // Remove media of thumb_images that are not in selected_pre_thumb_image
        $selectedThumbIds = $request->selected_pre_thumb_image ?? [];
        $product->getMedia('thumb_images')->each(function ($media) use ($selectedThumbIds) {
            if (!in_array($media->id, $selectedThumbIds)) {
                $media->delete(); // Delete media if not in selected_pre_thumb_image
            }
        });

        // Store thumbnail images
        if ($request->hasFile('thumb_images')) {
            foreach ($request->file('thumb_images') as $thumb) {
                $product->addMedia($thumb)
                    ->toMediaCollection('thumb_images');
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Product Updated successfully.",
        ], 201);
    }

    public function destroy($id)
    {
        Product::findOrFail($id)->delete();
        return response()->json(['success' => 'Deleted successfully']);
    }

    public function deleteSelected(Request $request)
    {
        if (is_array($request->product_ids)) {
            $products = Product::whereIn('uuid', $request->product_ids)->get();
            foreach ($products as $product) {
                $product->delete();
                // $product->searchable();
            }
        } else {
            $product = Product::where('uuid', $request->product_ids)->first();
            $product->delete();
            // $product->searchable();
        }
        return response()->json(['success' => true]);
    }

    public function pauseSelected(Request $request)
    {
        $products = Product::whereIn('uuid', $request->product_ids)->get();
        foreach ($products as $product) {
            $product->update([
                'listing_status' => "Inactive",
                '‘listing_end_date' => Carbon::now()->format('Y-m-d')
            ]);
            $product->searchable();
        }
        return response()->json(['success' => true]);
    }

    public function copy($uuid)
    {
        DB::beginTransaction();
        try {
            $product = Product::where('uuid', $uuid)->first();
            $newProduct = $product->replicate();
            $newProduct->name = $product->name . ' - Copy';
            $newProduct->uuid = DB::select('SELECT UUID_SHORT() AS uuid')[0]->uuid;
            $newProduct->listing_status = 'Inactive'; // Set the new product's status to inactive
            $newProduct->save();
    
            //copy all the relationships
            $newProduct->categories()->sync($product->categories->pluck('id'));
            $newProduct->keyFeatures()->sync($product->keyFeatures->pluck('id'));
            $newProduct->afterSaleSupports()->sync($product->afterSaleSupports->pluck('id'));
            // $newProduct->regions()->sync($product->regions->pluck('id'));
    
            if ($product->product_continents()->exists()) {
                foreach ($product->product_continents as $product_continent) {
                    // Replicate the product continent
                    $newProductContinent = $product_continent->replicate();
                    $newProductContinent->product_id = $newProduct->id;
                    $newProductContinent->save();
                }
            }
            if ($product->product_countries()->exists()) {
                foreach ($product->product_countries as $product_country) {
                    // Replicate the product country
                    $newProductCountry = $product_country->replicate();
                    $newProductCountry->product_id = $newProduct->id;
                    $newProductCountry->save();
                }
            }
            if ($product->product_regions()->exists()) {
                foreach ($product->product_regions as $product_region) {
                    // Replicate the product region
                    $newProductRegion = $product_region->replicate();
                    $newProductRegion->product_id = $newProduct->id;
                    $newProductRegion->save();
                }
            }
            if ($product->pricings()->exists()) {
                foreach ($product->pricings as $pricing) {
                    $newPricing = $pricing->replicate();
                    $newPricing->uuid = (string) Str::uuid();
                    $newPricing->product_id = $newProduct->id;
                    $newPricing->save();
                }
            }
    
            if ($product->shippings()->exists()) {
                foreach ($product->shippings as $shipping) {
                    $newShipping = $shipping->replicate();
                    $newShipping->uuid = (string) Str::uuid();
                    $newShipping->product_id = $newProduct->id;
                    $newShipping->save();
                }
            }
            if ($product->attributes()->exists()) {
                foreach ($product->attributes as $attribute) {
                    $newAttribute = $attribute->replicate();
                    $newAttribute->uuid = (string) Str::uuid();
                    $newAttribute->product_id = $newProduct->id;
                    $newAttribute->save();
                }
            }
            if ($product->certifications()->exists()) {
                foreach ($product->certifications as $certification) {
                    $newCertification = $certification->replicate();
                    $newCertification->uuid = (string) Str::uuid();
                    $newCertification->product_id = $newProduct->id;
                    $newCertification->save();
                }
            }
            if ($product->standardsCompliances()->exists()) {
                foreach ($product->standardsCompliances as $compliance) {
                    $newCompliance = $compliance->replicate();
                    $newCompliance->uuid = (string) Str::uuid();
                    $newCompliance->product_id = $newProduct->id;
                    $newCompliance->save();
                }
            }
            DB::commit(); // ✅ Commit the transaction if everything is successful
            return Redirect::route('frontend.portfolio.portfolio-product.index')->with('success', 'Product copied successfully.');
        } catch (\Exception $e) {
            DB::rollBack(); // ❌ Rollback on error
            return Redirect::route('frontend.portfolio.portfolio-product.index')->with('error', $e->getMessage());
        }
    }
}
