<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\AfterSaleSupport;

class AfterSaleSupportSeeder extends Seeder
{
    public function run()
    {
        $supports = [
            ['support_type' => 'Product Warranty', 'description' => 'Providing repair or replacement options under warranty or guarantee, ensuring quality and performance.'],
            ['support_type' => 'Documentation and Records Management', 'description' => 'Managing and providing all product-related documents and records, ensuring that all necessary materials are accessible for product use, regulatory compliance, and ongoing support.'],
            ['support_type' => 'Installation / Setup Support', 'description' => 'Offering professional installation and configuration services to ensure products are properly set up'],
            ['support_type' => 'Product Training', 'description' => 'Delivering training and educational materials to help customers use and maintain products effectively'],
            ['support_type' => 'Technical Support', 'description' => 'Offering ongoing assistance for technical issues via multiple channels such as phone, email, or live chat'],
            ['support_type' => 'Maintenance and Repair', 'description' => 'Providing regular maintenance and repair services to extend product lifespan and keep it functional'],
            ['support_type' => 'Spare Parts Supply', 'description' => 'Supplying genuine spare parts to facilitate repairs and replacements'],
            ['support_type' => 'Software Updates and Upgrades', 'description' => 'Providing software enhancements and security updates to keep products up to date'],
            ['support_type' => 'Consulting and Customization', 'description' => 'Offering expert advice and tailoring products to fit specific customer needs'],
            ['support_type' => 'Digital Services and Management', 'description' => 'Providing access to custom software, managing product registration data, and offering database or digital service management for the product. This includes user account management, software access, or remote product monitoring'],
            ['support_type' => 'Delivery and Logistics', 'description' => 'Ensuring timely product delivery with tracking, and managing coordination to ensure smooth logistics'],
            ['support_type' => 'Customer Feedback and Surveys', 'description' => 'Collecting customer feedback through surveys to continuously improve product offerings and services'],
            ['support_type' => 'After-Sales Support Contracts', 'description' => 'Offering ongoing service contracts that provide priority support and long-term maintenance options'],
            ['support_type' => 'Return and Exchange', 'description' => 'Managing hassle-free returns or exchanges to ensure satisfaction with the product'],
            ['support_type' => 'Customer Loyalty Program', 'description' => 'Offering discounts, rewards, or incentives to repeat customers to encourage continued business'],
            ['support_type' => 'Legal Compliance Support', 'description' => 'Assisting customers in meeting industry regulations, standards, and product certifications'],
            ['support_type' => 'Sustainable Product Disposal', 'description' => 'Offering environmentally friendly recycling and disposal services for products at the end of their lifecycle'],
        ];

        foreach ($supports as $support) {
            AfterSaleSupport::create([
                'uuid' => Str::uuid(),
                'support_type' => $support['support_type'],
                'description' => $support['description'],
            ]);
        }
    }
}
