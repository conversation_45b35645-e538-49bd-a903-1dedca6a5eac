<?php

namespace Database\Seeders;

use App\Models\CompanyChatContactTitle;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanyChatContactTitleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $titles = ['Chat', 'Customer Support chat', 'Technical Support chat', 'Product Enquiries chat'];
        foreach($titles as $title){
             CompanyChatContactTitle::create([
                 'title' => $title
             ]);
        }
    }
}
