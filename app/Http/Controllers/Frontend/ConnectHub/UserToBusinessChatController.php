<?php

// app/Http/Controllers/Frontend/ConnectHub/UserToBusinessChatController.php

namespace App\Http\Controllers\Frontend\ConnectHub;

use App\Events\UserToBussinessChatEvent;
use App\Http\Controllers\Controller;
use App\Models\BusinessConversation;
use App\Models\BusinessMessage;
use Illuminate\Http\Request;

class UserToBusinessChatController extends Controller
{
    public function sendUserToBusinessMessage(Request $request)
    {
        $request->validate([
            'message' => 'required|string',
        ]);

        $conversation = BusinessConversation::where('user_id', auth()->id())
            ->whereIn('status', ['open', 'in_progress'])
            ->first();

        if (!$conversation) {
            $conversation = BusinessConversation::create([
                'user_id' => auth()->id(),
                'status' => 'open',
            ]);
        }

        $message = BusinessMessage::create([
            'conversation_id' => $conversation->id,
            'sender_id' => auth()->id(),
            'message' => $request->message,
        ]);

        broadcast(new UserToBussinessChatEvent($message))->toOthers();

        return response()->json(['message' => $message->load('sender')]);
    }

    public function initiateBusinessChat(Request $request)
    {
        $conversation = BusinessConversation::where('user_id', auth()->id())
            ->whereIn('status', ['open', 'in_progress'])
            ->first();

        if (!$conversation) {
            $conversation = BusinessConversation::create([
                'user_id' => auth()->id(),
                'status' => 'open',
            ]);
        }

        $messages = $conversation->messages()->with('sender')->get();

        $formattedMessages = $messages->map(function ($msg) {
            return [
                'id' => $msg->id,
                'conversation_id' => $msg->conversation_id,
                'sender_id' => $msg->sender_id,
                'message' => $msg->message,
                'time' => $msg->created_at->format('H:i-d M'),
                'sender' => [
                    'id' => $msg->sender->id ?? null,
                    'name' => $msg->sender->name ?? null,
                    'email' => $msg->sender->email ?? null,
                ],
            ];
        });

        return response()->json([
            'messages' => $formattedMessages,
            'data' => $conversation->id,
        ]);
    }
}

