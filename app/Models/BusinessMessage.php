<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BusinessMessage extends Model
{
    protected $fillable = [
        'conversation_id',
        'sender_id',
        'message',
    ];

    public function conversation()
    {
        return $this->belongsTo(BusinessConversation::class, 'conversation_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }
}

