<?php

namespace App\Models\Traits;

use App\Notifications\PhoneVerificationNotification;
use App\Services\OtpService;

trait HasVerifiedPhone
{
    /**
     * Determine if the user has verified their phone number.
     *
     * @return bool
     */
    public function isPhoneVerified(): bool
    {
        return $this->phone_verified_at !== null;
    }

    /**
     * Send the phone verification SMS to the user.
     *
     * @return void
     */
    public function sendPhoneVerification(): void
    {
        $otp = app(OtpService::class)->generate($this, 'phone');
        // $this->notify(new PhoneVerificationNotification($otp->code));
    }

    /**
     * Mark the user's phone number as verified.
     *
     * @return void
     */
    public function markPhoneAsVerified(): void
    {
        $this->forceFill([
            'phone_verified_at' => now(),
        ])->save();
    }

    /**
     * Get the phone number for the Twilio notification driver.
     *
     * @return string
     */
    public function routeNotificationForTwilio(): string
    {
        return $this->phone;
    }
}
