<?php

namespace App\Http\Controllers\Admin\Supplier;

use App\Models\Company;
use App\Models\User;
use App\Services\CompanyService;
use Illuminate\Support\Facades\Gate;

class CompanyController extends BaseCompanyController
{
    /**
     * Create a new controller instance.
     *  
     * @param CompanyService $service
     * @return void
     */
    public function __construct(CompanyService $service)
    {
        $role = User::ROLE_SUPPLIER;
        $routePrefix = 'admin.suppliers.';
        $title = 'Company';
        
        parent::__construct($service, $role, $routePrefix, $title); 
    }


    public function show(Company $supplier)
    {
        if (!Gate::any(['manage suppliers', 'view suppliers'])) {
            abort(403);
        }

        return $this->parseShow($supplier);
    }
}
