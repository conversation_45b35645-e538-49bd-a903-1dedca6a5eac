<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->string('name'); // Product name
            $table->text('description')->nullable(); // Product description (nullable)
            $table->text('region_restrictions')->nullable(); // Regions supplying to, restrictions, or special arrangements
            $table->unsignedBigInteger('company_id'); // Foreign key to companies table
            $table->text('additional_pricing_detail')->nullable();
            $table->text('additional_shiping_rate_detail')->nullable();
            $table->text('compliance_summary')->nullable();
            $table->enum('listing_duration', ['Publish', 'Schedule'])->nullable()->comment("Publish: Release the listing immediately when click ‘Publish’. No end date. Schedule: Choose a specific release and end date within the next 12 months.");
            $table->enum('listing_status', ['Active', 'Inactive'])->nullable()->comment("Change Listing Status: Active or Inactive.");
            $table->boolean('remove_listing')->default(false)->comment("Remove this listing: Boolean value. Default is false.");
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade'); // Foreign key constraint
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
