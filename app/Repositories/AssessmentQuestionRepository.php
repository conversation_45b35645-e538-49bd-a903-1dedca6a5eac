<?php

namespace App\Repositories;

use App\Models\AssessmentQuestion;

/**
 * Class AssessmentQuestionRepository
 *
 * Handles data operations for the AssessmentQuestion model.
 */
class AssessmentQuestionRepository extends BaseRepository
{
    /**
     * The AssessmentQuestion model instance.
     *
     * @var \App\Models\AssessmentQuestion
     */
    protected $model;

    /**
     * AssessmentQuestionRepository constructor.
     *
     * @param \App\Models\AssessmentQuestion $assessmentQuestion
     * @return void
     */
    public function __construct(AssessmentQuestion $assessmentQuestion)
    {
        $this->model = $assessmentQuestion;
    }
}
