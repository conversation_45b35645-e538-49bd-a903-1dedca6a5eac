<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stripe_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('stripe_product_id')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('features_list')->nullable();
            $table->decimal('gbp_price_monthly', 8, 2)->nullable();
            $table->decimal('gbp_price_annual', 8, 2)->nullable();
            $table->decimal('euro_price_monthly', 8, 2)->nullable();
            $table->decimal('euro_price_annual', 8, 2)->nullable();
            $table->decimal('usd_price_monthly', 8, 2)->nullable();
            $table->decimal('usd_price_annual', 8, 2)->nullable();
            $table->integer('subscription_rank')->default(1)->comment('Free = 1, Essential = 2, Pro = 3');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_assigned')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stripe_subscriptions');
    }
};
