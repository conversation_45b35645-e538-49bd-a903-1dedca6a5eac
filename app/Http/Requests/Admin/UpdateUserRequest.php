<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('user')->id;
        
        return [
            'username' => ['required', 'string', 'max:25','unique:users,username,' . $id],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'regex:/^\d{3}-\d{3}-\d{4}$/', 'max:12'],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * Provides user-friendly error messages for common validation errors,
     *
     * @return array<string, string> Array of validation error messages.
     */
    public function messages(): array
    {
        return [
            'phone.regex' => 'The phone number must be in the format xxx-xxx-xxxx.',
        ];
    }
}
