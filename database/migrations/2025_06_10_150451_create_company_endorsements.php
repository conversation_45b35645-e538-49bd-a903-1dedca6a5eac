<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_endorsements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('endorser_company')
                ->constrained('companies')
                ->cascadeOnDelete();
            $table->foreignId('endorsed_company')
                ->constrained('companies')
                ->cascadeOnDelete();
            $table->foreignId('endorser_user')
                ->constrained('users')
                ->cascadeOnDelete();
            $table->foreignId('remover_user')
                ->nullable()
                ->constrained('users')
                ->cascadeOnDelete();
            $table->enum('status', ['active', 'removed'])
                ->default('active');
            $table->string('ip_address')
                ->nullable();
            $table->timestamp('created_at');
            $table->timestamp('removed_at')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_endorsements');
    }
};
