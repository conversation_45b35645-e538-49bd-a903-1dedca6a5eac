<?php

namespace App\Repositories;

use App\Models\CompanySubscription;
use App\Models\PlanFeature;
use App\Models\StripeSubscription;
use App\Models\SubscriptionActive;
use App\Models\SubscriptionDowngrade;
use App\Models\SubscriptionLog;
use App\Models\User;
use App\Services\MailService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Stripe\Price;
use Stripe\Stripe;
use Stripe\Subscription;


class StripeRepository_old
{
    /**
     * Handle subscription created event.
     *
     * @param  \Stripe\Subscription  $subscription
     * @return void
     */
    public function handleSubscriptionCreated($subscription)
    {
        \Log::debug("Customer Subscription created", ['subscription' => $subscription]);
        $user = User::where('stripe_customer_id', $subscription->customer)->first();
        $company = \App\Models\Company::where('user_id', $user->id)->orderBy('id', 'desc')->first();
        $companyId = $company->id;
        \Log::debug("companyID", ['companyID' => $companyId]);

        if ($companyId) {
            SubscriptionActive::create([
                'company_id' => $companyId,
                'stripe_subscription_id' => $subscription->id,
                'stripe_customer_id' => $subscription->customer,
                'user_id' => $user->id,
                'billing_cycle' => $subscription->plan->interval ?? null,
                'currency' => $subscription->plan->currency ?? null,
                'cost' => $subscription->plan->amount / 100,
                'last_payment_status' => $subscription->status === 'active' ? 'paid' : 'failed',
                'last_payment_date' => Carbon::createFromTimestamp($subscription->current_period_start),
                'ended_at' => Carbon::createFromTimestamp($subscription->current_period_end),
            ]);
        }
    }

    /**
     * Handle subscription updated event.
     *
     * @param  \Stripe\Subscription  $subscription
     * @return void
     */
    public function handleSubscriptionUpdated($subscription): void
    {
        Log::debug("Subscription updated", ['subscription' => $subscription]);

        $companySubscription = SubscriptionActive::where('stripe_customer_id', $subscription->customer)
            //->where('stripe_subscription_id', $subscription->id)
            ->where('last_payment_status', 'paid')
            ->orderBy('id', 'desc')
            ->first();


        $checkPlanSwitch = $this->checkPlanSwitch($companySubscription, $subscription);

        StripeSubscription::where('');

        if ($companySubscription) {
            $companySubscription->update([
                'billing_cycle' => $subscription->plan->interval ?? null,
                'currency' => $subscription->plan->currency ?? null,
                'cost' => $subscription->plan->amount / 100,
                'last_payment_status' => $subscription->status === 'active' ? 'paid' : 'failed',
                'last_payment_date' => Carbon::createFromTimestamp($subscription->current_period_start),
                'ended_at' => Carbon::createFromTimestamp($subscription->current_period_end),
            ]);
        } else {
            $userId = User::where('stripe_customer_id', $subscription->customer)->first()->id;
            $companyId = \App\Models\Company::where('user_id', $userId)->orderBy('id', 'desc')->first()->id;
            SubscriptionActive::create([
                'company_id' => $companyId ?? null,
                'stripe_subscription_id' => $subscription->id,
                'stripe_customer_id' => $subscription->customer,
                'user_id' => $userId,
                'billing_cycle' => $subscription->plan->interval ?? null,
                'currency' => $subscription->plan->currency ?? null,
                'cost' => $subscription->plan->amount / 100,
                'last_payment_status' => $subscription->status === 'active' ? 'paid' : 'failed',
                'last_payment_date' => Carbon::createFromTimestamp($subscription->current_period_start),
                'ended_at' => Carbon::createFromTimestamp($subscription->current_period_end),
            ]);
        }
    }



    public function handleSubscriptionDowngrade($newsubscription)
    {
        $customer = $newsubscription->customer;
        $newSubscriptionId = $newsubscription->id;
        $currentSubscription = SubscriptionActive::where('stripe_customer_id', $customer)
            ->where('last_payment_status', 'paid')
            ->orderBy('id', 'desc')
            ->first();

        if ($currentSubscription) {
            try {
                Stripe::setApiKey(env('STRIPE_SECRET'));
                $stripeSubscription = Subscription::retrieve($currentSubscription->stripe_subscription_id);
                $currentPeriodEnd = Carbon::createFromTimestamp($stripeSubscription->current_period_end);
                SubscriptionDowngrade::create([
                    'company_id' => $currentSubscription->company_id,
                    'current_subscription_id' => $currentSubscription->stripe_subscription_id,
                    'new_subscription_id' => $newSubscriptionId,
                    'stripe_customer_id' => $customer,
                    'downgrade_date' => $currentPeriodEnd,
                    'downgrade_status' => 'pending'
                ]);

                $this->logSubscriptionChanges($currentSubscription, $newSubscriptionId, 'downgrade');

            } catch (\Exception $e) {
                Log::error("Error retrieving subscription from Stripe: " . $e->getMessage());
            }
        } else {

            Log::warning("Subscription downgrade requested to: {$newSubscriptionId}");
        }
    }

    protected function logSubscriptionChanges($currentSubscription, $newSubscriptionId, $changeType)
    {
        SubscriptionLog::create([
            'company_id' => $currentSubscription->company_id,
            'previous_subscription_id' => $currentSubscription->stripe_subscription_id,
            'new_subscription_id' => $newSubscriptionId,
            'stripe_customer_id' => $currentSubscription->stripe_customer_id,
            'user_id' => $currentSubscription->user_id,
            'change_type' => $changeType,
            'change_initiator' => 'system',
            'change_date' => now(),
        ]);
    }

      
    public function handlePaymentSucceeded($invoice){
        \Log::debug("Payment succeeded {$invoice}");
    }


    /**
     * Handle payment failed event.
     *
     * @param  \Stripe\Invoice  $invoice
     * @return void
     */
    public function handlePaymentFailed($invoice)
    {
        \Log::debug("Payment failed {$invoice}");
        $subscriptionId = $invoice->subscription;
        $customerId = $invoice->customer;

        Stripe::setApiKey(env('STRIPE_SECRET'));
        $subscription = Subscription::retrieve($subscriptionId);
        $company = \App\Models\Company::where('user_id', User::where('stripe_customer_id', $customerId)->first()->id)->orderBy('id', 'desc')->first();
        $companyId = $company->id;

        if ($subscription->status === 'incomplete') {
            // New subscription payment failed
            $subscription->cancel();
            $this->notifyUserPaymentFailed($customerId, 'initial_payment_failed');

        } elseif ($subscription->status === 'past_due') {
            // Renewal subscription payment failed
            // Schedule downgrade with 14-day grace period
            SubscriptionDowngrade::create([
                'company_id' => $companyId,
                'current_subscription_id' => $subscriptionId,
                'new_subscription_id' => $subscriptionId, // Assuming downgrade to free tier
                'downgrade_date' => now()->addDays(14),
                'downgrade_status' => 'pending'
            ]);

            // Notify user about the grace period & failed renewal
            $this->notifyUserPaymentFailed($customerId, 'renewal_payment_failed');

        }
    }

    public function notifyUserPaymentFailed($customerId, $type)
    {
        try {
            // Retrieve customer details from Stripe
            Stripe::setApiKey(env('STRIPE_SECRET'));
            $stripeCustomer = \Stripe\Customer::retrieve($customerId);
            $user = User::where('email', $stripeCustomer->email)->first();

            if ($user) {
                // Send notification to user
                $user->notify(new \App\Notifications\PaymentFailed($type));
            } else {
                Log::warning("User not found for Stripe customer ID: {$customerId} ({$stripeCustomer->email}).");
            }
        } catch (\Exception $e) {
            Log::error("Error sending payment failure notification: " . $e->getMessage());
        }
    }


    public function handleProductCreated($product)
    {
        $prices = $this->extractPrices($product);

        $stripeProduct = StripeSubscription::create([
            'stripe_product_id' => $product->id,
            'name' => $product->name,
            'description' => $product->description ?? null,
            'features_list' => json_encode($product->marketing_features ?? []),
            'gbp_price_monthly' => $prices['gbp_price_monthly'],
            'gbp_price_annual' => $prices['gbp_price_annual'],
            'euro_price_monthly' => $prices['euro_price_monthly'],
            'euro_price_annual' => $prices['euro_price_annual'],
            'usd_price_monthly' => $prices['usd_price_monthly'],
            'usd_price_annual' => $prices['usd_price_annual'],
            'subscription_rank' => $product->metadata->rank ?? 1,
        ]);

        if ($stripeProduct && !empty($product->marketing_features)) {

            foreach ($product->marketing_features as $feature) {
                PlanFeature::create([
                    'stripe_subscription_id' => $stripeProduct->id,
                    'name' => $feature['name'],
                    'description' => $feature['name']
                ]);
            }
        }
    }


    public function handleProductUpdated($product)
    {
        $subscription = StripeSubscription::where('stripe_product_id', $product->id)->first();

        $prices = $this->extractPrices($product);

        if ($subscription) {
            $subscription->update([
                'name' => $product->name,
                'description' => $product->description ?? null,
                'features_list' => json_encode($product->marketing_features ?? []),
                'gbp_price_monthly' => $prices['gbp_price_monthly'],
                'gbp_price_annual' => $prices['gbp_price_annual'],
                'euro_price_monthly' => $prices['euro_price_monthly'],
                'euro_price_annual' => $prices['euro_price_annual'],
                'usd_price_monthly' => $prices['usd_price_monthly'],
                'usd_price_annual' => $prices['usd_price_annual'],
                'subscription_rank' => $product->metadata->rank ?? 1,
            ]);

            if ($product && !empty($product->marketing_features)) {
                foreach ($product->marketing_features as $feature) {
                    PlanFeature::updateOrCreate(
                        [
                            'stripe_subscription_id' => $subscription->id,
                            'name' => $feature['name'],
                        ],
                        [
                            'description' => $feature['name']
                        ]
                    );
                }

                PlanFeature::where('stripe_subscription_id', $subscription->id)
                    ->whereNotIn('name', array_column($product->marketing_features, 'name'))
                    ->delete();
            }


        } else {
            Log::warning("Product update received, but no matching record found: {$product->id}");
        }
    }

    public function handleProductDeleted($product)
    {
        $subscription = StripeSubscription::where('stripe_product_id', $product->id)->first();

        if ($subscription) {
            $subscription->update(['is_active' => false]);
            Log::info("Product marked as inactive: {$product->id}");
        } else {
            Log::warning("Product delete event received, but no matching record found: {$product->id}");
        }
    }

    public function handlePriceUpdated($price)
    {
        $subscription = StripeSubscription::where('stripe_product_id', $price->product)->first();

        if ($subscription) {
            $currency = strtolower($price->currency);
            $interval = $price->recurring->interval ?? null;

            if ($interval === 'month') {
                $column = "{$currency}_price_monthly";
            } elseif ($interval === 'year') {
                $column = "{$currency}_price_annual";
            } else {
                Log::warning("Unexpected pricing interval: {$interval}");
                return;
            }

            $subscription->update([$column => $price->unit_amount / 100]);

            Log::info("Price updated for {$column}: {$price->unit_amount}");
        } else {
            Log::warning("Price update received, but no matching product found: {$price->product}");
        }
    }

    /**
     * Extracts and formats price data from the product object.
     */
    private function extractPrices($product)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $prices = Price::all(['product' => $product->id, 'expand' => ['data.currency_options']]);
        $priceMap = [
            'GBP' => ['month_gbp' => 'gbp_price_monthly', 'year_gbp' => 'gbp_price_annual'],
            'EUR' => ['month_eur' => 'euro_price_monthly', 'year_eur' => 'euro_price_annual'],
            'USD' => ['month_usd' => 'usd_price_monthly', 'year_usd' => 'usd_price_annual'],
        ];

        $formattedPrices = array_fill_keys(array_merge(...array_values($priceMap)), null);


        if (!empty($prices['data'][0]['currency_options'])) {
            $currencyOptions = json_decode(json_encode($prices['data'][0]['currency_options']), true);

            foreach ($currencyOptions as $currency => $price) {
                $currency = strtoupper($currency);
                $interval = strtolower($prices['data'][0]['recurring']['interval'] ?? '');

                $amount = isset($price['unit_amount_decimal']) ? $price['unit_amount_decimal'] / 100 : null;

                if (isset($priceMap[$currency][$interval . '_' . strtolower($currency)])) {
                    $formattedPrices[$priceMap[$currency][$interval . '_' . strtolower($currency)]] = $amount;
                }
            }
        } else {
            Log::warning("No prices found for product.");
        }

        return $formattedPrices;
    }


    public function checkPlanSwitch($companySubscription, $subscription)
    {

        Stripe::setApiKey(config('services.stripe.secret'));
        $existingSubscripionId = $companySubscription->stripe_subscription_id ?? null;
        $existingSubscripion = Subscription::retrieve($existingSubscripionId);
        
        $newSubsProductId = $subscription->items->data[0]->price->product ?? null;
        $existingProductId = $existingSubscripion->items->data[0]->price->product ?? null;

        $rank1 = StripeSubscription::where('stripe_product_id',$existingProductId)->first();
        $rank2 = StripeSubscription::where('stripe_product_id',$newSubsProductId)->first();

        if ($rank1->subscription_rank > $rank2->subscription_rank) {
            $this->handleSubscriptionDowngrade($subscription);

        }
    }


}
