<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropForeign('subscriptions_plan_id_foreign');
            $table->dropForeign('subscriptions_user_id_foreign');
        });

        // Rename the table
        Schema::rename('subscriptions', 'company_subscriptions');

        Schema::table('company_subscriptions', function (Blueprint $table) {
            $table->dropColumn(['plan_id', 'user_id']); // Remove old columns
            $table->foreignId('company_id')
            ->after('id')
            ->constrained('companies')
            ->cascadeOnDelete();
            $table->string('stripe_plan_id')->nullable()->after('company_id');
            $table->string('subscription_type')->nullable()->after('stripe_plan_id');
            $table->string('stripe_customer_id')->nullable()->after('subscription_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
         // Revert the renamed table structure
        Schema::table('company_subscriptions', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
            $table->dropColumn(['company_id', 'stripe_plan_id', 'subscription_type', 'stripe_customer_id']);
            $table->foreignId('plan_id')->constrained('plans')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
        });

        // Rename the table back
        Schema::rename('company_subscriptions', 'subscriptions');
    }
};
