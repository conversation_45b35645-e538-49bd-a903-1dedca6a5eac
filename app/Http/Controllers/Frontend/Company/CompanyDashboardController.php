<?php

namespace App\Http\Controllers\Frontend\Company;

use Exception;
use App\Models\CaseStudy;
use App\Models\GeoRegion;
use Illuminate\View\View;
use App\Models\GeoCountry;
use App\Models\PlanFeature;
use App\Models\CompanyAdmin;
use App\Models\CompanyAward;
use App\Models\GeoContinent;
use App\Models\LocationType;
use Illuminate\Http\Request;
use App\Models\QueryCategory;
use App\Models\CompanyLocation;
use App\Models\IsoStandardCode;
use App\Models\OrganisationSize;
use App\Models\OrganisationType;
use App\Models\PhoneContactTitle;
use App\Models\StripeSubscription;
use App\Http\Controllers\Controller;
use App\Models\CompanyIsoCompliance;
use App\Models\OrganisationTypeLocal;
use App\Models\CompanyBusinessCategory;
use App\Models\CompanyChatContactTitle;
use Illuminate\Support\Facades\Session;
use App\Http\Actions\ContactQueryAction;
use Illuminate\Support\Facades\Redirect;
use App\Models\CompanyProductCertification;
use App\Models\CompanyPersonnelQualification;
use App\Http\Requests\Frontend\CreateQueryRequest;
use App\Http\Actions\CompanyDashboardSettingsAction;
use App\Http\Requests\Frontend\CompanyCaseStudyRequest;
use App\Http\Requests\Frontend\CompanyEmailUpdateRequest;
use App\Http\Requests\Frontend\LocationCreateUpdateRequest;
use App\Http\Requests\Frontend\CompanyDashboardSettingsRequest;
use App\Models\User;

class CompanyDashboardController extends Controller
{

    public function companyDashboard(): View
    { 
        
        if(Session::get('currentAccount') == 'user'){
            Redirect::route('frontend.user-profile-setting');
        }
        $company = auth()->user()->company;
        $organisationTypeLocals = OrganisationTypeLocal::where('iso_code', $company->registered_location)->get();
        $organisationTypes = OrganisationType::all();
        $organisationSizes = OrganisationSize::all();
        $phoneContactTitles = PhoneContactTitle::all();
        $companyChatContactTitles = CompanyChatContactTitle::all();
        $locationTypes = LocationType::all();
        $isoStandardCodes = IsoStandardCode::all();
        $companyLocations = $company->company_locations()->orderBy('is_main', 'desc')->get();
        $companyAdmins = CompanyAdmin::with(['user'])
                                    ->where('company_id', $company->id)
                                    ->whereHas('user', function ($query) {
                                        $query->where('status', User::STATUS_ACTIVE);
                                    })
                                    ->get()
                                    ->filter(function ($admin) {
                                        $permissions = json_decode($admin->permissions, true);
                                        return is_array($permissions) && in_array('inbox access', $permissions);
                                    });
        $publishDisabled = true;
        if($company->year_founded && $company->description && $company->getFirstMedia('company_logo') && $company->phone){
            $publishDisabled = false;
        } 
        $totalSegments = 6;
        $filledSegments = 0;
        // Example: Check different fields and count filled segments
        if ($company->year_founded && $company->description) $filledSegments++;
        if ($company->year_founded && $company->description && $company->getFirstMedia('company_logo')) $filledSegments++;
        if ($company->year_founded && $company->description && $company->getFirstMedia('company_logo') && $company->phone) $filledSegments++;
        if ($company->year_founded && $company->description && $company->getFirstMedia('company_logo') && $company->phone && $companyLocations) $filledSegments++;
        if ($company->year_founded && $company->description && $company->getFirstMedia('company_logo') && $company->phone && $companyLocations
         && $company->company_iso_compliance->isNotEmpty() && $company->company_personnel_qualifications->isNotEmpty()
         && $company->company_product_certifications->isNotEmpty()) $filledSegments++;
        if ($company->year_founded && $company->description && $company->getFirstMedia('company_logo') && $company->phone && $companyLocations
         && $company->company_iso_compliance->isNotEmpty() && $company->company_personnel_qualifications->isNotEmpty()
         && $company->company_product_certifications->isNotEmpty() && $company->company_awards->isNotEmpty()
         && $company->company_case_studies->isNotEmpty()) $filledSegments++;
        // Calculate percentage
        $completionPercentage = ($filledSegments / $totalSegments) * 100;

        return view('frontend.company.company-dashboard', [
            'organisationTypeLocals' => $organisationTypeLocals,
            'organisationTypes' => $organisationTypes,
            'organisationSizes' => $organisationSizes,
            'companyAdmins' => $companyAdmins,
            'phoneContactTitles' => $phoneContactTitles,
            'companyChatContactTitles' => $companyChatContactTitles,
            'locationTypes' => $locationTypes,
            'isoStandardCodes' => $isoStandardCodes,
            'company' => $company,
            'companyLocations' => $companyLocations,
            'publishDisabled' => $publishDisabled,
            'segmentsFilled' => $filledSegments,
            'completionPercentage' => $completionPercentage
        ]);

    }

    public function saveCompanyDashboardSettings(CompanyDashboardSettingsRequest $request, CompanyDashboardSettingsAction $action)
    {
        $data = $request->validated();
        try{
            $action($request, $data);
            return response()->json([
                'status' => 'success',
                'redirect_url' => url()->previous(),  // Redirect back if needed
                'modalToShow' => config('settings.modal.confirmation_modal')   // The modal to show on the frontend
            ]);

        }catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }
    }

    public function saveCompanyLocation(LocationCreateUpdateRequest $request, CompanyDashboardSettingsAction $action)
    {
        $data = $request->validated();
        try{
            $action->saveCompanyLocation($data);
            return redirect()->route('frontend.company.dashboard')
            ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Company location updated successfully.']);

        }catch (Exception $e) {
            return Redirect::back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }
    }

    public function saveCaseStudy(CompanyCaseStudyRequest $request, CompanyDashboardSettingsAction $action)
    {
        $data = $request->validated();
        try{
            $action->saveCompanyCaseStudy($request, $data);
            return response()->json([
                'status' => 'success',
                'redirect_url' => route('frontend.company.dashboard'),  // Redirect back if needed
                'modalToShow' => config('settings.modal.confirmation_modal')   // The modal to show on the frontend
            ]);


        }catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }
    }

    public function submitQuery(CreateQueryRequest $request, ContactQueryAction $action)
    {
        $data = $request->validated();
        try{
            $action($data);
            return Redirect::back()
            ->with(['modalToShow' => $request->nextModalToDisplay(), 'message' => 'Request sent to '.env('APP_NAME').' successfully.']);

        }catch (Exception $e) {
            return Redirect::back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }
    }
        
    public function destroy($id, $type)
    {
        try{
            switch ($type) {
                case 'location':
                    $modelToBeDeleted = CompanyLocation::find($id);
                    break;
                case 'case-study':
                    $modelToBeDeleted = CaseStudy::find($id);
                    break;
                case 'compliance':
                    $modelToBeDeleted = CompanyIsoCompliance::find($id);
                    break;
                case 'qualification':
                    $modelToBeDeleted = CompanyPersonnelQualification::find($id);
                    break;
                case 'certification':
                    $modelToBeDeleted = CompanyProductCertification::find($id);
                    break;
                case 'award':
                    $modelToBeDeleted = CompanyAward::find($id);
                    break;
                default:
                    $modelToBeDeleted = '';
                  // Code to run if no case matches
            }
            
            if ($modelToBeDeleted) {
                $modelToBeDeleted->delete();
                return response()->json([
                    'status' => 'success',
                    'redirect_url' => url()->previous(),  // Redirect back if needed
                    'modalToShow' => config('settings.modal.confirmation_modal')   // The modal to show on the frontend
                ]);
            }
        }catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }
    }

    public function deleteBusinessSector($id, $type=null){
        try{
            $businessCategory = CompanyBusinessCategory::where(['company_id' => auth()->user()->company->id, $type ? 'level_2_category_id' : 'level_3_category_id' => $id])->first();
            if ($businessCategory) {
                $businessCategory->delete();
                return response()->json([
                    'status' => 'success',
                    'redirect_url' => url()->previous(),//Redirect back if needed
                    'modalToShow' => config('settings.modal.confirmation_modal')   // The modal to show on the frontend
                ]);
            }
        }catch (Exception $e) {
            return response()->json(['catchError' => $e->getMessage()]);
        }
    }

    public function availablePlans(): View
    {
        $plans = StripeSubscription::with(['planFeatures' => function ($query) {
            $query->where('status', PlanFeature::STATUS_ACTIVE);
        }])
        ->where('is_active', StripeSubscription::STATUS_ACTIVE)
        ->whereHas('planFeatures', function ($query) {
            $query->where('status', PlanFeature::STATUS_ACTIVE);
        })
        ->orderBy('subscription_rank', 'asc')
        ->take(3)
        ->get();
        
        return view('frontend.available-plans',compact('plans'));
    }

    public function updateCompanyEmail(CompanyEmailUpdateRequest $request, CompanyDashboardSettingsAction $action){
        $data = $request->validated();
        try{
            $action->companyEmailUpdate($data);
            return Redirect::back()
            ->with(['modalToShow' => $request->nextModalToDisplay()]);

        }catch (Exception $e) {
            return Redirect::back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }

    }

     // Get continents
     public function getContinents()
     {
         $continents = GeoContinent::all();
         return response()->json($continents);
     }
 
     // Get countries by continent ID
     public function getCountries(Request $request, $continentId=null)
     {
         $countries = GeoCountry::select('id', 'country_name as name')
                                // ->when($request->has('continentIds'), function($query) use ($request){
                                //     $query->whereIn('continent_id', $request->continentIds);
                                // })
                                ->where('continent_id', $continentId)
                                ->get();
         return response()->json($countries);
     }
 
     // Get regions by country ID
     public function getRegions($countryId)
     {
         $regions = GeoRegion::select('id', 'region as name')->where('country_id', $countryId)->get();
         return response()->json($regions);
     }




}