<?php

namespace App\Livewire\ConnectHub;

use App\Models\BlockEvent;
use App\Models\EntityBlock;
use App\Models\UserConversation;
use App\Models\UserChatDeletion;
use Livewire\Component;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ChatUserList extends Component
{
    public $search = '';
    public $filter = 'all'; // or 'unread'
    public string $conversationView = 'live'; // 'live' or 'archived'

    public array $selectedConversations = [];
    public bool $confirmingArchive = false;

    public bool $showingCheckboxes = false;

    public bool $dummyToggle = false;

    public array $blockedUserIds = [];

    protected $listeners = ['chat-updated' => '$refresh'];

    public function updatedFilter()
    {
        // Optional reset logic
    }


    public function mount()
    {
        $this->loadBlockedUsers();
    }

    public function loadBlockedUsers()
    {
        $this->blockedUserIds = EntityBlock::query()
            //->where('blocker_type', 'user')
            ->where('blocker_id', auth()->id())
            //->where('blocked_type', 'user')
            ->pluck('blocked_id')
            ->toArray();
    }

    public function toggleBlock($userId)
    {
        $authUser = auth()->user();
        $authId = $authUser->id;

        $blockedUser = User::findOrFail($userId);

        // Get Spatie roles
        $authRole = $authUser->getRoleNames()->first();
        $blockedRole = $blockedUser->getRoleNames()->first();

        $existing = EntityBlock::where([
            'blocker_type' => $authRole == 'supplier' ? 'company' : 'user',
            'blocker_id' => $authId,
            'blocked_type' => $blockedRole == 'supplier' ? 'company' : 'user',
            'blocked_id' => $userId
        ])->first();

        if ($existing) {
            $existing->delete();
            $action = 'unblock';
        } else {
            EntityBlock::create([
                'blocker_type' => $authRole == 'supplier' ? 'company' : 'user',
                'blocker_id' => $authId,
                'blocked_type' => $blockedRole == 'supplier' ? 'company' : 'user',
                'blocked_id' => $userId
            ]);
            $action = 'block';
        }

        // Log to block_events
        BlockEvent::create([
            'blocker_type' => $authRole == 'supplier' ? 'company' : 'user',
            'blocker_id' => $authId,
            'blocked_type' => $blockedRole == 'supplier' ? 'company' : 'user',
            'blocked_id' => $userId,
            'action' => $action,
            'action_at' => now(),
        ]);

        $this->loadBlockedUsers(); // Refresh UI
    }


    public function render()
    {
        $search = $this->search;
        $searchableUsers = collect();

        if (!empty($search)) {
            $searchableUsers = User::query()
                ->where('id', '!=', auth()->id())
                ->whereHas('roles', fn($q) => $q->where('name', User::ROLE_USER))
                ->where(function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                })
                ->get();
        }

        $users = $this->getFilteredUsersProperty();

        return view('livewire.connect-hub.chat-user-list', compact('users', 'searchableUsers'));
    }

    public function getFilteredUsersProperty()
    {
        return $this->getUserConversationList($this->filter === 'unread');
    }

    protected function getUserConversationList(bool $unreadOnly = false)
    {
        $loggedInUserId = auth()->id();

        $archivedConversationIds = DB::table('user_chat_deletions')
            ->where('user_id', $loggedInUserId)
            ->pluck('conversation_id')
            ->toArray();

        $conversations = UserConversation::query()
            ->where(function ($query) use ($loggedInUserId) {
                $query->where('user_one_id', $loggedInUserId)
                    ->orWhere('user_two_id', $loggedInUserId);
            })
            ->when($this->conversationView === 'live', fn($q) => $q->whereNotIn('id', $archivedConversationIds))
            ->when($this->conversationView === 'archived', fn($q) => $q->whereIn('id', $archivedConversationIds))
            ->get();

        $partnerIds = [];
        $conversationMap = [];

        foreach ($conversations as $conversation) {
            if ($conversation->user_one_id === $conversation->user_two_id) {
                continue;
            }

            $partnerId = $conversation->user_one_id == $loggedInUserId
                ? $conversation->user_two_id
                : $conversation->user_one_id;

            $partnerIds[] = $partnerId;
            $conversationMap[$partnerId] = $conversation->id;
        }

        $conversationIds = array_values($conversationMap);

        $unreadCounts = DB::table('user_messages')
            ->select('conversation_id', DB::raw('COUNT(*) as unread'))
            ->whereIn('conversation_id', $conversationIds)
            ->where('sender_id', '!=', $loggedInUserId)
            ->whereNull('read_at')
            ->where('is_blocked', false)
            ->groupBy('conversation_id')
            ->get()
            ->keyBy('conversation_id');

        if ($unreadOnly) {
            $partnerIds = collect($conversationMap)->filter(fn($cid, $pid) => isset($unreadCounts[$cid]))->keys()->all();
        }

        $users = User::whereIn('id', $partnerIds)->with('company')->get();

        $lastMessages = DB::table('user_messages')
            ->select('conversation_id', DB::raw('MAX(created_at) as latest_time'))
            ->whereIn('conversation_id', $conversationIds)
            ->where('is_blocked', false)
            ->groupBy('conversation_id');

        $messages = DB::table('user_messages as m')
            ->joinSub($lastMessages, 'last_messages', function ($join) {
                $join->on('m.conversation_id', '=', 'last_messages.conversation_id')
                    ->on('m.created_at', '=', 'last_messages.latest_time');
            })
            ->select('m.conversation_id', 'm.message', 'm.created_at')
            ->get()
            ->keyBy('conversation_id');

        $users->transform(function ($user) use ($conversationMap, $messages, $unreadCounts) {
            $conversationId = $conversationMap[$user->id] ?? null;

            $user->conversation_id = $conversationId;
            $user->last_message = $conversationId && isset($messages[$conversationId])
                ? $messages[$conversationId]->message
                : null;

            $user->last_message_time = $conversationId && isset($messages[$conversationId])
                ? $messages[$conversationId]->created_at
                : null;

            $user->unread_count = $conversationId && isset($unreadCounts[$conversationId])
                ? $unreadCounts[$conversationId]->unread
                : 0;

            return $user;
        });
        $users = $users->sortByDesc('last_message_time')->values();

        return $users;
    }

    public function confirmArchive()
    {
        if (!empty($this->selectedConversations)) {
            $this->confirmingArchive = true;
        }
    }

    public function archiveIndividual($conversationId)
    {
        if (!in_array($conversationId, $this->selectedConversations)) {
            $this->selectedConversations[] = $conversationId;
            $this->showingCheckboxes = true;
        }

    }

    public function archiveSelected()
    {
        $userId = auth()->id();

        foreach ($this->selectedConversations as $conversationId) {
            UserChatDeletion::firstOrCreate([
                'user_id' => $userId,
                'conversation_id' => $conversationId,
            ]);
        }

        $this->reset(['selectedConversations', 'confirmingArchive']);
        session()->flash('message', 'Conversations archived.');
    }

    public function undoArchive($conversationId)
    {
        UserChatDeletion::where('user_id', auth()->id())
            ->where('conversation_id', $conversationId)
            ->delete();

        session()->flash('message', 'Conversation restored.');
    }

    public function cancelSelection()
    {
        $this->reset('selectedConversations');
    }

    public function markMessagesAsRead($userId)
    {
        $loggedInUserId = auth()->id();

        $conversation = UserConversation::where(function ($query) use ($loggedInUserId, $userId) {
            $query->where('user_one_id', $loggedInUserId)
                ->where('user_two_id', $userId);
        })->orWhere(function ($query) use ($loggedInUserId, $userId) {
            $query->where('user_one_id', $userId)
                ->where('user_two_id', $loggedInUserId);
        })->first();

        if ($conversation) {
            DB::table('user_messages')
                ->where('conversation_id', $conversation->id)
                ->where('sender_id', '!=', $loggedInUserId)
                ->whereNull('read_at')
                ->update(['read_at' => now()]);
        }
    }

    public function updatedSelectedConversations($value)
    {
        // Do something when checkboxes update
    }
}
