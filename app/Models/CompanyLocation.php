<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class CompanyLocation
 *
 * Represents the locations of companies.
 *
 * @package App\Models
 */
class CompanyLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'location_type_id',
        'geo_country_id',
        'geo_region_id',
        'address_line_1',
        'address_line_2',
        'city',
        'postcode',
        'latitude',
        'longitude',
        'email',
        'phone',
        'phone_prefix',
        'is_main'
    ];

    /**
     * Get the company associated with the location.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the location type associated with the company location.
     */
    public function locationType()
    {
        return $this->belongsTo(LocationType::class);
    }

    /**
     * Get the country associated with the company location.
     */
    public function geo_country(): BelongsTo
    {
        return $this->belongsTo(GeoCountry::class);
    }

    /**
     * Get the region associated with the company location.
     */
    public function geo_region(): BelongsTo
    {
        return $this->belongsTo(GeoRegion::class);
    }
}
