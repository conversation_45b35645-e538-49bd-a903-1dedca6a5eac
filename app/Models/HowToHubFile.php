<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * Class HowToHubFile
 * 
 * Represents a file associated with a "How To" hub section.
 *
 * @package App\Models
 */
class HowToHubFile extends Model
{
    use HasFactory;

    /** @var string File path */
    public const FILE_PATH = 'files/content';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'how_to_hub_id',
        'file',
    ];

    /**
     * Get the hub associated with the file.
     */
    public function hub()
    {
        return $this->belongsTo(HowToHub::class, 'how_to_hub_id');
    }

    public function getFileUrlAttribute()
    {
        $filePath = self::FILE_PATH . '/' . $this->file;
        return Storage::url($filePath);
    }
}
