<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::create('notification_assignees', function (Blueprint $table) {
        $table->id();
        $table->foreignId('category_id')->constrained('notifications_user_categories')
        ->onDelete('cascade');
        $table->unsignedBigInteger('user_id');
        $table->timestamps();

        $table->unique(['category_id', 'user_id']); // each user-category pair should be unique
    });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_assignees');
    }
};
