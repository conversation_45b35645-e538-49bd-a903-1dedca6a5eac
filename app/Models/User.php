<?php

namespace App\Models;

use App\Models\Product;
use App\Models\Traits\Uuids;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use App\Models\Contracts\Verification;
use Spatie\Permission\Traits\HasRoles;
use App\Models\Traits\HasVerifiedEmail;
use App\Models\Traits\HasVerifiedPhone;
use Illuminate\Notifications\Notifiable;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Notifications\AdminResetPasswordNotification;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Class User
 *
 * @package App\Models
 *
 * @property string $uuid
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $password
 * @property string|null $phone
 * @property int|null $country_id
 * @property int|null $area_id
 * @property string|null $status
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property-read string $name
 * @property-read \App\Models\UserAttribute|null $attribute
 * @property-read \App\Models\Country|null $country
 * @property-read \App\Models\Area|null $area
 *
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 */
class User extends Authenticatable implements HasMedia, Verification
{
    use HasFactory, Notifiable, Uuids, HasRoles, HasVerifiedEmail, HasVerifiedPhone, InteractsWithMedia, SoftDeletes;

    /** @var string Role identifier for admin users */
    public const ROLE_ADMIN = 'admin';

    /** @var string Role identifier for supplier users */
    public const ROLE_SUPPLIER = 'supplier';

    /** @var string Role identifier for regular users */
    public const ROLE_USER = 'user';

    /** @var integer Status identifier active users */
    public const STATUS_ACTIVE = 1;

    /** @var integer Status identifier in-active users */
    public const STATUS_INACTIVE = 0;

    /** @var integer Status identifier blocked users */
    public const STATUS_BLOCKED = 2;

    /** @var integer Status identifier suspended users */
    public const STATUS_SUSPENDED = 3;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'first_name',
        'last_name',
        'username',
        'email',
        'password',
        'phone',
        'phone_prefix',
        'area_id',
        'google_id',
        'linkedin_id',
        'about_me',
        'linkedin_link',
        'facebook_link',
        'instagram_link',
        'status',
        'last_login_at',
        'last_login_ip',
        'admin_user_role',
        'admin_notes',
        'stripe_customer_id',
        'closure_reason',
        'closure_text'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * The attributes that should be appended to the model's JSON form.
     *
     * @var array<int, string>
     */
    protected $appends = ['name'];


    /**
     * Register the media collections for the model.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('org_profile_photo')
            ->singleFile();
        $this->addMediaCollection('crp_profile_photo')
            ->singleFile();
        $this->addMediaCollection('org_cover_image')
            ->singleFile();
        $this->addMediaCollection('crp_cover_image')
            ->singleFile();
    }

    public function orgProfilePhoto(){
        if ($this->hasMedia('org_profile_photo')) {
            return $this->getFirstMediaUrl('org_profile_photo');
        }
    }

    public function crpProfilePhoto(){
        if ($this->hasMedia('crp_profile_photo')) {
            return $this->getFirstMediaUrl('crp_profile_photo');
        }
    }

    public function orgCoverImage(){
        if ($this->hasMedia('org_cover_image')) {
            return $this->getFirstMediaUrl('org_cover_image');
        }
    }

    public function crpCoverImage(){
        if ($this->hasMedia('crp_cover_image')) {
            return $this->getFirstMediaUrl('crp_cover_image');
        }
    }




    /**
     * Accessor for full name.
     *
     * Combines the first name and last name into a single string.
     *
     * @return string
     */
    public function getNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Accessor for assigned role name.
     *
     * @return string
     */
    public function getRoleNameAttribute(): string
    {
        return $this->getRoleNames()->first();
    }

    /**
     * Accessor for phone number with prefix.
     *
     *
     * @return string
     */
    public function getFullPhoneAttribute(): string
    {
        return "{$this->phone_prefix} {$this->phone}";
    }

    /**
     * Accessor for check if admin role.
     *
     * @return bool
     */
    public function getIsAdminAttribute(): bool
    {
        return $this->hasRole(self::ROLE_ADMIN);
    }

    /**
     * Accessor for check if user role.
     *
     * @return bool
     */
    public function getIsUserAttribute(): bool
    {
        return $this->hasRole(self::ROLE_USER);
    }

    /**
     * Accessor for check if admin role.
     *
     * @return bool
     */
    public function getIsSupplierAttribute(): bool
    {
        return $this->hasRole(self::ROLE_SUPPLIER);
    }

    /**
     * Accessor for check if admin role.
     *
     * @return bool
     */
    public function phone_prefix(): bool
    {
        //How to get the phone prefix from phone as it was stored +919
    }



    /**
     * Get the associated User Registration model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function company_registration(): HasOne
    {
        return $this->hasOne(CompanyRegistration::class);
    }

    /**
     * Get the associated User Registration Status model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function company_registration_status(): HasOne
    {
        return $this->hasOne(CompanyRegistrationStatus::class);
    }

    /**
     * Get the associated Area model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function area(): BelongsTo
    {
        return $this->belongsTo(Area::class);
    }

    /**
     * Scope a query to filter users by their status.
     *
     * @param \Illuminate\Database\Eloquent\Builder
     * @param int $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Send a password reset notification to the user.
     *
     * @param  string  $token
     */
    public function sendAdminPasswordResetNotification($token): void
    {
        $this->notify(new AdminResetPasswordNotification($token));
    }

    /**
     * Get the categories associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'user_categories');
    }

    public function user_company()
    {
        return $this->hasOne(UserCompany::class);
    }

    /**
     * Get the user profile with the user.
     */
    public function user_profile()
    {
        return $this->hasOne(UserProfileSetting::class);
    }


    /**
     * Get the user contact history with the user.
     */
    public function user_contact_history()
    {
        return $this->hasMany(UserContactHistory::class);
    }


    /**
     * company
     *
     * @return HasOne
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'user_id');
    }

    /**
     * company
     *
     * @return HasOne
     */
    public function otp()
    {
        return $this->belongsTo(OTP::class, 'user_id');
    }

    public function isCompanyRegistrationCompleted()
    {
        return $this->company_registration_status();
    }

    public function isCompanyRegistrationPending()
    {
        $companyRegistrationStatus = $this->company_registration_status;
        return $companyRegistrationStatus && $companyRegistrationStatus->status == $companyRegistrationStatus::STATUS_PENDING;
    }

    public function isCompanyRegistrationApproved()
    {
        $companyRegistrationStatus = $this->company_registration_status;
        $subscriptionActive = SubscriptionActive::where('user_id', $this->id)->first();
        return $companyRegistrationStatus && $companyRegistrationStatus->status == $companyRegistrationStatus::STATUS_APPROVED && empty($subscriptionActive);
    }

    public function isCompanyProfileSetup()
    {
        $subscriptionActive = SubscriptionActive::where('user_id', $this->id)->first();
        return $this->company && $subscriptionActive;
    }

    /**
     * checking for subscription
     *
     * @return HasOne
     */
    public function subscriptions(): HasOne
    {
        return $this->hasOne(SubscriptionActive::class);
    }

    public function saved_companies()
    {
        return $this->belongsToMany(Company::class, 'saved_companies', 'user_id', 'company_id')->withTimestamps();

    }


    public function savedProducts()
    {
        return $this->belongsToMany(Product::class, 'save_products', 'user_id', 'product_id');
    }

    /**
     * sentMessages
     *
     * @return HasMany
     */
    public function sentMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * receivedMessages
     *
     * @return HasMany
     */
    public function receivedMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'receiver_id');
    }

    public function latestMessage(): HasOne
    {
        return $this->hasOne(Message::class, 'sender_id')
            ->orWhere('receiver_id', $this->id)
            ->latestOfMany();
    }

    public function adminConversations()
    {
        return $this->hasMany(AdminConversation::class);
    }

    public function assignedNotifications()
    {
        return $this->hasMany(AdminNotification::class, 'assigned_to');
    }

    public function sentAdminMessages()
    {
        return $this->hasMany(AdminMessage::class, 'sender_id');
    }

    public function isCompanyAdmin(){
        
    }


}
