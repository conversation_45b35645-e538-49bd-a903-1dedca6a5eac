<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_regions', function (Blueprint $table) {
            $table->foreignId('company_country_id')
                ->constrained('company_countries')
                ->cascadeOnDelete()
                ->after('region_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_regions', function (Blueprint $table) {
            //
        });
    }
};
