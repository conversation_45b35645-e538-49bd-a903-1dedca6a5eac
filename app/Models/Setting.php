<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'settings';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'key',
        'name',
        'value',
        'type',
        'section',
        'info',
        'editable',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'editable' => 'boolean',
    ];

    /**
     * Constants for setting types.
     */
    public const TYPE_INPUT = 'input';
    public const TYPE_TEXTAREA = 'textarea';
    public const TYPE_EDITOR = 'editor';
    public const TYPE_IMAGE = 'image';
    public const TYPE_DROPDOWN = 'dropdown';

    /**
     * Constants for setting sections.
     */
    public const SECTION_SITE = 'site';
    public const SECTION_SOCIAL = 'social';
    public const SECTION_CONTACT = 'contact';
    public const SECTION_API_KEYS = 'api_keys';

    /**
     * Scope a query to only include editable settings.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEditable($query)
    {
        return $query->where('editable', true);
    }

    /**
     * Scope a query to filter by type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by section.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $section
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBySection($query, string $section)
    {
        return $query->where('section', $section);
    }
}
