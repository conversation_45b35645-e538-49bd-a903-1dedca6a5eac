<?php

namespace Database\Seeders;

use App\Models\IsoStandardCode;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class IsoCodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $isoCodes = [
            ['title' => 'Quality Management Systems (QMS)', 'code' => 'ISO 9001'],
            ['title' => 'Environmental Management Systems (EMS)', 'code' => 'ISO 14001'],
            ['title' => 'Occupational Health and Safety Management', 'code' => 'ISO 45001'],
            ['title' => 'Occupational Health and Safety Management', 'code' => 'ISO 45001'],
            ['title' => 'Information Security Management System (ISMS)', 'code' => 'ISO 27001'],
        ];
        foreach($isoCodes as $code){
            IsoStandardCode::create([
                'title' => $code['title'],
                'code' => $code['code'],
            ]);
        }
    }
}
