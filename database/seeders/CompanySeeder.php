<?php
namespace Database\Seeders;

use App\Models\CompanyRegistration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\CompanyLocation;
use App\Models\CompanyRegistrationStatus;
use Illuminate\Support\Str;
use App\Models\User;
use Spatie\Permission\Models\Role;
use App\Models\LocationType; // Import LocationType model

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
       

        User::factory()->count(10)->create()->each(function ($user) {
            // Assign "supplier" role to user
            $role = Role::firstOrCreate(['name' => User::ROLE_SUPPLIER]);
            $user->assignRole($role);

            $companyRegistration = CompanyRegistration::create([
                'user_id' => $user->id,
                'registered_location' => fake()->address,
                'organisation_type_id' => rand(1, 5),
                'organisation_size_id' => rand(1, 5),
                'business_sector_id' => rand(1, 5),
                'country_id' => rand(1, 200),
                'company_name' => fake()->company,
                'email' => fake()->unique()->companyEmail,
                'phone' => fake()->phoneNumber,
                'phone_prefix' => '+1',
                'website_url' => fake()->url,
                'position' => fake()->jobTitle,
                'address_line_1' => fake()->streetAddress,
                'address_line_2' => fake()->secondaryAddress,
                'city' => fake()->city,
                'postcode' => fake()->postcode,
                'latitude' => fake()->latitude,
                'longitude' => fake()->longitude,
            ]);


            // Create registration status
            CompanyRegistrationStatus::create([
                'user_id' => $user->id,
                'registration_id' => $companyRegistration->id,
                'status' => 0,
                'attempt_number' => rand(1, 5),
            ]);
        });
    }
}
