<?php

namespace App\Repositories;

use App\Models\CompanyAssessment;

/**
 * Class CompanyAssessmentRepository
 *
 * Handles data operations for the CompanyAssessment model.
 */
class CompanyAssessmentRepository extends BaseRepository
{
    /**
     * The CompanyAssessment model instance.
     *
     * @var \App\Models\CompanyAssessment
     */
    protected $model;

    /**
     * CompanyAssessmentRepository constructor.
     *
     * @param \App\Models\CompanyAssessment $companyAssessment
     * @return void
     */
    public function __construct(CompanyAssessment $companyAssessment)
    {
        $this->model = $companyAssessment;
    }
}
