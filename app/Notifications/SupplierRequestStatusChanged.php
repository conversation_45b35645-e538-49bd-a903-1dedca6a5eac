<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Exception;

class SupplierRequestStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    public $status;

    /**
     * Create a new notification instance.
     */
    public function __construct($status)
    {
        $this->status = $status;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    // public function toMail($notifiable)
    // {
    //     $message = "Dear {$notifiable->name}, your application request for company profile has been ";

    //     if ($this->status === 'approved') {
    //         $message .= "**approved**. 🎉 Congratulations! You can now access your account and start using our platform.";
    //     } else {
    //         $message .= "**rejected**. 😞 Unfortunately, we are unable to approve your request at this time. If you have any questions, please contact our support team.";
    //     }

    //     return (new MailMessage)
    //         ->subject('Your Company Profile Request Status Has Changed')
    //         ->greeting('Hello ' . $notifiable->name . ',')
    //         ->line($message)
    //         ->line('If you have any questions, please contact us.')
    //         ->action('View Your Account', url('/supplier-dashboard'))
    //         ->line('Thank you for being with us!');
    // }


    public function toMail($notifiable)
    {
        try {
            return (new MailMessage)
                ->mailer(config('mail.default')) // Primary mailer (SES)
                ->subject('Your Company Profile Request Status Has Changed')
                ->greeting("Hello, {$notifiable->name}")
                ->line($this->status === 'approved' ?
                    '🎉 Congratulations! Your application request for a company profile has been **approved**. You can now access your account and start using our platform.' :
                    '😞 Unfortunately, your application request for a company profile has been **rejected**. If you have any questions, please contact our support team.')
                ->action('View Your Account', url('/supplier-dashboard'))
                ->line('Thank you for being with us!');
        } catch (Exception $e) {
            Log::error('Mail Sending Failed: ' . $e->getMessage()); // Log the error
            return (new MailMessage)
                ->mailer(env('MAIL_FAILOVER', 'smtp')) // Fallback mailer (SMTP)
                ->subject('Your Company Profile Request Status Has Changed')
                ->greeting("Hello, {$notifiable->name}")
                ->line($this->status === 'approved' ?
                    '🎉 Congratulations! Your application request for a company profile has been **approved**. You can now access your account and start using our platform.' :
                    '😞 Unfortunately, your application request for a company profile has been **rejected**. If you have any questions, please contact our support team.')
                ->action('View Your Account', url('/supplier-dashboard'))
                ->line('Thank you for being with us!');
        }
    }


    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
