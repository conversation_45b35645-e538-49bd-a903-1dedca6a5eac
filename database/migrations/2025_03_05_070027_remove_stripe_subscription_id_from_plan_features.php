<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('plan_features', function (Blueprint $table) {
            $table->dropForeign(['stripe_subscription_id']);
            $table->dropColumn('stripe_subscription_id');
            $table->dropForeign(['parent_id']);
            $table->dropColumn('parent_id');
        });
    }

    public function down(): void
    {
        Schema::table('plan_features', function (Blueprint $table) {
            $table->unsignedBigInteger('stripe_subscription_id')->after('id');
            $table->foreign('stripe_subscription_id')->references('id')->on('stripe_subscriptions')->onDelete('cascade');
            $table->unsignedBigInteger('parent_id')->nullable()->after('stripe_subscription_id');
        });
    }
};
