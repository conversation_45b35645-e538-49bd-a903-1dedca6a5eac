<?php

namespace App\Services;

use App\Repositories\QuotationRepository as Repository;
use App\Traits\DBService;
use Illuminate\Support\Facades\Auth;
use App\Models\QuotationRequest;

class QuotationService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

     /*
     * QuotationRepository Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }
    
    /**
     * Get current loggedin user
     *
     * @return App\Models\User
     */
    public function current()
    {
        return Auth::user();
    }

    public function getListForAdmin(string $status = null, string $role = null)
    {
        $conditions = [];
        if (!is_null($status)) {
            $conditions['status'] = $status;
        }

        if ($role) {
            $conditions['role'] = $role;
        }

       
        return $this->repository->getByMixedCondition($conditions);
    }
    
    

    /**
     * Get list of all the statuses
     *
     * @return array
     */
    public function getStatuses()
    {
        return $this->repository->statuses();
    }
}
