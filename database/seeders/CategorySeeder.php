<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    public function run()
    {
        // Create root categories
        $rootCategories = Category::factory()
            ->count(5)
            ->create();

        // Create child categories for each root category
        foreach ($rootCategories as $root) {
            $childCategories = Category::factory()
                ->count(3)
                ->child($root->id, $root->level + 1, $root->type)
                ->create();

            // Create sub-child categories
            foreach ($childCategories as $child) {
                Category::factory()
                    ->count(2)
                    ->child($child->id, $child->level + 1, $child->type)
                    ->create();
            }
        }
    }
}

