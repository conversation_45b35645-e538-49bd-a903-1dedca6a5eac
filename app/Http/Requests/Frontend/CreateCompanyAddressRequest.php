<?php

namespace App\Http\Requests\Frontend;

use App\Rules\UniquePhoneWithCode;
use App\Rules\ValidPhoneNumberCode;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class CreateCompanyAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'city' => ['required', 'string'],
            'country_code' => ['nullable', 'string'],
            'latitude' => ['nullable', 'string'],
            'longitude' => ['nullable', 'string'],
            'postal_code' => ['required', 'string', 'max:12'],
            'address_line_1' => ['required', 'string', 'max:100'],
            'address_line_2' => ['nullable', 'string', 'max:100'],
            'country' => ['required', 'string'],
            'phone_prefix' => ['nullable', 'string'],
            'number' => [
                'nullable',
                new ValidPhoneNumberCode(request()->phone_prefix),
                new UniquePhoneWithCode(request()->phone_prefix),]
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function failedValidation(Validator $validator): void
    {
        throw new ValidationException(
            $validator,
            \Redirect::back()
                ->withInput()
                ->withErrors($validator)
                ->with('modalToShow', $this->currentModalToDisplay())
        );
    }

    /**
     * Get the ID of the modal that should be shown after validation failure.
     *
     * @return string
     */
    public function currentModalToDisplay(): string
    {
        return config('settings.modal.company-registration-step2');
    }

    /**
     * Get the ID of the modal that should be shown after the request is successful.
     *
     * @return string
     */
    public function nextModalToDisplay(): string
    {
        return config('settings.modal.company-registration-step3');
    }
}
