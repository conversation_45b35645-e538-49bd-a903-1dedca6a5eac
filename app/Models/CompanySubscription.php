<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Class Subscription
 * 
 * Represents a subscription to a plan by a user.
 *
 * @package App\Models
 */
class CompanySubscription extends Model
{
    use HasFactory;

    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELED = 'canceled';
    const STATUS_PAST_DUE = 'past_due';
    const STATUS_UNPAID = 'unpaid';
    const STATUS_INCOMPLETE = 'incomplete';
    const STATUS_INCOMPLETE_EXPIRED = 'incomplete_expired';
    const STATUS_TRIALING = 'trialing';

    protected $fillable = [
        'company_id',
        'stripe_plan_id',
        'subscription_type',
        'stripe_customer_id',
        'transaction_id',
        'info',
        'price',
        'status',
        'started_at',
        'ended_at',
        'trial_ends_at',
    ];
    
    /**
     * casts
     *
     * @var array
     */
    protected $casts = [
        'info' => 'array',
    ];
    
    
    /**
     * Get the status label and style dynamically.
     *
     * @param string $status
     * @return array
     */
    public static function getStatusLabelAndStyle($status)
    {
        $statuses = [
            self::STATUS_ACTIVE => ['label' => 'Active', 'style' => 'background-color:#358b00;'],
            self::STATUS_CANCELED => ['label' => 'Canceled', 'style' => 'background-color:#ff0404;'],
            self::STATUS_PAST_DUE => ['label' => 'Past Due', 'style' => 'background-color:#e0a800;'],
            self::STATUS_UNPAID => ['label' => 'Unpaid', 'style' => 'background-color:#d9534f;'],
            self::STATUS_INCOMPLETE => ['label' => 'Incomplete', 'style' => 'background-color:#f0ad4e;'],
            self::STATUS_INCOMPLETE_EXPIRED => ['label' => 'Incomplete Expired', 'style' => 'background-color:#6c757d;'],
            self::STATUS_TRIALING => ['label' => 'Trialing', 'style' => 'background-color:#0275d8;'],
        ];

        return $statuses[$status] ?? ['label' => 'Unknown', 'style' => 'background-color:#6c757d;'];
    }
    
    /**
     * Scope a query to filter subscription by their status.
     *
     * @param \Illuminate\Database\Eloquent\Builder
     * @param int $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Get the user associated with the subscription.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * The features that belong to the subscription.
     */
    public function features(): BelongsToMany
    {
        return $this->belongsToMany(PlanFeature::class, 'subscription_features');
    }
}
