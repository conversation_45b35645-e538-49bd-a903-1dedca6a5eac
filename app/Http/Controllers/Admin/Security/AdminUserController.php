<?php

namespace App\Http\Controllers\Admin\Security;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreAdminUserRequest;
use App\Http\Requests\Admin\UpdateAdminUserRequest;
use App\Models\User;
use App\Services\UniqueIdService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use App\Services\UserService;
use Illuminate\Support\Facades\Gate;
use Yajra\DataTables\Facades\DataTables;
use Spatie\Permission\Models\Permission;

class AdminUserController extends Controller
{
    /**
     * User service
     *
     * @var UserService
     */
    public $service;

    /**
     * Role name
     *
     */
    public $role;

    /**
     * Create a new controller instance.
     *  
     * @param UserService $service
     * @return void
     */
    public function __construct(UserService $service)
    {
        $this->service = $service;
        $this->role = User::ROLE_ADMIN;
    }

    public function index(Request $request)
    {
        $role = $this->role;
        $user = $this->service->current();
        $status = $request->status;

        if ($request->ajax()) {
            $users = $this->service->getListForAdmin($status, $role, $user->id);
            $statuses = $this->service->getStatuses();

            return DataTables::of($users)
                ->addColumn('created_at', function ($user) {
                    return $user->created_at;
                })
                ->addColumn('last_login_at', function ($user) {
                    return $user->last_login_at;
                })
                ->addColumn('status', function ($user) {
                    $status = '';
                    if ($user->status == User::STATUS_INACTIVE) {
                        $status = '<div style="background-color:#d35757;text-align: center;border-radius: 15px;color: #fff;">In-Active</div>';
                    } elseif ($user->status == User::STATUS_ACTIVE) {
                        $status = '<div style="background-color:#358b00;text-align: center;border-radius: 15px;color: #fff;">Active</div>';
                    } elseif ($user->status == User::STATUS_BLOCKED) {
                        $status = '<div style="background-color:#b59600;text-align: center;border-radius: 15px;color: #fff;">Blocked</div>';
                    } elseif ($user->status == User::STATUS_SUSPENDED) {
                        $status = '<div style="background-color:#ff0404;text-align: center;border-radius: 15px;color: #fff;">Suspended</div>';
                    }
    
                    return $status;
                })
                ->addColumn('action', function ($user) use ($statuses) {
                    $action = '';
                    $editUrl = route('admin.security.admin_users.edit', $user);
                    $action .= '<a href="' . $editUrl . '" class="btn btn-primary"><i class="fa fa-edit">&nbsp;Edit</i></a> ';
                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                    Action
                </button>
                <ul class="dropdown-menu" style="">';
                    foreach ($statuses as $name => $value) {
                        $disabled = ($user->status == $value) ? 'disabled' : '';

                        $action .= '<li class="dropdown-item">';
                        $action .= '    <button class="btn btn-sm btn-primary change-status-btn ' . $disabled . '" data-id="' . $user->id . '" ' . $disabled . ' data-statusname="' . $name . '" data-status="' . $value . '">' . $name . '</button>';
                        $action .= '</li>';
                    }
                    // $action .= '<li class="dropdown-item"><button class="btn btn-danger delete-btn change-status-btn" data-id="' . $user->id . '" data-status="delete"><i class="fa fa-trash-alt"></i> Delete</button></li>';
                    $action .= '</ul>';
                    return $action;
                })
                ->addColumn('admin_user_role', function ($user) {
                    return $user->admin_user_role??'N/A';
                })
                ->rawColumns(['status', 'action'])
                ->toJson();
        }

        return view('admin.security.admin_users.index', compact('user', 'role'));
    }

    // Show form to create a new admin user
    public function create()
    {
        $superUserPermission = Permission::findByName('super user')->toArray();
        $managePermissions = Permission::where('name', 'like', 'manage%')->pluck('name', 'slug');
        $viewPermissions = Permission::where('name', 'like', 'view%')->pluck('name', 'slug');
        $userPermissions = [];
        $mode = 'add';

        return view('admin.security.admin_users.create', compact(
            'superUserPermission',
            'managePermissions',
            'viewPermissions',
            'userPermissions',
            'mode'
        ));
    }

    // Store a new admin user
    public function store(StoreAdminUserRequest $request)
    {
        $data = [
            'username' => $request->username,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'status' => $request->status,
            'admin_user_role' => $request->admin_user_role,
        ];

        // $data['username'] = app(UniqueIdService::class)->generate(
        //     $request->first_name,
        //     $request->last_name,
        //     'GB',
        // );

        $user = $this->service->create($data);
        $user->assignRole($this->role);
        $this->service->setPermissions($user, $request->permissions);

        return redirect()->route('admin.security.admin_users.index')
            ->with('success', 'Admin user created successfully.');
    }

    // Show form to edit an admin user
    public function edit(User $admin_user)
    {
        $superUserPermission = Permission::findByName('super user')->toArray();
        $managePermissions = Permission::where('name', 'like', 'manage%')->pluck('name', 'slug');
        $viewPermissions = Permission::where('name', 'like', 'view%')->pluck('name', 'slug');
        $userPermissions = $admin_user->getAllPermissions()->pluck('name', 'slug')->toArray();
        $mode = 'edit';
        // dd($userPermissions);

        return view('admin.security.admin_users.edit', compact(
            'admin_user',
            'superUserPermission',
            'managePermissions',
            'viewPermissions',
            'userPermissions',
            'mode'
        ));
    }

    // Update an admin user
    // public function update(UpdateAdminUserRequest $request, User $admin_user)
    public function update(UpdateAdminUserRequest $request, User $admin_user)
    {
        $data = [
            'username' => $request->username,
            'status' => $request->status,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'admin_user_role' => $request->admin_user_role,
        ];

        if ($request->password) {
            $data['password'] = Hash::make($request->password);
        }

        $admin_user = $this->service->update($admin_user->id, $data);
        $this->service->setPermissions($admin_user, $request->permissions);

        return redirect()->route('admin.security.admin_users.index')
            ->with('success', 'Admin user updated successfully.');
    }

    // Delete an admin user
    public function destroy($id)
    {
        $adminUser = User::findOrFail($id);
        $adminUser->delete();

        return redirect()->route('admin.security.admin_users.index')->with('success', 'Admin user deleted successfully.');
    }

    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;

        $user = $this->service->find($id);

        if (!$user) {
            return response()->json(['message' => 'No user Found']);
        }

        $this->service->update($id, ['status' => $status]);

        return response()->json(['message' => 'Status changed successfully']);
    }
}
