<?php

namespace App\Services\Content;

use App\Repositories\PageRepository as Repository;
use App\Traits\DBService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Database\Eloquent\Builder;

class PageService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

     /*
     * Page Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get query or collection for the page list
     *
     * @param boolean $query
     * @param array $params
     * @return Builder|Collection
     */
    public function getListForAdmin(bool $query = false, array $params = []) : Builder|Collection
    {
        if ($query) {
            return $this->repository->queryByMixedCondition($params);
        } else {
            return $this->repository->getByMixedCondition($params);
        }        
    }
    
    /**
     * getStatuses
     *
     * @return array
     */
    public function getStatuses(): array
    {
        return $this->repository->statuses();
    }
}
