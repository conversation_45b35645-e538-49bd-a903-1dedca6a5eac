<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use OpenSearch\ClientBuilder;

class OpenSearchServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('opensearch', function () {
            return ClientBuilder::create()
                ->setHosts([env('OPENSEARCH_HOST')])
                ->build();
        });
    }

    public function boot()
    {
        //
    }
}
