<?php

namespace App\Http\Controllers\Frontend\Company;

use App\Http\Actions\CompanyRegistrationAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\CreateCompanyAddressRequest;
use App\Http\Requests\Frontend\CreateCompanyEmailRequest;
use App\Http\Requests\Frontend\CreateCompanyRequest;
use App\Models\Category;
use App\Models\CompanyRegistration;
use App\Models\Country;
use App\Models\OrganisationSize;
use App\Models\OrganisationType;
use App\Models\OrganisationTypeLocal;
use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class CompanyRegistrationController extends Controller
{
    public function createCompanyProfile()
    { 
        if(getCompanyId()){
           return redirect()->back();
        }
        $area = auth()->user()->area ?? null;
        $country = $area ? $area->country : null;
        $organisationTypeLocals = $country ? $country->organisation_type_locals : [];
        $organisationTypes = OrganisationType::all();
        $organisationSizes = OrganisationSize::all();
        $categories = $this->getCategoriesData(1, 2);
        
        return view('frontend.company.create-company-profile', [
            'area' => $area,
            'country' => $country,
            'organisationTypes' => $organisationTypes,
            'organisationTypeLocals' => $organisationTypeLocals,
            'organisationSizes' => $organisationSizes,
            'categories' => $categories,
            'companyRegistrationData' => []
        ]);

    }

    public function getOrganisationByCountry($countryCode){
        if($countryCode){
            $organisationTypeLocals = OrganisationTypeLocal::with('organisation_type')
                                                           ->where('iso_code', $countryCode)->get();
            return response()->json($organisationTypeLocals);
        }
        return response()->json([]);

    }

    public function getCategoriesBySector($businessType, $level, $parent=null) : JsonResponse{
        $categories = $this->getCategoriesData($businessType, $level, $parent);
        return response()->json($categories);
    }

    public function getCategoriesData($businessType, $level, $parent=null)
    {
        if (empty($businessType)) {
            return collect(); // Return an empty collection
        }

        return Category::where([
            'level' => $level,
            'type' => $businessType,
            'status' => 1
        ])->
        when($parent, function($query, $parent){
           $query->where('parent_id', $parent);
        })->get();
    }

    public function getCountriesByCode($code) : JsonResponse{
        $country = Country::where('code', $code)->first();
        return response()->json($country);
    }

    public function createCompanyDetails(CreateCompanyRequest $request, CompanyRegistrationAction $action){
        $data = $request->validated();
        try{
            $action($data);
            return \Redirect::back()
                   ->with('modalToShow', $request->nextModalToDisplay());

        }catch (Exception $e) {
            return \Redirect::back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }
    }

    public function backToCompanyRegsitrationStep1(){
        return \Redirect::back()
                ->with('modalToShow', config('settings.modal.company-registration-step1'));
    }

    public function createCompanyAddressDetails(CreateCompanyAddressRequest $request, CompanyRegistrationAction $action){
        $data = $request->validated();
        try{
            $action->storeCompanyAddress($data);
            return \Redirect::back()
                   ->with('modalToShow', $request->nextModalToDisplay());

        }catch (Exception $e) {
            return \Redirect::back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }
    }
    public function backToCompanyRegsitrationStep2(){
        return \Redirect::back()
                ->with('modalToShow', config('settings.modal.company-registration-step2'));
    }

    public function storeCompanyEmail(CreateCompanyEmailRequest $request, CompanyRegistrationAction $action){
        $data = $request->validated();
        try{
            Session::put('verifyType', 'verifyCompanyEmail');
            $action->storeCompanyEmail($data);
            return \Redirect::back()
                   ->with('modalToShow', $request->nextModalToDisplay());

        }catch (Exception $e) {
            return \Redirect::back()
                ->withErrors(['catchError' => $e->getMessage()])
                ->with('modalToShow', $request->currentModalToDisplay());
        }
    }

    public function backToCompanyRegsitrationStep3(){
        return \Redirect::back()
                ->with('modalToShow', config('settings.modal.company-registration-step3'));
    }
    
}
