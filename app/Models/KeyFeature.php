<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class KeyFeature extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['feature'];

    /**
     * Automatically generate a UUID when creating a product.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->uuid) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'key_feature_product');
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'key_feature_service');
    }
}
