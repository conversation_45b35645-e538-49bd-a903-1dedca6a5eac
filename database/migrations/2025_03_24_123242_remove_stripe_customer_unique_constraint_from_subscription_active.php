<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_active', function (Blueprint $table) {
            $table->dropUnique('subscription_active_stripe_customer_id_unique');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_active', function (Blueprint $table) {
            $table->unique('stripe_customer_id', 'subscription_active_stripe_customer_id_unique');

        });
    }
};
