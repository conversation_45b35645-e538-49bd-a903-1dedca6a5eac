<?php

namespace App\Repositories;

use App\Models\PlanFeature;

/**
 * Class PlanFeatureRepository
 *
 * Handles data operations for the PlanFeature model.
 */
class PlanFeatureRepository extends BaseRepository
{
    /**
     * The PlanFeature model instance.
     *
     * @var \App\Models\PlanFeature
     */
    protected $model;

    /**
     * PlanFeatureRepository constructor.
     *
     * @param \App\Models\PlanFeature $planFeature
     */
    public function __construct(PlanFeature $planFeature)
    {
        $this->model = $planFeature;
    }
}
