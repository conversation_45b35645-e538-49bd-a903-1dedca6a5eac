.image-modal-content{
    .modal-content{
        background: transparent !important;
        border: 0px;
        position: relative !important;
        width: 670px !important;
        margin: 0 auto !important; 
    }

    button.owl-prev {
        position: absolute;
        left: 0px;
        top: 17%;
    }

    button.owl-next {
        position: absolute;
        right: 0px;
        top: 27%;
    }
    button {
        img{
            background-color: white;
            border-radius: 50px;
            height: 40px;
            width: 40px;
            padding: 12px;
        }
    }

    .owl-nav {
        position: absolute;
        width: 100%;
        top: 50%;
    }

    .item {
        width: 100%;
        margin: 0 auto;
        padding: 10px 50px;
        img{
            width: 100%;
            margin: 0 auto;
            height: 450px !important;
            object-fit: cover;
        }
    }
    .custom-modal .modal-body {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        height: 100vh !important;
        background-color: transparent !important;
    }
    .custom-modal .modal-header {
        background-color: transparent !important;
        border: 0px !important;
        justify-content: flex-end !important;
        position: absolute;
        right: 0px;

        .btn-close {
            opacity: 1;
            background-color: #ffffffeb !important;
            color: #fff;
            z-index: 9999; 
            font-size: 0px;
            border-radius: 50px;
            padding: 7px;
            height: 20px;
            width: 20px;
            i{
                opacity: 1;
                color: #038799;
                z-index: 9999;
                font-size: 16px;
                border-radius: 50px;
                padding: 0px;
            }
        }
    }
    .modal-dialog{
        left: 50% !important;
        top: 50px !important;
        transform: translate(-50%, 0%) !important;
        max-width: 100% !important;
    }
    
}
@media (max-width:767px){
    .image-modal-content .modal-content{
        width:100% !important
    }
    .image-modal-content .modal-dialog{
        transform: translate(0%) !important;
        max-width: 100% !important;
    }
}