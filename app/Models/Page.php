<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

/**
 * Class Page
 * 
 * Represents a page with content and metadata.
 *
 * @package App\Models
 */
class Page extends Model
{
    use HasFactory, HasSlug;

    /** @var integer Status identifier active content */
    public const PUBLISHED = 1;

    /** @var integer Status identifier in-active content */
    public const DRAFTED = 0;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'content',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'status',
        'created_by',
        'updated_by',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    /**
     * Scope for retrieving only published pages.
     *
     * @param $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('status', 1);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    
    /**
     * updater
     *
     * @return void
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
