<?php

namespace App\Repositories;

use App\Models\UserCompany;

/**
 * Class UserCompanyRepository
 *
 * Handles data operations for the UserCompany model.
 */
class UserCompanyRepository extends BaseRepository
{
    /**
     * The UserCompany model instance.
     *
     * @var \App\Models\UserCompany
     */
    protected $model;

    /**
     * UserCompanyRepository constructor.
     *
     * @param \App\Models\UserCompany $userCompany
     */
    public function __construct(UserCompany $userCompany)
    {
        $this->model = $userCompany;
    }
}
