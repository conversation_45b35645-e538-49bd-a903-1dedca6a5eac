<?php
namespace App\Services;

use Illuminate\Support\Facades\Mail;
use Exception;
use Log;

class MailService
{
    public static function send($mailable, $recipient)
    {
        try {
            // Attempt to send using AWS SES
            Mail::mailer('ses')->to($recipient)->send($mailable);
        } catch (Exception $e) {
            \Log::error('AWS SES failed: ' . $e->getMessage());
            try {
                // Fallback to SMTP
                Mail::mailer('smtp')->to($recipient)->send($mailable);
                \Log::info('Email sent using SMTP as fallback.');
            } catch (Exception $ex) {
                \Log::error('SMTP fallback also failed: ' . $ex->getMessage());
            }
        }
    }
}
