<?php

namespace App\Repositories;

use App\Models\Setting;

/**
 * Class SettingRepository
 *
 * Handles data operations for the Setting model.
 */
class SettingRepository extends BaseRepository
{
    /**
     * The Setting model instance.
     *
     * @var \App\Models\Setting
     */
    protected $model;

    /**
     * SettingRepository constructor.
     *
     * @param \App\Models\Setting $setting
     * @return void
     */
    public function __construct(Setting $setting)
    {
        $this->model = $setting;
    }

    /**
     * Retrieve all records from the model using group by.
     *
     * @param string $groupBy
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByGroupBy(String $groupBy)
    {
        return $this->model->all()->groupBy($groupBy);
    }

    /**
     * Get all section array
     *
     * @return array
     */
    public function sections()
    {
        return [
            $this->model::SECTION_SITE => 'Site Settings',        
            $this->model::SECTION_CONTACT => 'Contact Info',
            $this->model::SECTION_SOCIAL => 'Social Info',
            // $this->model::SECTION_API_KEYS => 'API Keys',
        ];
    }

    /**
     * Get all types array
     *
     * @return array
     */
    public function types()
    {        
       return [
            $this->model::TYPE_INPUT,
            $this->model::TYPE_TEXTAREA,
            $this->model::TYPE_EDITOR,
            $this->model::TYPE_IMAGE,
            $this->model::TYPE_DROPDOWN
       ];
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByMixedCondition(array $params)
    {
        $query = $this->model->select();

        /** filter by status */
        if (isset($params['keys'])) {
            $query->whereIn('key', $params['keys']);
        }

        return $query->get();
    }
}
