<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update products table         
        Schema::table('products', function (Blueprint $table) {
            // Step 1: Drop foreign key constraint (replace 'products_company_id_foreign' with your actual constraint name)
            $table->dropForeign(['company_id']);
        });

        Schema::table('products', function (Blueprint $table) {
            // Step 2: Drop the column
            $table->dropColumn('company_id');
        });

        Schema::table('products', function (Blueprint $table) {
            // Step 3: Re-add company_id in the correct position
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->unsignedBigInteger('company_id')->after('uuid');
            $table->softDeletes();

            // Step 4: Restore foreign key constraint
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
        });

        // Update services table
        Schema::table('services', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update key_features table
        Schema::table('key_features', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update after_sale_supports table
        Schema::table('after_sale_supports', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update product_pricings table
        Schema::table('product_pricings', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update service_pricings table
        Schema::table('service_pricings', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update product_shippings table
        Schema::table('product_shippings', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update product_attributes table
        Schema::table('product_attributes', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update service_attributes table
        Schema::table('service_attributes', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update product_certifications table
        Schema::table('product_certifications', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update service_qualifications table
        Schema::table('service_qualifications', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        // Update product_standards_compliances table
        Schema::table('product_standards_compliances', function (Blueprint $table) {
            $table->uuid('uuid')->after('id')->unique()->index();
            $table->softDeletes();
        });

        Schema::dropIfExists('service_certifications');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
