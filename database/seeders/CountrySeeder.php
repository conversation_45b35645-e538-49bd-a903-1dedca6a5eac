<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Country;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $json = file_get_contents(database_path('seeders/stubs/countries.json'));
        $countries = collect(json_decode($json, true));

        $countries->each(function ($country) {
            Country::updateOrCreate(
                ['id' => $country['id']], // Match based on the unique 'id'
                [
                    "id" => $country['id'],
                    "code" => $country['code'],
                    "name" => $country['name'],
                    "currency_code" => $country['currency_code'],
                    "fips_code" => $country['fips_code'],
                    "iso_numeric" => $country['iso_numeric'],
                    "continent_name" => $country['continent_name'],
                    "continent" => $country['continent'],
                    "phone_prefix" => $country['phone_prefix'],
                ]
            );
        });
    }
}
