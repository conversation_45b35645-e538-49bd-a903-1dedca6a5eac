<?php

namespace App\Repositories;

use App\Models\CompanyIsoCompliance;

/**
 * Class CompanyIsoComplianceRepository
 *
 * Handles data operations for the CompanyIsoCompliance model.
 */
class CompanyIsoComplianceRepository extends BaseRepository
{
    /**
     * The CompanyIsoCompliance model instance.
     *
     * @var \App\Models\CompanyIsoCompliance
     */
    protected $model;

    /**
     * CompanyIsoComplianceRepository constructor.
     *
     * @param \App\Models\CompanyIsoCompliance $companyIsoCompliance
     */
    public function __construct(CompanyIsoCompliance $companyIsoCompliance)
    {
        $this->model = $companyIsoCompliance;
    }
}
