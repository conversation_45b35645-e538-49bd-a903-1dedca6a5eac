<?php

namespace App\Repositories;

use App\Models\CompanyAssessmentStatus;

/**
 * Class CompanyAssessmentStatusRepository
 *
 * Handles data operations for the CompanyAssessmentStatus model.
 */
class CompanyAssessmentStatusRepository extends BaseRepository
{
    /**
     * The CompanyAssessmentStatus model instance.
     *
     * @var \App\Models\CompanyAssessmentStatus
     */
    protected $model;

    /**
     * CompanyAssessmentStatusRepository constructor.
     *
     * @param \App\Models\CompanyAssessmentStatus $companyAssessmentStatus
     * @return void
     */
    public function __construct(CompanyAssessmentStatus $companyAssessmentStatus)
    {
        $this->model = $companyAssessmentStatus;
    }
}
