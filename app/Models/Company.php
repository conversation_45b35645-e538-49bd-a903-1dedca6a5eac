<?php

namespace App\Models;

use App\Models\OrganisationTypeLocal;
use App\Traits\CompanyRelations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Laravel\Scout\Searchable;

/**
 * Class Company
 *
 * @package App\Models
 * 
 * @property int $id
 * @property int $organisation_type_id
 * @property int $organisation_size_id
 * @property string $name
 * @property string $slug
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $phone_prefix
 * @property int|null $year_founded
 * @property string|null $description
 * @property string|null $logo
 * @property string|null $cover_image
 * @property string|null $website_url
 * @property bool $workplace_verification
 * @property bool $display_phone
 * @property string|null $phone_contact_title
 * @property string|null $chat_contact_title
 * @property string|null $compliance_summary
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Company extends Model implements HasMedia
{
    use HasFactory, HasSlug, Searchable, InteractsWithMedia, CompanyRelations;

    /**
     * @var int Status identifier for inactive company.
     */
    public const STATUS_PENDING = 0;

    /**
     * @var int Status identifier for active company.
     */
    public const STATUS_ACTIVE = 1;

    /**
     * @var int Status identifier for rejected company.
     */
    public const STATUS_REJECTED = 2;

    /**
     * @var int Status identifier for approved company.
     */
    public const STATUS_APPROVED = 3;

    /**
     * @var int Status identifier for company account closure.
     */
    public const STATUS_CLOSED = 4;

    /**
     * @var int Status identifier for freeze company.
     */
    public const STATUS_FREEZED = 5;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'registered_location',
        'organisation_type_id',
        'organisation_size_id',
        'name',
        'slug',
        'email',
        'allow_email_forwarding',
        'phone',
        'phone_prefix',
        'year_founded',
        'description',
        'website_url',
        'workplace_verification',
        'show_workplace_verfification',
        'display_phone',
        'phone_contact_title',
        'notification_access_ids',
        'chat_contact_title',
        'chat_contact_user_id',
        'compliance_summary',
        'advertising_from',
        'advertising_to',
        'status',
        'admin_note'

    ];


    /**
     * The attributes that should be searchable in the model.
     *
     * @var array<string>
     */
    public static array $searchableFields = [
        'name',
        'company',
        'description',
        'website_url',
        'phone',
        'email',
        'country_ids',
        'region_ids',
        'categories',
    ];

    protected $casts = [
        'notification_access_ids' => 'array',
    ];



    public static function getStatusLabels()
    {
        return [
            self::STATUS_PENDING => ['text' => 'Pending', 'class' => 'btn-primary'],
            self::STATUS_ACTIVE => ['text' => 'Active', 'class' => 'btn-success'],
            self::STATUS_REJECTED => ['text' => 'Rejected', 'class' => 'btn-warning'],
            self::STATUS_APPROVED => ['text' => 'Approved', 'class' => 'btn-primary'],
            self::STATUS_CLOSED => ['text' => 'Closed', 'class' => 'btn-danger'],
            self::STATUS_FREEZED => ['text' => 'Freezed', 'class' => 'btn-warning'],
        ];
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('company_org_logo')->singleFile();
        $this->addMediaCollection('company_crp_logo')->singleFile();
        $this->addMediaCollection('company_org_cov_image')->singleFile();
        $this->addMediaCollection('company_crp_cov_mage')->singleFile();
        $this->addMediaCollection('company_org_adv_image')->singleFile();
        $this->addMediaCollection('company_crp_adv_image')->singleFile();
    }

    public function getOriginalLogoAttribute()
    {
        if ($this->hasMedia('company_org_logo')) {
            return $this->getFirstMediaUrl('company_org_logo');
        }
    }


    public function getCroppedLogoAttribute()
    {
        if ($this->hasMedia('company_crp_logo')) {
            return $this->getFirstMediaUrl('company_crp_logo');
        }
        return null;
    }

    public function getOriginalCoverImageAttribute()
    {
        if ($this->hasMedia('company_org_cov_image')) {
            return $this->getFirstMediaUrl('company_org_cov_image');
        }
        return null;
    }

    public function getCroppedCoverImageAttribute()
    {
        if ($this->hasMedia('company_crp_cov_mage')) {
            return $this->getFirstMediaUrl('company_crp_cov_mage');
        }
        return null;
    }
    

    public function getOriginalAdvertisementImageAttribute()
    {
        if ($this->hasMedia('company_org_adv_image')) {
            return $this->getFirstMediaUrl('company_org_adv_image');
        }
        return null;
    }

    public function getCroppedAdvertisementImageAttribute()
    {
        if ($this->hasMedia('company_crp_adv_image')) {
            return $this->getFirstMediaUrl('company_crp_adv_image');
        }
        return null;
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }


    /**
     * Accessor for phone number with prefix.
     *
     *
     * @return string
     */
    public function getFullPhoneAttribute(): string
    {
        return "{$this->phone_prefix} {$this->phone}";
    }

    /**
     * registrationStatus
     *
     * @return HasOne
     */
    public function registrationStatus(): HasOne
    {
        return $this->hasOne(CompanyRegistrationStatus::class);
    }

    /**
     * Get all services associated with the company.
     */
    public function services()
    {
        return $this->hasMany(Product::class)->where('type', 'service');
    }
    /**
     * Get all products associated with the company.
     */
    public function products()
    {
        return $this->hasMany(Product::class)->where('type', 'product');
    }

    /**
     * Get all locations associated with the company.
     */
    public function company_locations()
    {
        return $this->hasMany(CompanyLocation::class);
    }
    /**
     * Get the main location associated with the company.
     */
    public function mainLocation()
    {
        return $this->hasOne(CompanyLocation::class)->where('is_main', 1);
    }
    /**
     * Get business categories associated with the company.
     */
    public function company_business_categories()
    {
        return $this->hasMany(CompanyBusinessCategory::class);
    }

    /**
     * Get the company contact history with the company.
     */
    public function company_contact_history()
    {
        return $this->hasMany(CompanyContactHistory::class);
    }

    /**
     * company iso compliance
     *
     * @return HasMany
     */
    public function company_iso_compliance(): HasMany
    {
        return $this->hasMany(CompanyIsoCompliance::class);
    }

    /**
     * company personnel qualifications
     *
     * @return HasMany
     */
    public function company_personnel_qualifications(): HasMany
    {
        return $this->hasMany(CompanyPersonnelQualification::class);
    }

    /**
     * company product certifications
     *
     * @return HasMany
     */
    public function company_product_certifications(): HasMany
    {
        return $this->hasMany(CompanyProductCertification::class);
    }

    /**
     * CompanyConti
     *
     * @return HasMany
     */
    public function company_continents(): HasMany
    {
        return $this->hasMany(CompanyContinent::class);
    }

    /**
     * CompanyCountry
     *
     * @return HasMany
     */
    public function company_countries(): HasMany
    {
        return $this->hasMany(CompanyCountry::class);
    }

    /**
     * CompanyCountry
     *
     * @return HasMany
     */
    public function company_regions(): HasMany
    {
        return $this->hasMany(CompanyRegion::class);
    }

    /**
     * subscriptions
     *
     * @return HasMany
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(SubscriptionActive::class);
    }

    /**
     * pending_downgrade
     *
     * @return HasOne
     */
    public function pending_downgrade(): HasOne
    {
        return $this->hasOne(SubscriptionDowngrade::class);
    }

    /**
     * Get the subscription change logs for a company.
     */

    public function subscription_change_logs()
    {
        return $this->hasMany(SubscriptionLog::class);
    }

    /**
     * Get the user associated with the company.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function oraganisationSize()
    {
        return $this->belongsTo(OrganisationSize::class, 'organisation_size_id');
    }

    public function oraganisationType()
    {
        return $this->belongsTo(OrganisationType::class, 'organisation_type_id');
    }

    /**
     * Define the index name in Elasticsearch.
     */
    public function searchableAs()
    {
        return 'suppliers_index';
    }


    /**
     * Define the OpenSearch mapping for this model.
     * 
     * @return array
     */
    public function searchableMapping()
    {
        return [
            'properties' => [
                'company_locations' => [
                    'type' => 'geo_point'
                ]
            ]
        ];
    }


    /**
     * Define the data that will be indexed.
     */
    public function toSearchableArray()
    {
        $orgSize = $this->oraganisationSize()->exists()
            ? $this->oraganisationSize?->range_from . '-' . $this->oraganisationSize?->range_to
            : null;

        $orgType = $this->oraganisationType()->exists()
            ? $this->oraganisationType?->name
            : null;

        $filters = array_filter(array_merge(
            $this->company_business_categories()->exists()
                ? array_merge(
            $this->company_business_categories()
                        ->with('level_2_category')
                        ->get()
                        ->pluck('level_2_category.name')
                        ->filter()
                        ->toArray(),

                    $this->company_business_categories()
                        ->with('level_3_category')
                        ->get()
                        ->pluck('level_3_category.name')
                        ->filter()
                        ->toArray()
                )
                : [],
            $this->company_countries()->with('country')->get()->pluck('country.code')->filter()->toArray(),
            $orgSize ? [$orgSize] : [],
            $orgType ? [$orgType] : []
        ));

        $array = [
            'id' => $this->id,
            'name' => optional($this->user)->name,
            'company' => $this->name,
            'description' => $this->description,
            'website_url' => $this->website_url,
            'phone' => $this->full_phone,
            'email' => $this->email,
            '                                                                                                                               ' => $this->year_founded,
            'created_at' => $this->created_at,
            // 'portfolio_size' => $this->company->oraganisationSize->size,
            'country_ids' => $this->company_countries()->exists()
                ? implode(', ', $this->company_countries()->get()->pluck('country_id')->toArray())
                : null,
            'region_ids' => $this->company_regions()->exists()
                ? implode(', ', $this->company_regions()->get()->pluck('region_id')->toArray())
                : null,
            'categories' => $this->company_business_categories()->exists()
                ? implode(', ', $this->company_business_categories()->with('level_3_category')->get()->pluck('level_3_category.name')->filter()->toArray())
                : null,
            'filters' => $filters,
        ];

        // Add company locations
        $companyLocations = [];
        if ($this->company_locations) {
            foreach ($this->company_locations as $location) {
                if ($location->latitude && $location->longitude) {
                    $companyLocations[] = [
                        'lat' => (float)$location->latitude,
                        'lon' => (float)$location->longitude
                    ];
                }
            }
        }

        $array['company_locations'] = $companyLocations;

        return $array;
    }

    /**
     * Get the company awards associated with the company.
     *
     * @return HasMany
     */

    public function company_awards(): HasMany
    {
        return $this->hasMany(CompanyAward::class);

    }

    /**
     * Get the company case studies associated with the company.
     *
     * @return HasMany
     */

    public function company_case_studies(): HasMany
    {
        return $this->hasMany(CaseStudy::class);
    }

    public function notifications()
    {
        return $this->hasMany(BusinessNotification::class, 'business_id');
    }

    public function conversations()
    {
        return $this->hasMany(BusinessConversation::class, 'business_id');
    }

    public function company_admins(): HasMany
    {
        return $this->hasMany(CompanyAdmin::class);
    }

    public function showComplianceSummaryTab(): bool
    {
        return $this->compliance_summary 
            || $this->company_iso_compliance->isNotEmpty()
            || $this->company_personnel_qualifications->isNotEmpty() 
            || $this->company_product_certifications->isNotEmpty();
    }

    public function showExcellenceTab(): bool
    {
        return $this->company_awards->isNotEmpty() || $this->company_case_studies->isNotEmpty();
    }

    public function showEndorseButton(): bool
    {
        if (auth()->check() && getCompanyId() && getCompanyId() !== $this->id) {
            return !CompanyEndorsement::where('endorsed_company', $this->id)
                ->where('endorser_user', auth()->user()->id)
                ->whereNull('remover_user')
                ->exists();
        }
        return false;
    }

    public function showRemoveEndorseButton(): bool
    {
        if (auth()->check() && getCompanyId() && getCompanyId() !== $this->id) {
            return CompanyEndorsement::where('endorsed_company', $this->id)
                ->where('endorser_user', auth()->user()->id)
                ->whereNull('remover_user')
                ->exists();
        }
        return false;
    }

    public function activeCompanyEndorsements(): HasMany
    {
        return $this->hasMany(CompanyEndorsement::class, 'endorsed_company')
                    ->where('status', 'active');
    }



}
