<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterOnProductTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // Remove the 'remove_listing' column if it exists
            if (Schema::hasColumn('products', 'remove_listing')) {
                $table->dropColumn('remove_listing');
            }
            // Remove the 'status' column if it exists
            if (Schema::hasColumn('products', 'status')) {
                $table->dropColumn('status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
    }
}
