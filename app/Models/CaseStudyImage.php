<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CaseStudyImage
 *
 * Represents an image associated with a case study.
 *
 * @package App\Models
 */
class CaseStudyImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'case_study_id',
        'image',
    ];

    /**
     * Get the case study that owns the image.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function caseStudy()
    {
        return $this->belongsTo(CaseStudy::class);
    }
}
