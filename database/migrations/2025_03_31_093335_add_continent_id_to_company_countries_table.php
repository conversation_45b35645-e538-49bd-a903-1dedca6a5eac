<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_countries', function (Blueprint $table) {
            $table->foreignId('company_continent_id')
                ->constrained('company_continents')
                ->cascadeOnDelete()
                ->after('country_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_countries', function (Blueprint $table) {
            //
        });
    }
};
