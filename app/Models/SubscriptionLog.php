<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SubscriptionLog extends Model
{
    protected $fillable = [
        'company_id',
        'previous_subscription_id',
        'new_subscription_id',
        'stripe_customer_id',
        'user_id',
        'change_type',
        'change_initiator',
        'change_date',
    ];

   
    public function company()
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
    public function previous_subscription()
    {
        return $this->hasOne(StripeSubscription::class, 'id', 'previous_subscription_id');
    }
    public function new_subscription()
    {
        return $this->hasOne(StripeSubscription::class, 'id', 'new_subscription_id');
    }
}
