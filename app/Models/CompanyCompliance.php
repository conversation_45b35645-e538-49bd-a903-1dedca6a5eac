<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyCompliance extends Model
{
    protected $fillable = [
        'company_id',
        'iso_compliance',
        'personnel_qualifications',
        'product_certifications',
        'compliance_summary',
    ];

    protected $casts = [
        'iso_compliance' => 'array',
        'personnel_qualifications' => 'array',
        'product_certifications' => 'array',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
