<?php

namespace App\Http\Controllers\Auth\Social;

use App\Http\Actions\SocialAuthAction;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Laravel\Socialite\Facades\Socialite;

class LinkedinAuthController extends Controller
{
    /**
     * Redirect the user to the LinkedIn authentication page.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function __invoke(): RedirectResponse
    {
        return Socialite::driver('linkedin-openid')->redirect();
    }

    /**
     * Handle the callback from LinkedIn.
     *
     * @param SocialAuthAction $action
     * @return \Illuminate\Http\RedirectResponse
     */
    public function callback(SocialAuthAction $action): RedirectResponse
    {
        $linkedin = Socialite::driver('linkedin-openid')->user();
        $user = $action->execute($linkedin, 'linkedin');

        return $user->isEmailVerified() && $user->isPhoneVerified()
            ? \Redirect::route('frontend.index')
            : \Redirect::route('frontend.index')
            ->with('modalToShow', config('settings.modal.user_basic_details'));
    }
}
