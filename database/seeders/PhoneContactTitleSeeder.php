<?php

namespace Database\Seeders;

use App\Models\PhoneContactTitle;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PhoneContactTitleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $titles = ['Phone contact', 'Customer Support', 'Technical Support', 'Product Equiries'];
        foreach($titles as $title){
             PhoneContactTitle::create([
                 'title' => $title
             ]);
        }
    }
}
