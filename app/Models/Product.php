<?php

namespace App\Models;

use App\Models\Company;
use App\Models\Country;
use App\Models\Category;
use App\Models\Feedback;
use App\Models\KeyFeature;
use Illuminate\Support\Str;
use App\Models\ProductRegion;
use Lara<PERSON>\Scout\Searchable;
use App\Models\ProductCountry;
use App\Models\ProductPricing;
use App\Models\ProductShipping;
use App\Models\AfterSaleSupport;
use App\Models\ProductAttribute;
use App\Models\ProductContinent;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Support\Facades\DB;
use App\Models\ProductCertification;
use Illuminate\Database\Eloquent\Model;
use App\Models\ProductStandardsCompliance;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;


/**
 * Class Product
 *
 * Represents a product in the system.
 *
 * @package App\Models
 *
 * @property int $id
 * @property string $name Name of the product
 * @property string|null $description Detailed description of the product
 * @property string|null $region_restrictions Regions where supply is restricted or special arrangements are needed
 * @property int $company_id Foreign key referring to the company that owns the product
 * @property string|null $additional_pricing_detail Additional pricing information for the product
 * @property string|null $additional_shiping_rate_detail Additional shipping rate details
 * @property string|null $compliance_summary Summary of product compliance with standards and regulations
 * @property string|null $listing_duration Determines whether the product is published immediately or scheduled
 * @property string $listing_status Status of the listing, either "Active" or "Inactive"
 * @property bool $remove_listing Whether the listing is removed (default: false)
 * @property \Illuminate\Support\Carbon|null $created_at Timestamp when the product was created
 * @property \Illuminate\Support\Carbon|null $updated_at Timestamp when the product was last updated
 */
class Product extends Model implements HasMedia
{
    use HasFactory, Searchable, InteractsWithMedia, SoftDeletes;

    /** @var integer Status identifier active product */
    public const STATUS_ACTIVE = 'Active';

    /** @var integer Status identifier inactive product */
    public const STATUS_INACTIVE = 'Inactive';


    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'region_restrictions',
        'company_id',
        'additional_pricing_detail',
        'additional_shiping_rate_detail',
        'compliance_summary',
        'listing_duration',
        'listing_status',
        'remove_listing',
        'listing_release_date',
        'listing_end_date',
        'uuid',
        'remove_listing',
        'type',
    ];

    protected $casts = [
        'listing_release_date' => 'datetime',
        'listing_end_date' => 'datetime',
    ];

    public static array $searchableFields = [
        'name',
        'description',
    ];


    /**
     * Automatically generate a UUID when creating a product.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->uuid) {
                // Use MySQL's UUID_SHORT() for generating the UUID
                $model->uuid = DB::select('SELECT UUID_SHORT() AS uuid')[0]->uuid;
            }
        });
    }
    /**
     * Boot the model and define event hooks for indexing in Elasticsearch.
     * 
     * This method ensures that a product is indexed in Elasticsearch only after 
     * it has at least one pricing entry. Additionally, it triggers reindexing 
     * whenever the product is updated.
     */
    protected static function booted()
    {
        /**
         * Handle the "created" event.
         * 
         * When a product is newly created, it should not be indexed immediately 
         * because its associated pricing records may not exist yet. This check 
         * ensures that the product is only indexed if at least one pricing entry 
         * is present.
         */
        // static::created(function ($product) {
        //     // ✅ Ensure product is indexed only if pricing exists
        //     if ($product->pricings()->exists()) {
        //         $product->searchable();
        //     }
        // });

        /**
         * Handle the "updated" event.
         * 
         * Whenever a product is updated, it should be reindexed to reflect any 
         * changes in its searchable attributes, including price updates.
         */
        // static::updated(function ($product) {
        //     // ✅ Always update Elasticsearch when product details change
        //     $product->searchable();
        // });

        /**
         * Handle the "deleted" event.
         * 
         * When a product is deleted, it should be removed from the Elasticsearch 
         * index to ensure that it is no longer searchable.
         */
        // static::deleted(function ($product) {
        //     // ✅ Remove the product from Elasticsearch when deleted
        //     $product->unsearchable();
        // });
    }

    // Register media collections
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('main_image')->singleFile(); // Only one main image
        $this->addMediaCollection('thumb_images'); // Multiple thumbnails allowed
        $this->addMediaCollection('documents'); // pdf documents allowed
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     * Define the index name in Elasticsearch.
     */
    public function searchableAs()
    {
        return 'products_index';
    }

    /**
     * Define the OpenSearch mapping for this model.
     * 
     * @return array
     */
    public function searchableMapping()
    {
        return [
            'properties' => [
                'company_locations' => [
                    'type' => 'geo_point'
                ]
            ]
        ];
    }

    /**
     * Define the data that will be indexed.
     */
    public function toSearchableArray()
    {
        $allFeedback = Feedback::where('product_id', $this->id)
            ->whereNull('parent_id')
            ->get();
        $positiveFeedback = $allFeedback->where('type', 'like')->count();
        $negativeFeedback = $allFeedback->where('type', 'dislike')->count();
        if ($positiveFeedback + $negativeFeedback == 0) {
            $rating = 0;
        } elseif ($positiveFeedback == 0) {
            $rating = 0;
        } else {
            $rating = ($positiveFeedback / ($positiveFeedback + $negativeFeedback)) * 100;
        }
        $company = $this->company;
        $countryIds = $this->product_countries()->pluck('country_id')->toArray();
        // $countries = $this->product_countries()->with('country')->get()->pluck('country.country_name')->filter()->toArray();
        
        $regionIds = $this->product_regions()->pluck('region_id')->toArray();
        // $regions = $this->product_regions()->with('region')->get()->pluck('region.region')->filter()->toArray();
        
        $oraganisationSize = $company->oraganisationSize;
        $range  = $oraganisationSize->range_from . '-' . $oraganisationSize->range_to;
        
        $oraganisationType = $company->oraganisationType;
        $oraganisationType  = $oraganisationType->name;
        
        $filters = [];

        $categoryNames = $this->categories()->exists()
            ? $this->categories()->pluck('name')->toArray()
            : [];

        foreach ($categoryNames as $categoryName) {
            $filters[] = $categoryName;
        }

        if (!empty($range)) {
            $filters[] = $range;
        }

        if (!empty($oraganisationType)) {
            $filters[] = $oraganisationType;
        }
        if($this->listing_status == self::STATUS_INACTIVE) {
            $product_status = 'Inactive';
        } else if($this->listing_status == self::STATUS_ACTIVE && $this->listing_duration == 'Scheduled') {
            $product_status = 'Active-Scheduled';
        } else if($this->listing_status == self::STATUS_ACTIVE && $this->listing_duration == 'Published') {
            $product_status = 'Active-Published';
        } else {
            $product_status = 'Inactive';
        }
        // $filters = trim($categoryNames . ($range ? ', ' . $range : '') . ($oraganisationType ? ', ' . $oraganisationType : ''));
        $array = [
            'id'            => $this->id,
            'uuid'          => $this->uuid,
            'name'          => $this->name,
            'description'   => $this->description,
            'company_id'       => $company?->id,
            'company'       => $company?->name,
            'type'          => $this->type,
            'created_at'    => $this->created_at,
            'listing_duration'    => $this->listing_duration,
            'listing_status'    => $this->listing_status,
            'product_status'    => $product_status,
            'listing_release_date'    => $this->listing_release_date,
            'listing_end_date'    => $this->listing_end_date,
            'price'         => $this->pricings()->exists() ? (float) $this->pricings()->min('price') : null,
            'country_ids' => $countryIds,
            'region_ids' => $regionIds,
            'categories'    => $this->categories()->exists()
                ? implode(', ', $this->categories()->pluck('name')->toArray())
                : null,
            'filters'       => $filters,
            'feedback'      => $rating,
        ];
        
        // Add company locations
        $companyLocations = [];
        if ($this->company && $this->company->company_locations) {
            foreach ($this->company->company_locations as $location) {
                if ($location->latitude && $location->longitude) {
                    $companyLocations[] = [
                        'lat' => (float)$location->latitude,
                        'lon' => (float)$location->longitude
                    ];
                }
            }
        }

        $array['company_locations'] = $companyLocations;
        
        return $array;
    }

    /**
     * Get the company that owns the product.
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     * 
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the categories associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'category_product');
    }

    /**
     * Get the key features associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function keyFeatures()
    {
        return $this->belongsToMany(KeyFeature::class, 'key_feature_product');
    }

    /**
     * Get the after-sale supports associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function afterSaleSupports()
    {
        return $this->belongsToMany(AfterSaleSupport::class, 'after_sale_support_product');
    }

    /**
     * Get the regions (countries) where the product is supplied.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function regions()
    {
        return $this->belongsToMany(Country::class, 'product_region', 'product_id', 'country_id');
    }

    /**
     * Get the pricing details associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function pricings()
    {
        return $this->hasMany(ProductPricing::class);
    }

    /**
     * Get the shipping details associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function shippings()
    {
        return $this->hasMany(ProductShipping::class);
    }

    /**
     * Get the attributes associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function attributes()
    {
        return $this->hasMany(ProductAttribute::class);
    }

    /**
     * Get the certifications associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function certifications()
    {
        return $this->hasMany(ProductCertification::class);
    }

    /**
     * Get the standards compliance records associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function standardsCompliances()
    {
        return $this->hasMany(ProductStandardsCompliance::class);
    }

    /**
     * Get the qualifications associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function qualifications()
    {
        return $this->hasMany(ProductQualification::class);
    }

    public function savedProducts()
    {
        return $this->belongsToMany(Product::class, 'save_products', 'user_id', 'product_id');
    }

    /**
     * Get the feedback associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function feedback()
    {
        return $this->hasMany(Feedback::class);
    }

    /**
     * ProductContinent
     *
     * @return HasMany
     */
    public function product_continents(): HasMany
    {
        return $this->hasMany(ProductContinent::class);
    }

    /**
     * ProductCountry
     *
     * @return HasMany
     */
    public function product_countries(): HasMany
    {
        return $this->hasMany(ProductCountry::class);
    }

    /**
     * ProductCountry
     *
     * @return HasMany
     */
    public function product_regions(): HasMany
    {
        return $this->hasMany(ProductRegion::class);
    }
}
