<?php

namespace App\Services;

use App\Models\CompanyRegistration;
use App\Models\GeoCountry;
use App\Repositories\CompanyRepository;
use App\Repositories\SupplierRequestRepository as Repository;
use App\Traits\DBService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SupplierRequestService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

    /*
     * User Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;


    /**
     * companyRepository
     *
     * @var mixed
     */
    public $companyRepository;
    protected $mailboxLayerService;


    /**
     * __construct
     *
     * @param  mixed $repository
     * @param  mixed $companyRepository
     * @return void
     */
    public function __construct(
        Repository $repository,
        CompanyRepository $companyRepository,
        MailboxLayerService $mailboxLayerService)
    {
        $this->repository = $repository;
        $this->companyRepository = $companyRepository;
        $this->mailboxLayerService = $mailboxLayerService;
    }

    /**
     * Get current loggedin user
     *
     * @return App\Models\User
     */
    public function current()
    {
        return Auth::user();
    }

    public function getListForAdmin(string $status = null)
    {
        $conditions = [];
        if (!is_null($status)) {
            $conditions['status'] = $status;
        }

        return $this->repository->getByMixedCondition($conditions);
    }


    /**
     * Get list of all the statuses
     *
     * @return array
     */
    public function getStatuses()
    {
        return $this->repository->statuses();
    }

    public function createCompany(CompanyRegistration $registrationRequest)
    {
        DB::beginTransaction();

        try {
            // Prepare the data
            $companyData = $this->prepareCompanyData($registrationRequest);
            $businessData = $this->prepareBusinessData($registrationRequest);
            $locationData = $this->prepareLocationData($registrationRequest);

            // Create the company
            $company = $this->companyRepository->create($companyData);
           
            if (!$company) {
                throw new \Exception('Failed to create company.');
            }

            // Update registration status
            $registrationRequest->registrationStatus->update(['company_id' => $company->id]);

            // Create business sector and location
            $this->createBusinessSector($company, $businessData);
            $this->createLocation($company, $locationData);
            $company->searchable();

            DB::commit();
            return $company;
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Error creating company: ' . $e->getMessage(), [
                'registrationRequest' => $registrationRequest->toArray(),
            ]);

            throw $e;
        }
    }


    /**
     * Prepare company data for creation.
     */
    private function prepareCompanyData(CompanyRegistration $registrationRequest): array
    {
        $domainVerifyStatus = $this->mailboxLayerService->validateEmail($registrationRequest->email);

        return [
            'user_id' => $registrationRequest->user_id,
            'registered_location' => $registrationRequest->registered_location,
            'organisation_type_id' => $registrationRequest->organisation_type_id,
            'organisation_size_id' => $registrationRequest->organisation_size_id,
            'name' => $registrationRequest->company_name,
            'email' => $registrationRequest->email,
            'phone' => $registrationRequest->phone,
            'phone_prefix' => $registrationRequest->phone_prefix,
            'website_url' => $registrationRequest->website_url,
            'show_workplace_verfification' => !$domainVerifyStatus['role'] && !$domainVerifyStatus['disposable'] && !$domainVerifyStatus['free']
        ];
    }

    /**
     * Prepare business data for creation.
     */
    private function prepareBusinessData(CompanyRegistration $registrationRequest): array
    {
        return [
            'level_2_category_id' => $registrationRequest->business_sector_id,
            //'level_3_category_id' => $registrationRequest->level_3_category_id,
        ];
    }

    /**
     * Prepare location data for creation.
     */
    private function prepareLocationData(CompanyRegistration $registrationRequest): array
    {
         //Fetching country_id by code
         $pointWKT = "POINT({$registrationRequest->latitude} {$registrationRequest->longitude})";
         //Fetching region_id by country_id and point
         $region = \DB::table('geo_regions as r')
         ->join('geo_countries as c', 'r.country_id', '=', 'c.id')
         ->select('r.id as region_id')
         ->where('c.id', $registrationRequest->country_id)
         ->whereRaw("ST_Contains(r.shape, ST_GeomFromText(?, 4326))", [$pointWKT])
         ->first();
        return [
            'location_type_id' => $registrationRequest->location_type_id ?? 1,
            'geo_country_id' => $registrationRequest->country_id,
            'geo_region_id' => $region ? $region->region_id : rand(1, 5),
            'address_line_1' => $registrationRequest->address_line_1,
            'address_line_2' => $registrationRequest->address_line_2,
            'city' => explode(',', $registrationRequest->city)[0],
            'postcode' => $registrationRequest->postcode,
            'latitude' => $registrationRequest->latitude,
            'longitude' => $registrationRequest->longitude,
            'is_main' => 1,
        ];
    }

    /**
     * Create the business sector.
     */
    private function createBusinessSector($company, array $businessData)
    {
        try {
            $company->businessSectors()->sync([$businessData['level_2_category_id']]);
            // $company->businessSectors()->update([
            //     'level_3_category_id' => $businessData['level_3_category_id'
            //     ]
            // ]);

        } catch (\Exception $e) {
            Log::error('Error creating business sector: ' . $e->getMessage());
            throw new \Exception('Failed to create business sector. ' . $e->getMessage());
        }
    }



    /**
     * Create the location.
     */
    private function createLocation($company, array $locationData)
    {
        $location = $company->locations()->create($locationData);

        if (!$location) {
            throw new \Exception('Failed to create location.');
        }
    }
}
