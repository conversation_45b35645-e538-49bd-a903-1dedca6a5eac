<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Str;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'name' => User::ROLE_ADMIN,
                'slug' => Str::slug(User::ROLE_ADMIN),
                'type' => 'admin',
            ],
            [
                'name' => User::ROLE_SUPPLIER,
                'slug' => Str::slug(User::ROLE_SUPPLIER),
                'type' => 'supplier',
            ],
            [
                'name' => User::ROLE_USER,
                'slug' => Str::slug(User::ROLE_USER),
                'type' => 'visitor',
            ],
        ];

        foreach ($data as $single) {
            Role::create($single);
        }
    }
}
