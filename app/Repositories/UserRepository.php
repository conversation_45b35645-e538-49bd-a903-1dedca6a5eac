<?php

namespace App\Repositories;

use App\Models\User;

/**
 * Class UserRepository
 *
 * Handles data operations for the User model.
 */
class UserRepository extends BaseRepository
{
    /**
     * The User model instance.
     *
     * @var \App\Models\User
     */
    public $model;

    /**
     * UserRepository constructor.
     *
     * @param \App\Models\User $user
     */
    public function __construct(User $user)
    {
        $this->model = $user;
    }

    /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {        
       return [
            'Active' => $this->model::STATUS_ACTIVE,
            'In-active' => $this->model::STATUS_INACTIVE,
            'Blocked' => $this->model::STATUS_BLOCKED,
            'Suspended' => $this->model::STATUS_SUSPENDED,
       ];
    }

    /**
     * Retrieve all records from the model by role
     *
     * @param string $role
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByRole(string $role)
    {
        return $this->model->role($role)->get();
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByMixedCondition(array $params)
    {
        $query = $this->model->select();
        
        /** filter by status */
        if (isset($params['status'])) {
            $query->status($params['status']);
        }

        /** filter by role */
        if (isset($params['role'])) {
            $query->role($params['role']);
        }

        if (isset($params['except_user'])) {
            if (is_array($params['except_user'])) {
                $query->whereNotIn('id', $params['except_user']);
            } else {
                $query->where('id', '!=', $params['except_user']);
            }
        }

        return $query->get();
    }
}
