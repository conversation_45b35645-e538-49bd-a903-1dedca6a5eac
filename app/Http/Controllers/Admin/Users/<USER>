<?php

namespace App\Http\Controllers\Admin\Users;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateUserRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Services\UserService;
use Illuminate\Support\Facades\Gate;

class BaseUserController extends Controller
{
    /**
     * User service
     *
     * @var UserService
     */
    public $service;

    /**
     * Role of the user
     *
     * string $role
     */
    public $role;

    /**
     * Page title based on the role of users
     *
     * string $title
     */
    public $title;

    /**
     * Route prefix based on the role of users
     *
     * @var $routePrefix
     */
    public $routePrefix;

    /**
     * Create a new controller instance.
     *
     * @param UserService $service
     * @param string $role
     * @param string $routePrefix
     * @param string $title
     * @return void
     */
    public function __construct(
        UserService $service, 
        string $role, 
        string $routePrefix, 
        string $title
    )
    {
        $this->service = $service;
        $this->role = $role;
        $this->routePrefix = $routePrefix;
        $this->title = $title;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        $user = $this->service->current();
        $status = $request->status ?? null;
        $role = $this->role;
        $title = $this->title;
        $routePrefix = $this->routePrefix;

        return view('admin.users.index', compact('user', 'status', 'role', 'title', 'routePrefix'));
    }

    protected function list($request)
    {
        $status = $request->status;

        
        $role = $this->role;
        $user = $this->service->current();

        $entrys = $this->service->getListForAdmin($status, $role);
        $statuses = $this->service->getStatuses();

        return DataTables::of($entrys)
            ->addColumn('created_at', function ($entry) {
                return $entry->created_at;
            })
            ->addColumn('role', function ($entry) {
                return \Str::ucfirst($entry->role_name);
            })
            ->addColumn('status', function ($entry) use ($user) {
                $status = '';
                if ($entry->status == User::STATUS_INACTIVE) {
                    $status = '<div style="background-color:#d35757;"  class="custom-rounded">In-Active</div>';
                } elseif ($entry->status == User::STATUS_ACTIVE) {
                    $status = '<div style="background-color:#358b00;" class="custom-rounded">Active</div>';
                } elseif ($entry->status == User::STATUS_BLOCKED) {
                    $status = '<div style="background-color:#b59600;" class="custom-rounded">Blocked</div>';
                } elseif ($entry->status == User::STATUS_SUSPENDED) {
                    $status = '<div style="background-color:#ff0404;" class="custom-rounded">Suspended</div>';
                }

                return $status;
            })
            ->addColumn('action', function ($entry) use ($user, $statuses) {                    
                $action = '';
                if ($user->can('manage users') || $user->can('view users')) {
                    $action .= '<a href="' . route('admin.users.show', $entry) . '" class="btn btn-primary">View</a> ';
                }

                if ($user->can('manage users')) {
                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    Action
                                </button>
                                <ul class="dropdown-menu" style="">';


                    $action .= '<li class="dropdown-item">';
                    $action .= '<a href="' . route($this->routePrefix . 'edit', $entry) . '" class="btn btn-primary"><i class="fa fa-edit">&nbsp;Edit</i></a> ';
                    $action .= '</li>';

                    foreach ($statuses as $name => $value) {
                        $disabled = ($entry->status == $value) ? 'disabled' : '';

                        $action .= '<li class="dropdown-item">';
                        $action .= '    <button class="btn btn-sm btn-primary change-status-btn ' . $disabled . '" data-id="' . $entry->id . '" ' . $disabled . ' data-statusname="' . $name . '" data-status="' . $value . '">' . $name . '</button>';
                        $action .= '</li>';
                    }
                    $action .= '</ul>';
                }
                return $action;
            })
            ->addColumn('phone', function ($entry) {
                return $entry->full_phone;
            })
            ->addColumn('country', function ($entry) {
                return $entry->country->name??'N/A';
            })
            ->rawColumns(['status', 'action'])
        ->toJson();
    }

    public function parseShow(User $user)
    {
        $role = $this->role;
        $title = $this->title;
        $routePrefix = $this->routePrefix;
        $userListLink = route($routePrefix . 'index', [
            'status' => $user->status,
        ]);        

        return view('admin.users.show', compact('user', 'userListLink', 'role', 'title', 'routePrefix'));
    }


    public function parseEdit(User $user)
    {
        if (!Gate::allows('manage users')) {
            abort(403);
        }

        $role = $this->role;
        $title = $this->title;
        $routePrefix = $this->routePrefix;
        $userListLink = route($routePrefix . 'index', [
            'status' => $user->status,
        ]);

        return view('admin.users.edit', compact('user', 'userListLink', 'role', 'title', 'routePrefix'));
    }


    public function parseUpdate(UpdateUserRequest $request, User $user)
    {
        $data = $request->all();
        $this->service->update($user->id, $data);

        return redirect()->route($this->routePrefix . 'index')
            ->with('success', 'User updated successfully.');
    }


    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;        

        $user = $this->service->find($id);

        if (!$user) {
            return response()->json(['message' => 'No user Found']);
        }

        $this->service->update($id, ['status' => $status]);
            
        return response()->json(['message' => 'Status changed successfully']);
    }
}
