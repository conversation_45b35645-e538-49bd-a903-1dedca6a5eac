<?php

namespace App\Services\Location;

use App\Repositories\CountryRepository as Repository;
use App\Traits\DBService;
use Illuminate\Database\Eloquent\Collection;

class CountryService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

    /*
     * Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get phone prefixes from countries
     *
     * @return array
     */
    public function getPhonePrefixes()
    {
        return $this->repository->getPlucked("phone_prefix", "phone_prefix");
    }
}
