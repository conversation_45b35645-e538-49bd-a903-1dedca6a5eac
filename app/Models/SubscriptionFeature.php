<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class SubscriptionFeature
 * 
 * Represents the features assigned to a subscription.
 *
 * @package App\Models
 */
class SubscriptionFeature extends Model
{
    use HasFactory;

    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = 0;

    protected $fillable = [
        'stripe_subscription_id',
        'feature_id',
        'enabled',
        'limit',
        'updated_by',
    ];

    public static function getStatusLabelAndStyle($status)
    {
        $statuses = [
            self::STATUS_ENABLED => [
                'label' => 'Enabled',
                'style' => 'background-color:#28a745; color:#fff; text-align:center; border-radius:15px; padding:5px 10px; display:inline-block;'
            ],
            self::STATUS_DISABLED => [
                'label' => 'Disabled',
                'style' => 'background-color:#dc3545; color:#fff; text-align:center; border-radius:15px; padding:5px 10px; display:inline-block;'
            ],
        ];


        return $statuses[$status] ?? ['label' => 'Unknown', 'style' => 'background-color:#6c757d;'];
    }

    /**
     * Get the subscription that owns the feature.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(StripeSubscription::class, 'stripe_subscription_id');
    }

    /**
     * Get the plan feature associated with the subscription feature.
     */
    public function feature(): BelongsTo
    {
        return $this->belongsTo(PlanFeature::class, 'feature_id');
    }



    /**
     * Get the user associated with the subscription feature.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
