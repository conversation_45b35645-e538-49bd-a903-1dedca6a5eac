<?php

namespace App\Http\Controllers\Admin\Supplier;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Product;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Services\ProductService;

class BaseProductController extends Controller
{
    /**
     * ProductService service
     *
     * @var ProductService
     */
    public $service;

    /**
     * Role of the user
     *
     * string $role
     */
    public $role;

    /**
     * Page title based on the role of users
     *
     * string $title
     */
    public $title;

    /**
     * Route prefix based on the role of users
     *
     * @var $routePrefix
     */
    public $routePrefix;

    /**
     * Create a new controller instance.
     *
     * @param ProductService $service
     * @param string $role
     * @param string $routePrefix
     * @param string $title
     * @return void
     */
    public function __construct(
        ProductService $service,
        string $role,
        string $routePrefix,
        string $title
    ) {
        $this->service = $service;
        $this->role = $role;
        $this->routePrefix = $routePrefix;
        $this->title = $title;
    }

    public function index(Request $request, $type)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        $user = $this->service->current();
        $status = $request->status ?? null;
        $role = $this->role;
        $title = ucfirst($type);
        $routePrefix = $this->routePrefix;

        $view = $type == 'product' ? 'admin.products.index' : 'admin.services.index';

        return view($view, compact('user', 'status', 'role', 'title', 'routePrefix', 'type'));
    }

    protected function list($request)
    {
        $status = $request->status;
        $user = $this->service->current();
        $type = $request->type ?? 'product';
        $entrys = $this->service->getListForAdmin($status, $type);

        return DataTables::of($entrys)

            ->addColumn('Product Title', function ($entry) {
                return $entry?->name;
            })
            ->addColumn('company', function ($entry) {
                return '<a href="' . route('admin.suppliers.index', ['company' => $entry->company?->id]) . '">' . e($entry->company?->name) . '</a>'; 
            })
            ->addColumn('type', function ($entry) {
                return $entry->type;
            })
            ->addColumn('categories', function ($entry) {
                return $entry->categories->pluck('name')->implode(', ');
            })
            ->addColumn('status', function ($entry) {
                $status = '';
                if ($entry->listing_status == Product::STATUS_ACTIVE) {
                    $status = '<div style="background-color:#358b00;" class="custom-rounded">Active</div>';
                } elseif ($entry->listing_status == Product::STATUS_INACTIVE) {
                    $status = '<div style="background-color:#ff0404;" class="custom-rounded">Inactive</div>';
                }
                return $status;
            })
            ->addColumn('action', function ($entry) use ($user) {
                $action = '';
                if ($user->can('manage products') || $user->can('view products')) {
                    $action .= '<a href="' . route('admin.products.show', $entry) . '" class="btn btn-primary">View</a> ';
                }

                if ($user->can('manage products')) {
                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    Action
                                </button>
                                <ul class="dropdown-menu" style="">';


                    $action .= '<li class="dropdown-item">';
                    $action .= '    <button class="btn btn-sm btn-warning change-status-btn" 
                                                            data-id="' . $entry->id . '" 
                                                            data-statusname="Active" 
                                                            data-status="' . Product::STATUS_ACTIVE . '" '
                        . ($entry->status == Product::STATUS_ACTIVE ? 'disabled' : '') . '>Active</button>';
                    $action .= '</li>';


                    $action .= '<li class="dropdown-item">';
                    $action .= '    <button class="btn btn-sm btn-danger change-status-btn" 
                                                        data-id="' . $entry->id . '" 
                                                        data-statusname="Inactive" 
                                                        data-status="' . Product::STATUS_INACTIVE . '" '
                        . ($entry->status == Product::STATUS_INACTIVE ? 'disabled' : '') . '>Inactive</button>';
                    $action .= '</li>';



                    $action .= '</ul>';
                }
                return $action;
            })
            ->addColumn('created_at', function ($entry) {
                return $entry->created_at;
            })
            ->rawColumns(['status', 'action','company'])
            ->toJson();
    }

    public function parseShow(Product $product)
    {
        $title = $this->title;
        $routePrefix = $this->routePrefix;
        $productListLink = route($routePrefix . 'index', ['type' => $product->type]);

        $view = $product->type == 'product' ? 'admin.products.show' : 'admin.services.show';

        return view($view, compact('product', 'productListLink', 'title', 'routePrefix'));
    }



    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;
        $product = $this->service->find($id);

        if (!$product) {
            return response()->json(['message' => 'No Product Found']);
        }

        $this->service->update($id, ['listing_status' => $status]);

        return response()->json(['message' => 'Status changed successfully']);
    }
}
