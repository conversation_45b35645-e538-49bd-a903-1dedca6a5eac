<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyAssessmentReason
 *
 * @property int $id
 * @property int $assessment_id
 * @property int $section_id
 * @property int $answered_by
 * @property string $answer
 */
class CompanyAssessmentReason extends Model
{
    protected $fillable = [
        'assessment_id',
        'section_id',
        'answered_by',
        'answer',
    ];

    public function assessment()
    {
        return $this->belongsTo(CompanyAssessment::class);
    }

    public function section()
    {
        return $this->belongsTo(AssessmentSection::class);
    }

    public function answeredBy()
    {
        return $this->belongsTo(User::class, 'answered_by');
    }
}
