<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_companies', function (Blueprint $table) {
            // Drop the foreign key if it exists
            $table->dropForeign(['company_id']);
            $table->string('company_name')->nullable()->after('company_id');
            // Modify company_id to be nullable (DO NOT add it again)
            $table->bigInteger('company_id')->unsigned()->nullable()->change();
            // Re-add the foreign key
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_companies', function (Blueprint $table) {
            //
        });
    }
};
