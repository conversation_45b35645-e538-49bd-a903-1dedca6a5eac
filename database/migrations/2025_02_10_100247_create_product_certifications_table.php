<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('product_certifications', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('product_id'); // Foreign key to products table
            $table->string('certificate_title'); // Name of the certification
            $table->string('certification_body'); // Issuing organization
            $table->string('certification_number')->nullable(); // Certification number
            $table->text('notes')->nullable(); // Additional notes
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_certifications');
    }
};
