<?php

namespace App\Http\Controllers\Admin\Subscription;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SubscriptionService;
class SubscriptionController extends BaseSubscriptionController
{
    /**
     * Create a new controller instance.
     *  
     * @param SubscriptionService $service
     * @return void
     */
    public function __construct(SubscriptionService $service)
    {
        $routePrefix = 'admin.subscriptions.';
        $title = 'Subscription';
        
        parent::__construct($service, $routePrefix, $title);
    }
}
