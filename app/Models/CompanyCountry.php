<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyCountry extends Model
{
    public $timestamps = false;
    protected $fillable = ['company_id', 'country_id', 'company_continent_id'];

    public function country()
    {
        return $this->belongsTo(GeoCountry::class);
    }

    public function company_country_regions()
    {
        return $this->hasMany(CompanyRegion::class, 'company_country_id');
    }
}
