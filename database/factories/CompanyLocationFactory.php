<?php

namespace Database\Factories;

use App\Models\CompanyLocation;
use App\Models\Company;
use App\Models\LocationType;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyLocation>
 */
class CompanyLocationFactory extends Factory
{
    protected $model = CompanyLocation::class;

    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'location_type_id' => LocationType::factory(),
            'country_id' => Country::factory(),
            'address_line_1' => $this->faker->streetAddress,
            'address_line_2' => $this->faker->optional()->secondaryAddress,
            'city' => $this->faker->city,
            'postcode' => $this->faker->postcode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'email' => $this->faker->optional()->companyEmail,
            'phone' => $this->faker->optional()->phoneNumber,
            'phone_prefix' => $this->faker->optional()->numerify('+###'),
        ];
    }
}
