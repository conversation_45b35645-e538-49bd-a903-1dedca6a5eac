<?php

namespace App\Repositories;

use App\Models\AssessmentReason;

/**
 * Class AssessmentReasonRepository
 *
 * Handles data operations for the AssessmentReason model.
 */
class AssessmentReasonRepository extends BaseRepository
{
    /**
     * The AssessmentReason model instance.
     *
     * @var \App\Models\AssessmentReason
     */
    protected $model;

    /**
     * AssessmentReasonRepository constructor.
     *
     * @param \App\Models\AssessmentReason $assessmentReason
     * @return void
     */
    public function __construct(AssessmentReason $assessmentReason)
    {
        $this->model = $assessmentReason;
    }
}
