<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ProductShipping extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['product_id', 'destination', 'shipping_method', 'estimated_delivery_time', 'shipping_rate', 'notes'];

    /**
     * Automatically generate a UUID when creating a product.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->uuid) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}

