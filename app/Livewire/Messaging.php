<?php

namespace App\Livewire;

use App\Events\MessageSent;
use Livewire\Component;
use App\Models\Message;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Broadcast;


class Messaging extends Component
{

    public $search = '';
    public $contacts;
    public $selectedContact;
    public $messages = [];
    public $newMessage = '';

    protected $listeners = ['handleIncomingMessage'];

    public function mount()
    {
        $this->getContacts();
    }

    public function selectContact($contactId)
    {
        $this->selectedContact = User::find($contactId);
        $this->loadMessages();
    }

    public function loadMessages()
    {
        $this->messages = Message::where(function ($q) {
            $q->where('sender_id', auth()->id())
                ->where('receiver_id', $this->selectedContact->id);
        })->orWhere(function ($q) {
            $q->where('sender_id', $this->selectedContact->id)
                ->where('receiver_id', auth()->id());
        })->orderBy('created_at')->get()->toArray();
    }

    public function sendMessage()
    {

       $this->validate([
            'newMessage' => 'required|string',
            'selectedContact.id' => 'exists:users,id',
        ]);

        if (!$this->newMessage || !$this->selectedContact)
            return;

        $message = Message::create([
            'sender_id' => auth()->id(),
            'receiver_id' => $this->selectedContact->id,
            'body' => $this->newMessage,
        ]);

        broadcast(new MessageSent($message))->toOthers();

        $this->messages[] = $message->toArray();
        $this->newMessage = '';

        $this->dispatch('messageSentJs', $this->selectedContact->id);
  
    
    }

    public function handleIncomingMessage($message)
    {
        
        if ($this->selectedContact && $message['sender_id'] == $this->selectedContact->id) {
            $this->messages[] = $message;
        }
        
        $this->dispatch('scrollToBottom');
    }
    


    public function updated($property)
    {
        if ($property === 'search') {
            $this->getContacts();
        }
    }

    public function getContacts()
    {
        $this->contacts = User::where('id', '!=', auth()->id())
            ->whereDoesntHave('roles', function ($query) {
                $query->where('name', User::ROLE_ADMIN);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                        ->orWhere('last_name', 'like', '%' . $this->search . '%');
                });
            })
            ->get();
    }

    public function render()
    {
        return view('livewire.messaging');
    }
}
