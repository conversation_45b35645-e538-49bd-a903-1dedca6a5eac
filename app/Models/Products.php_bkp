<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Products extends Model
{
    use HasFactory;
    protected $table = 'products';
    protected $guarded = [
        'id'
    ];

    public function productCategory()
    {
        return $this->belongsTo(ProductCategories::class, 'product_categories_id', 'id'); // Assuming 'stripe_id' is the primary key of the 'plans' table
    }

    public function category()
    {
        return $this->belongsTo(ProductCategories::class, 'product_categories_id', 'id'); // Assuming 'stripe_id' is the primary key of the 'plans' table
    }

    public function productImage()
    {
        return $this->belongsTo(ProductImages::class, 'id', 'product_id'); // Assuming 'stripe_id' is the primary key of the 'plans' table
    }

    public function getRouteKeyName()
    {
        // return 'product_name';
        return 'id';
    }
}
