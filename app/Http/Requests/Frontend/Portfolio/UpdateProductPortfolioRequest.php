<?php

namespace App\Http\Requests\Frontend\Portfolio;

use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateProductPortfolioRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Adjust authorization logic as needed
    }

    public function rules()
    {

        $this->merge([
            'product_category' => json_decode($this->input('product_category'), true),
            'key_feature' => json_decode($this->input('key_feature'), true),
            'after_sales_support' => json_decode($this->input('after_sales_support'), true),
            'product_attributes' => json_decode($this->input('product_attributes'), true),
            'product_certificates' => json_decode($this->input('product_certificates'), true),
            'product_compliance' => json_decode($this->input('product_compliance'), true),
            'product_prices' => json_decode($this->input('product_prices'), true),
            'shipping_details' => json_decode($this->input('shipping_details'), true),
            
        ]);
        return [
            // Product Basic Details
            'product_category' => 'required|array',
            'key_feature' => 'required|array|max:6',
            'after_sales_support' => 'required|array',
            'product_description' => 'required|string|max:500',
            // 'regions' => 'required|string',
            'supply_notes' => 'nullable|string|max:500',
            'name' => 'required|string',
            'listing_duration' => 'nullable|string',
            'useCompanySupplyLocation' => 'nullable|string',
            'continents' => 'nullable|array',
            'countries' => 'nullable|array',
            'regions' => 'nullable|array',
            'listing_release_date' => [
                'required_if:listing_duration,Schedule',
                function ($attribute, $value, $fail) {
                    if ($this->input('listing_duration') === 'Schedule') {
                        if (!strtotime($value)) {
                            $fail('The listing release date must be a valid date.');
                        } elseif (strtotime($value) <= strtotime('today')) {
                            $fail('The listing release date must be greater than today.');
                        } elseif ($this->listing_end_date && strtotime($value) >= strtotime($this->listing_end_date)) {
                            $fail('The listing release date must be before the listing end date.');
                        }
                    }
                },
            ],

            'listing_end_date' => [
                'required_if:listing_duration,Schedule',
                function ($attribute, $value, $fail) {
                    if ($this->input('listing_duration') === 'Schedule') {
                        if (!strtotime($value)) {
                            $fail('The listing end date must be a valid date.');
                        } elseif ($this->listing_release_date && strtotime($value) <= strtotime($this->listing_release_date)) {
                            $fail('The listing end date must be after the listing release date.');
                        }
                    }
                },
            ],

            // Product Pricing (Each entry must have all required fields)
            'product_prices' => 'nullable|array|min:1',
            'product_prices.*.price' => 'sometimes|required|numeric|min:0',
            'product_prices.*.min_order' => 'sometimes|required|integer|min:1',
            'product_prices.*.part_number' => 'sometimes|required|string|max:255',
            'product_prices.*.payment_terms' => 'sometimes|required|string|max:255',

            // Shipping Details (Each entry must have all required fields)
            'shipping_details' => 'nullable|array|min:1',
            'shipping_details.*.destination' => 'sometimes|required|string|max:255',
            'shipping_details.*.shipping_method' => 'sometimes|required|string|max:255',
            'shipping_details.*.shipping_time' => 'sometimes|required|string|max:100',
            'shipping_details.*.shipping_rate' => 'sometimes|required|numeric|min:0',
            'shipping_details.*.notes' => 'sometimes|nullable|string|max:1000',

            // Product Attributes (Each entry must have all required fields)
            'product_attributes' => 'nullable|array|min:1',
            'product_attributes.*.name' => 'sometimes|required|string|max:255',
            'product_attributes.*.characteristic' => 'sometimes|required|string|max:1000',

            // Product Certificates (Each entry must have all required fields)
            'product_certificates' => 'nullable|array|min:1',
            'product_certificates.*.title' => 'sometimes|required|string|max:255',
            'product_certificates.*.body' => 'sometimes|required|string|max:255',
            'product_certificates.*.number' => 'sometimes|required|string|max:255',
            'product_certificates.*.notes' => 'nullable|string|max:500',

            // Product Compliance (Each entry must have all required fields)
            'product_compliance' => 'nullable|array|min:1',
            'product_compliance.*.standard' => 'sometimes|required|string|max:255',
            'product_compliance.*.details' => 'nullable|string|max:1000',

            // Product Images
            // 'main_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB Max
            // 'thumb_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120', // Each image max 5MB
            // 'thumb_images' => 'required|array|max:24', // Max 24 images

            // Product Images
            'main_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB Max // Optional on update
            'selected_pre_main_image' => 'nullable|array|min:1',
            'selected_pre_main_image.*' => 'string', // IDs or filenames

            'thumb_images' => 'nullable|array|max:24',
            'thumb_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120', // Each image max 5MB

            'selected_pre_thumb_image' => 'nullable|array',
            'selected_pre_thumb_image.*' => 'string',
        ];
    }

    public function messages()
    {
        return [
            // Custom Messages for Basic Details
            'product_category.required' => 'The product category is required.',
            'product_category.array' => 'The product category is required.',
            'key_feature.required' => 'At least one key feature is required.',
            'key_feature.array' => 'Key features is required.',
            'key_feature.max' => 'You can only provide up to 6 key features.',
            'after_sales_support.required' => 'After sales support information is required.',
            'after_sales_support.array' => 'After sales support is required.',
            'product_description.required' => 'The product description is required.',
            'product_description.string' => 'The product description must be a string.',
            'product_description.max' => 'The product description must not exceed 500 characters.',
            'regions.required' => 'The regions field is required.',
            'regions.string' => 'The regions field must be a string.',
            'supply_notes.string' => 'Supply notes must be a string.',
            'supply_notes.max' => 'Supply notes must not exceed 500 characters.',
            'name.required' => 'The product name is required.',
            'name.string' => 'The product name must be a string.',
            'listing_release_date.required_if' => 'The listing release date is required when listing duration is Schedule.',
            'listing_end_date.required_if' => 'The listing end date is required when listing duration is Schedule.',
            'listing_release_date.date' => 'The listing release date must be a valid date.',
            'listing_end_date.date' => 'The listing end date must be a valid date.',


            // Custom Messages for Product Pricing (Ensuring all fields are present)
            'product_prices.required' => 'At least one price entry is required.',
            'product_prices.array' => 'Product prices is required.',
            'product_prices.min' => 'At least one product price entry is required.',
            'product_prices.*.price.required' => 'Each price entry must have a price value.',
            'product_prices.*.price.numeric' => 'The price must be a valid number.',
            'product_prices.*.price.min' => 'The price must be at least 0.',
            'product_prices.*.min_order.required' => 'Each price entry must have a minimum order quantity.',
            'product_prices.*.min_order.integer' => 'The minimum order must be a valid integer.',
            'product_prices.*.min_order.min' => 'The minimum order must be at least 1.',
            'product_prices.*.part_number.required' => 'Each price entry must have an ID/Part number.',
            'product_prices.*.part_number.string' => 'The part number must be a valid string.',
            'product_prices.*.part_number.max' => 'The part number must not exceed 255 characters.',
            'product_prices.*.payment_terms.required' => 'Each price entry must have payment terms.',
            'product_prices.*.payment_terms.string' => 'Payment terms must be a valid string.',
            'product_prices.*.payment_terms.max' => 'Payment terms must not exceed 255 characters.',

            // Custom Messages for Shipping Details
            'shipping_details.required' => 'Shipping details are required.',
            'shipping_details.array' => 'Shipping details are required.',
            'shipping_details.min' => 'At least one shipping detail is required.',
            'shipping_details.*.destination.required' => 'Each shipping entry must have a destination.',
            'shipping_details.*.destination.string' => 'The destination must be a valid string.',
            'shipping_details.*.destination.max' => 'The destination name must not exceed 255 characters.',
            'shipping_details.*.shipping_method.required' => 'Each shipping entry must have a shipping method.',
            'shipping_details.*.shipping_method.string' => 'The shipping method must be a valid string.',
            'shipping_details.*.shipping_method.max' => 'The shipping method must not exceed 255 characters.',
            'shipping_details.*.shipping_time.required' => 'Each shipping entry must have an estimated shipping time.',
            'shipping_details.*.shipping_time.max' => 'The shipping time description must not exceed 100 characters.',
            'shipping_details.*.shipping_rate.required' => 'Each shipping entry must have a shipping rate.',
            'shipping_details.*.shipping_rate.numeric' => 'The shipping rate must be a valid number.',
            'shipping_details.*.shipping_rate.min' => 'The shipping rate must be at least 0.',

            // Custom Messages for Attributes
            'product_attributes.required' => 'At least one product attribute is required.',
            'product_attributes.array' => 'Product attributes is required.',
            'product_attributes.min' => 'At least one attribute is required.',
            'product_attributes.*.name.required' => 'Each attribute must have a name.',
            'product_attributes.*.name.string' => 'Attribute name must be a string.',
            'product_attributes.*.name.max' => 'Attribute name must not exceed 255 characters.',
            'product_attributes.*.characteristic.required' => 'Each attribute must have a characteristic.',
            'product_attributes.*.characteristic.string' => 'Attribute characteristic must be a string.',
            'product_attributes.*.characteristic.max' => 'Attribute characteristic must not exceed 1000 characters.',

            // Custom Messages for Certificates
            'product_certificates.required' => 'At least one product certificate is required.',
            'product_certificates.array' => 'Product certificates is required.',
            'product_certificates.min' => 'At least one certificate is required.',
            'product_certificates.*.title.required' => 'Each certificate must have a title.',
            'product_certificates.*.title.max' => 'The certificate title must not exceed 255 characters.',
            'product_certificates.*.body.required' => 'Each certificate must have a certification body.',
            'product_certificates.*.body.max' => 'The certification body name must not exceed 255 characters.',
            'product_certificates.*.number.required' => 'Each certificate must have a certification number.',
            'product_certificates.*.number.max' => 'The certification number must not exceed 255 characters.',
            'product_certificates.*.notes.string' => 'Certificate notes must be a string.',
            'product_certificates.*.notes.max' => 'Certificate notes must not exceed 500 characters.',

            // Custom Messages for Compliance
            'product_compliance.required' => 'At least one product compliance standard is required.',
            'product_compliance.array' => 'Product compliance standards is required.',
            'product_compliance.min' => 'At least one compliance standard is required.',
            'product_compliance.*.standard.required' => 'Each compliance must have a standard name.',
            'product_compliance.*.standard.string' => 'Compliance standard name must be a string.',
            'product_compliance.*.standard.max' => 'The standard name must not exceed 255 characters.',
            'product_compliance.*.details.string' => 'Compliance details must be a string.',
            'product_compliance.*.details.max' => 'Compliance details must not exceed 1000 characters.',

            // Custom Messages for Images
            'main_image.required' => 'The main image is required.',
            'main_image.image' => 'The main image must be a valid image file.',
            'main_image.mimes' => 'The main image must be in JPEG, PNG, JPG, or GIF format.',
            'main_image.max' => 'The main image must not exceed 5MB.',

            'thumb_images.required' => 'The thumbnail image is required.',
            'thumb_images.array' => 'Thumbnail images is required of images.',
            'thumb_images.max' => 'You can upload a maximum of 24 thumbnail images.',
            'thumb_images.*.image' => 'Each thumbnail must be a valid image file.',
            'thumb_images.*.mimes' => 'Thumbnails must be in JPEG, PNG, JPG, or GIF format.',
            'thumb_images.*.max' => 'Each thumbnail must not exceed 5MB.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'errors' => $validator->errors()->toArray()
        ], 422));
    }
}
