<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProfileSetting extends Model
{

    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'user_id',
        'about_me',
        'linkedin_link',
        'facebook_link',
        'instagram_link',
        'preferred_search_location',
        'public_profile_email',
        'public_profile_number'
    ];


    /**
     * Get the user associated with the profile.
    */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    
}
