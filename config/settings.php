<?php

return [

    'otp_expire_time' => env('OTP_EXPIRE_TIME', 10), // in minutes

    'modal' => [
        'dashboard_category'        => 'categoriesModal',
        'signup'                    => 'signupModal',
        'login'                     => 'loginModal',
        'email_verification'        => 'emailVerificationModal',
        'user_basic_details'        => 'userBasicDetailsModal',
        'occupation_details'        => 'occupationDetailsModal',
        'phone_number_prompt'       => 'phoneNumberPromptModal',
        'phone_verification'        => 'phoneVerificationModal',
        'forgot_password_one'       => 'forgotPassModalOne',
        'forgot_password_two'       => 'forgotPassModalTwo',
        'forgot_password_three'     => 'forgotPassModalThree',
        'user_profile_created'      => 'userProfileCreatedModal',
        'my_profile'      => 'profileModal',
        'email_update' => 'emailUpdateModal',
        'verify_workplace' => 'verifyWorkplace',
        'confirmation_modal' => 'confirmationModal',
        'contact_honley_modal' => 'contactHonleyModal',
        'mobile_update' => 'mobileUpdateModal',
        'change_password' => 'changePasswordModal',
        'password_update' => 'passwordUpdateModal',
        'close_account_reason' => 'closeAccountReasonModal',
        'close_account' => 'closeAccountModal',
        'closed_account_confirmation' => 'closedAccountConfirmation',
        'company-registration-step1' => 'companyRegistrationStep1',
        'company-registration-step2' => 'companyRegistrationStep2',
        'company-registration-step3' => 'companyRegistrationStep3',
        'company-registration-success' => 'companyRegistrationSuccess',
        'company-loctaion' => 'companyLocation',
        'delete-location' => 'deleteLocationModal',
        'company-final-step' => 'companyFinalStep',
        'company-update-email' => 'companyUpdateEmail',
        'supply-extent' => 'supplyExtentModal',
        'image-editor' => 'imageEditorModal',
        'image-previewer' => 'imagePreviewerModal',
        'case-study' => 'caseStudyModal',
        'view-case-study' => 'viewCaseStudyModal',
    ],

    'google_api_key' => env('GOOGLE_API_KEY', ''),
];
