<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Sluggable\HasSlug;
use <PERSON><PERSON>\Sluggable\SlugOptions;

class Country extends Model
{
    use HasFactory, HasSlug;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'countries';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'slug',
        'code',
        'currency_code',
        'fips_code',
        'iso_numeric',
        'continent_name',
        'continent',
        'phone_prefix',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the full country name, formatted as "Name (Code)".
     *
     * @return string
     */
    public function getFullNameAttribute(): string
    {
        return $this->name . ' (' . $this->code . ')';
    }

    /**
     * Get the phone prefix, formatted with a "+" sign.
     *
     * @return string
     */
    public function getPhonePrefixFormattedAttribute(): string
    {
        return '+' . $this->phone_prefix;
    }

    public function areas(): HasMany
    {
        return $this->hasMany(Area::class);
    }

    public function organisation_type_local(): HasOne
    {
        return $this->hasOne(OrganisationTypeLocal::class, 'iso_code', 'code');
    }

}
