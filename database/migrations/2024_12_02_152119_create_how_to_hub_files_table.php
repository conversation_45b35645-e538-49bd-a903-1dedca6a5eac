<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('how_to_hub_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('how_to_hub_id')->constrained('how_to_hubs')->cascadeOnDelete(); // References 'how_to_hubs.id'
            $table->text('file');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('how_to_hub_files');
    }
};
