<?php

namespace App\Http\Controllers\Admin\ConnectHub;

use App\Http\Controllers\Controller;
use App\Models\NotificationAssignee;
use App\Models\NotificationUserCategory;
use Illuminate\Http\Request;
use App\Models\User;

class AssigneeController extends Controller
{
    public function index()
    {
        $categories = NotificationUserCategory::with('assignees')->get();
        $admins = User::role('admin')->get();

        return view('admin.connecthub.assignee.create', compact('categories', 'admins'));
    }



    public function store(Request $request)
    {
        if (empty($request->assignees) || !is_array($request->assignees)) {
            NotificationAssignee::truncate();
        } else {
            foreach ($request->assignees as $categoryId => $userIds) {
                NotificationAssignee::where('category_id', $categoryId)->delete();

                foreach ($userIds as $userId) {
                    NotificationAssignee::create([
                        'category_id' => $categoryId,
                        'user_id' => $userId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
        return back()->with('success', 'Assignees updated successfully.');
    }

}
