<?php

namespace App\Repositories;

use App\Models\CompanyAssessmentDocument;

/**
 * Class CompanyAssessmentDocumentRepository
 *
 * Handles data operations for the CompanyAssessmentDocument model.
 */
class CompanyAssessmentDocumentRepository extends BaseRepository
{
    /**
     * The CompanyAssessmentDocument model instance.
     *
     * @var \App\Models\CompanyAssessmentDocument
     */
    protected $model;

    /**
     * CompanyAssessmentDocumentRepository constructor.
     *
     * @param \App\Models\CompanyAssessmentDocument $companyAssessmentDocument
     * @return void
     */
    public function __construct(CompanyAssessmentDocument $companyAssessmentDocument)
    {
        $this->model = $companyAssessmentDocument;
    }
}
