<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentFailed extends Notification implements ShouldQueue
{
    use Queueable;

    protected $type;

    /**
     * Create a new notification instance.
     */
    public function __construct($type)
    {
        $this->type = $type; // Either "initial_payment_failed" or "renewal_payment_failed"
    }

    /**
     * Get the notification’s delivery channels.
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        try {
            return (new MailMessage)
                ->mailer(config('mail.default')) // Use primary mailer
                ->subject('Action Required: Update Payment Information for ' . $notifiable->company->name)
                ->greeting("Dear {$notifiable->name},")
                ->line("We attempted to process the payment for **{$notifiable->company->name}**'s account services subscription at Honley.online, but unfortunately, the payment failed. To ensure uninterrupted access to your services, please update your payment information promptly.")
                ->line("### To update your payment method:")
                ->line("1. Log in to your company account dashboard on our platform.")
                ->line("2. Navigate to the **Subscription & Payment** section.")
                ->line("3. Follow the prompts to update your payment details.")
                ->line("If the payment issue is not resolved within 14 days, your account will be downgraded from **{$notifiable->subscription_plan_name}** to the free tier, which may result in limited access to certain features.")
                ->line("While we strive to retain your data, we cannot guarantee that all information will be preserved after the downgrade.")
                ->line("If you have any questions or need assistance, please contact our support team.")
                ->salutation("Best regards,
    Honley Support Team");
        } catch (Exception $e) {
            Log::error('Mail Sending Failed: ' . $e->getMessage()); // Log the error

            return (new MailMessage)
                ->mailer(env('MAIL_FAILOVER', 'smtp')) // Use fallback mailer
                ->subject('Action Required: Update Payment Information for ' . $notifiable->company->name)
                ->greeting("Dear {$notifiable->name},")
                ->line("We attempted to process the payment for **{$notifiable->company->name}**'s account services subscription at Honley.online, but unfortunately, the payment failed. To ensure uninterrupted access to your services, please update your payment information promptly.")
                ->line("### To update your payment method:")
                ->line("1. Log in to your company account dashboard on our platform.")
                ->line("2. Navigate to the **Subscription & Payment** section.")
                ->line("3. Follow the prompts to update your payment details.")
                ->line("If the payment issue is not resolved within 14 days, your account will be downgraded from **{$notifiable->subscription_plan_name}** to the free tier, which may result in limited access to certain features.")
                ->line("While we strive to retain your data, we cannot guarantee that all information will be preserved after the downgrade.")
                ->line("If you have any questions or need assistance, please contact our support team.")
                ->salutation("Best regards,  
    Honley Support Team");
        }
    }


    /**
     * Store the notification in the database.
     */
    public function toArray($notifiable)
    {
        return [
            'message' => $this->type === 'initial_payment_failed' ?
                'Your initial subscription payment has failed. Please update your payment details.' :
                'Your subscription renewal payment has failed. Please update your payment details.',
            'type' => $this->type,
        ];
    }
}
