<?php

namespace App\Repositories;

use App\Models\Category;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class CategoryRepository
 *
 * Handles data operations for the Category model.
 */
class CategoryRepository extends BaseRepository
{
    /**
     * The Category model instance.
     *
     * @var \App\Models\Category
     */
    protected $model;

    /**
     * CategoryRepository constructor.
     *
     * @param \App\Models\Category $category
     */
    public function __construct(Category $category)
    {
        $this->model = $category;
    }
    
     /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {
       return [
            'Active' => $this->model::STATUS_ACTIVE,
            'In-active' => $this->model::STATUS_INACTIVE,
            'Suspended' => $this->model::STATUS_SUSPENDED,
       ];
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return Collection
     */
    public function getByMixedCondition(array $params) : Collection
    {
        return $this->queryByMixedCondition($params)->get();
    }

    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return Builder
     */
    public function queryByMixedCondition(array $params) : Builder
    {
        $query = $this->model->select();

        /** loading relationships */
        if (isset($params['with'])) {
            $query->with($params['with']);
        }

        /** filter by level */
        if (isset($params['level'])) {
            $query->level($params['level']);
        }

        /** filter by type */
        if (isset($params['type'])) {
            $query->type($params['type']);
        }

        /** filter by type */
        if (isset($params['status'])) {
            $query->status($params['status']);
        }
        
        /** filter by parent_id */
        if (isset($params['parent_id'])) {
            $query->parent($params['parent_id']);
        }

        /** filter by parent_id */
        if (isset($params['not_in'])) {
            $query->whereNotIn('id', $params['not_in']);
        }

        if (!empty($params['order_by'])) {
            $query->orderBy($params['order_by'][0], $params['order_by'][1]);
        }

        return $query;
    }
}
