<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyBusinessCategory extends Model
{

    use HasFactory;

    protected $fillable = [
        'company_id',
        'level_2_category_id',
        'level_3_category_id'
    ];

    /**
     * Get company associated the category.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function level_2_category()
    {
        return $this->belongsTo(Category::class, 'level_2_category_id');
    }

    public function level_3_category()
    {
        return $this->belongsTo(Category::class, 'level_3_category_id');
    }
      
}
