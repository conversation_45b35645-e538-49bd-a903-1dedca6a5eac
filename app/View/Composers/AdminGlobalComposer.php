<?php

namespace App\View\Composers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\View\View;
use App\Services\UserService;

class AdminGlobalComposer
{
    public function compose(View $view)
    {
        $userService = app(UserService::class);
        $user = $userService->current();
        $userPermissions = $userService->getPermissions($user);
        // dd($userPermissions);

        $adminMenu = config('adminmenu');
        $currentRouteName = Route::currentRouteName();

        $menu = $this->filterMenuByPermissions($adminMenu, $userPermissions, $user->is_admin);
        $menu = array_filter($menu);

        $view->with('user', $user);
        $view->with('isAdmin', $user->is_admin);

        $view->with('adminMenu', $menu);
        $view->with('currentRouteName', $currentRouteName);
    }

    private function filterMenuByPermissions(array $menu, array $userPermissions): array
    {
        if (in_array('super user', $userPermissions)) {
            return $menu;
        }

        return array_map(function ($menuItem) use ($userPermissions) {
            if (isset($menuItem['permissions'])) {
                $hasPermission = !empty(array_intersect($menuItem['permissions'], $userPermissions));
                if (!$hasPermission) {
                    return null;
                }
            }

            if (isset($menuItem['submenu']) && is_array($menuItem['submenu'])) {
                $menuItem['submenu'] = $this->filterMenuByPermissions($menuItem['submenu'], $userPermissions);

                $menuItem['submenu'] = array_filter($menuItem['submenu']);
            }

            return $menuItem;
        }, $menu);
    }
}
