<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StorePageRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Update based on your authorization logic if necessary
    }

    public function rules()
    {
        return [
            'title' => 'required|string|max:100',
            'content' => 'required',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:255',
            'meta_keywords' => 'nullable|string',
            'status' => 'required|boolean',
        ];
    }
}
