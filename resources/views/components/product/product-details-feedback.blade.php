<div class="tab-pane" id="review-tab-pane" role="tabpanel" aria-labelledby="review-tab" tabindex="0">
    <div class="rating-reviews overview product-management-page  ">
        <div class="filter-box pro-mgmt-top-filter">
            <h6 class="m-0 mb-2 text-center mb-sm-0">Feedback &amp; Questions</h6>
            <div class="table-btn-group">
                <div class="left-btn custom-rating">
                    <ul class="like-btn">
                        <li><i class="bi bi-hand-thumbs-up-fill"></i>(<count id="fbLike">0</count>)</li>
                        <li><i class="bi bi-hand-thumbs-down"></i>(<count id="fbDislike">0</count>)</li>
                        <li><span>Q</span> (<count id="fbQuestion">0</count>)</li>
                    </ul>
                </div>
                <div class="right-btns filter-custom">
                    <div class="dropdown border-right d-inline-block">
                        <button class="btn dropdown-toggle tbtn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="color-black bold">Sort by</span>
                        </button>
                        <ul class="dropdown-menu">
                            @foreach($feedbackSortOptions as $key => $label)
                                <li>
                                    <a href="#" class="dropdown-item feedback-sort-link" data-sort="{{ $key }}">{{ $label }}</a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="dropdown d-inline-block">
                        <button class="btn dropdown-toggle tbtn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="color-black bold">Filter by</span>
                        </button>
                        <ul class="dropdown-menu px-3">
                            @foreach($feedbackFilterOptions as $key => $label)
                                <li>
                                    <label for="feedback-filter-{{ $key }}" class="mb-0">
                                        <input
                                            type="checkbox"
                                            class="form-check-input feedback-filter-checkbox me-2"
                                            id="feedback-filter-{{ $key }}"
                                            value="{{ $key }}"
                                        >
                                        @if ($key == 'positive')
                                            <i class="bi bi-hand-thumbs-up-fill"></i> 
                                        @elseif ($key == 'negative')
                                            <i class="bi bi-hand-thumbs-down-fill"></i> 
                                        @elseif ($key == 'question')
                                            <span class="like-btn-q-icon">Q</span> 
                                        @endif
                                        {{ $label }}
                                    </label>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="users">
            <div class="users-comments">
                <div class="user-info mb-3">
                    @if (auth()->check())
                    <h6 class="h6 m-0 px-2">{{ auth()->user()->username }}</h6>
                    @endif
                </div>
                <textarea id="feedback_comment" class="w-100 write-box p-2 feedback_comment" @if (!auth()->check()) data-bs-toggle="modal" data-bs-target="#{{ config('settings.modal.login') }}" @endif rows="2" cols="100" name="feedback_comment" placeholder="Write Feedback"></textarea>
                <span class="characters-left like-btn-group-count-container d-none">1000 characters left</span>
                <div class="like-btn-group like-btn-group-container d-none">
                    <a href="#" type="button" class="users-comments-cancel px-2 border-black rounded-10">Cancel</a>
                    <div class="like-btn-group-inner flex-between">
                        <span class="color-black bold font-15">Post as: </span>
                        @if (auth()->check())
                            <ul class="like-btn no-border">
                                <li class="btn-bordered border-green question-type submit-question" data-value="like"><i class="bi bi-hand-thumbs-up-fill"></i></li>
                                <li class="btn-bordered border-green question-type submit-question" data-value="dislike"><i class="bi bi-hand-thumbs-down"></i></li>
                            </ul>
                            <a href="javascript:void(0)" type="button" class="users-comments-btn remove-before btn-bordered border-green submit-question" data-value="question">Question</a>
                            @else
                            <ul class="like-btn no-border">
                                <li data-bs-toggle="modal" data-bs-target="#{{ config('settings.modal.login') }}" class="btn-bordered border-green question-type selected" data-value="like"><i class="bi bi-hand-thumbs-up-fill"></i></li>
                                <li data-bs-toggle="modal" data-bs-target="#{{ config('settings.modal.login') }}" class="btn-bordered border-green question-type" data-value="dislike"><i class="bi bi-hand-thumbs-down"></i></li>
                            </ul>
                            <a href="#" type="button" data-bs-toggle="modal" data-bs-target="#{{ config('settings.modal.login') }}" class="users-comments-btn remove-before btn-bordered border-green">Question</a>
                        @endif
                    </div>

                </div>
            </div>
        </div>
        <div id="feedback-list">
        </div>
    </div>
</div>

<style>
    .like-btn-q-icon {
        font-size: 21px;
        font-weight: 600;
        color: #038799;
    }
</style>