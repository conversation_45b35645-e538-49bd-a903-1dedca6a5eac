<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AdminNotificationMailable extends Mailable
{
    use Queueable, SerializesModels;

    public string $messageText;

    public function __construct(string $messageText, string $subject)
    {
        $this->subject = $subject;
        $this->messageText = $messageText;
    }

    public function build()
    {
        return $this->subject($this->subject)
            ->markdown('emails.admin-notification')
            ->with([
                    'message' => $this->messageText,

                ]);
    }

}
