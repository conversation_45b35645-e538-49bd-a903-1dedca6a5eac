<?php

namespace App\Http\Requests\Frontend;

use App\Rules\ValidPhoneNumberCode;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class LocationCreateUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'location_id' => ['nullable'],
            'city' => ['required', 'string'],
            'country_code' => ['required', 'string'],
            'location_type' => ['required','string'],
            'latitude' => ['required', 'string'],
            'longitude' => ['required', 'string'],
            'postal_code' => ['required', 'string', 'max:12'],
            'address_line_1' => ['required', 'string', 'max:100'],
            'address_line_2' => ['nullable', 'string', 'max:100'],
            'country' => ['required', 'string'],
            'phone_prefix' => ['nullable', 'string'],
            'number' => [
                'nullable',
                new ValidPhoneNumberCode(request()->phone_prefix)
            ],
            'location_email' => [
                'nullable',
                'string',
                // 'lowercase',
                'email',
                'max:50',
                'regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix'
            ],
            'main_location' => 'nullable'
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function failedValidation(Validator $validator): void
    {
        throw new ValidationException(
            $validator,
            \Redirect::back()
                ->withInput()
                ->withErrors($validator)
                ->with('modalToShow', $this->currentModalToDisplay())
        );
    }

    /**
     * Get the ID of the modal that should be shown after validation failure.
     *
     * @return string
     */
    public function currentModalToDisplay(): string
    {
        return config('settings.modal.company-loctaion');
    }

    /**
     * Get the ID of the modal that should be shown after the request is successful.
     *
     * @return string
     */
    public function nextModalToDisplay(): string
    {
        return config('settings.modal.confirmation_modal');
    }
}
