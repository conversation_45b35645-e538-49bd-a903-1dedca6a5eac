<?php

namespace App\Http\Controllers\Admin\QuotationRequests;

use App\Http\Controllers\Controller;
use App\Models\QuotationRequest;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Services\QuotationService;
use Illuminate\Support\Facades\Gate;

class BaseQuotationRequestController extends Controller
{
    /**
     * Quotation service
     *
     * @var QuotationService
     */
    public $service;

    /**
     * Role of the user
     *
     * string $role
     */
    public $role;

    /**
     * Page title based on the role of users
     *
     * string $title
     */
    public $title;

    /**
     * Route prefix based on the role of users
     *
     * @var $routePrefix
     */
    public $routePrefix;

    /**
     * Create a new controller instance.
     *
     * @param QuotationService $service
     * @param string $role
     * @param string $routePrefix
     * @param string $title
     * @return void
     */
    public function __construct(
        QuotationService $service,
        string $role,
        string $routePrefix,
        string $title
    ) {
        $this->service = $service;
        $this->role = $role;
        $this->routePrefix = $routePrefix;
        $this->title = $title;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }

        $user = $this->service->current();
        $status = $request->status ?? null;
        $role = $this->role;
        $title = $this->title;
        $routePrefix = $this->routePrefix;

        return view('admin.quotations.index', compact('user', 'status', 'role', 'title', 'routePrefix'));
    }

    protected function list($request)
    {
        $status = $request->status;


        $role = $this->role;
        $user = $this->service->current();

        $entrys = $this->service->getListForAdmin($status, $role);
        $statuses = $this->service->getStatuses();

        return DataTables::of($entrys)
            ->addColumn('username', function ($entry) {
                return $entry->user->username;
            })
            ->addColumn('from', function ($entry) {
                return $entry->user->name;
            })
            ->addColumn('company', function ($entry) {
                return $entry->user->company?->name ?? 'N/A';
            })
            ->addColumn('role', function ($entry) {
                return 'N/A';
            })
            ->addColumn('created_at', function ($entry) {
                return $entry->created_at->format('d-m-Y H:i:s');
            })
            ->addColumn('updated_at', function ($entry) {
                return $entry->updated_at->format('d-m-Y H:i:s');
            })
            ->addColumn('status', function ($entry) {
                $status = '';
                if ($entry->status == QuotationRequest::STATUS_PENDING) {
                    $status = '<div style="background-color:#d35757;"  class="custom-rounded">Pending</div>';
                } elseif ($entry->status == QuotationRequest::STATUS_APPROVED) {
                    $status = '<div style="background-color:#358b00;" class="custom-rounded">Approved</div>';
                } elseif ($entry->status == QuotationRequest::STATUS_REJECTED) {
                    $status = '<div style="background-color:#b59600;" class="custom-rounded">Rejected</div>';
                }

                return $status;
            })
            ->addColumn('action', function ($entry) use ($user, $statuses) {
                $action = '';
                if ($user->can('manage quotations') || $user->can('view quotations')) {
                    $action .= '<a href="' . route('admin.quotations.show', $entry) . '" class="btn btn-primary">View</a> ';
                }

                if ($user->can('manage quotations')) {
                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    Action
                                </button>
                                <ul class="dropdown-menu" style="">';

                    foreach ($statuses as $name => $value) {
                        $disabled = ($entry->status == $value) ? 'disabled' : '';

                        $action .= '<li class="dropdown-item">';
                        $action .= '    <button class="btn btn-sm btn-primary change-status-btn ' . $disabled . '" data-id="' . $entry->id . '" ' . $disabled . ' data-statusname="' . $name . '" data-status="' . $value . '">' . $name . '</button>';
                        $action .= '</li>';
                    }
                    $action .= '</ul>';
                }
                return $action;
            })
            ->addColumn('phone', function ($entry) {
                return $entry->full_phone;
            })
            ->addColumn('country', function ($entry) {
                return $entry->country->name ?? 'N/A';
            })
            ->rawColumns(['status', 'action'])
            ->toJson();
    }

    public function parseShow(QuotationRequest $quotation)
    {
        $role = $this->role;
        $title = $this->title;
        $routePrefix = $this->routePrefix;
        $quotationListLink = route($routePrefix . 'index', [
            'status' => $quotation->status,
        ]);

        return view('admin.quotations.show', compact('quotation', 'quotationListLink', 'role', 'title', 'routePrefix'));
    }





    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;

        $quotation = $this->service->find($id);

        if (!$quotation) {
            return response()->json(['message' => 'No quotation Found']);
        }

        $this->service->update($id, ['status' => $status]);

        return response()->json(['message' => 'Status changed successfully']);
    }
}
