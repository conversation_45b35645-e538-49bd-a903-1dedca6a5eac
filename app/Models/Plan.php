<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'billing_cycle',
        'price',
        'duration',
        'has_trial',
        'trial_period_days',
        'status',
    ];

    /**
     * Get the features associated with the plan.
     */
    public function features(): HasMany
    {
        return $this->hasMany(PlanFeature::class);
    }
}
