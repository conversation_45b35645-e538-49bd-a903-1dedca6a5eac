<?php

namespace App\Services;

use App\Http\Actions\CompanyRegistrationAction;
use App\Models\OTP;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;

class OtpService
{
    /**
     * Generate a new OTP for a user
     *
     * @return array
     */
    public function code(): array
    {
        $duration = (int) config('settings.otp_expire_time');
        return [
            'code' => rand(100000, 999999),
            'expiry' => Carbon::now()->addMinutes($duration),
        ];
    }

    /**
     * Save a new OTP for a user
     *
     * @param User $user
     * @param string $type
     * @return OTP
     */
    public function generate(User $user, string $type): OTP
    {
        $otp = $this->code();
        $value = $type == 'email' ? $user->email : $user->phone;
        return OTP::updateOrCreate(
            [
                'user_id' => $user->id,
                

            ],
            [
                 $type => $value,
                'code' => $otp['code'],
                'expires_at' => $otp['expiry'],
            ]
        );
    }

    /**
     * Verify the OTP for a user
     *
     * @param string $identifier
     * @param string $value
     * @param string $code
     * @param string|null $currentModalToDisplay
     * @return RedirectResponse
     */
    public function verifyForCompanyEmail(
        string $identifier,
        string $value,
        string $code,
        ?string $currentModalToDisplay = null
    ): RedirectResponse {

            $otp = OTP::firstWhere(['user_id' => auth()->user()->id, $identifier => $value, 'code' => $code]);

            if (!$otp) {
                return \Redirect::back()->withErrors(['otp' => 'Invalid OTP.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }
            if (Carbon::now()->isAfter($otp->expires_at)) {
                return \Redirect::back()->withErrors(['otp' => 'OTP has expired.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }
            $otp->delete();

            Session::forget('verifyType');
            $action = new CompanyRegistrationAction;
            $action->storeCompanyRegistrationStatus();

            return redirect()->back()
                ->with(['modalToShow' => config('settings.modal.company-registration-success')]);
            
    }

    /**
     * Verify the OTP for a user
     *
     * @param string $identifier
     * @param string $value
     * @param string $code
     * @param string|null $currentModalToDisplay
     * @return RedirectResponse
     */
    public function verifyForOauthEmailUpdate(
        string $identifier,
        string $value,
        string $code,
        ?string $currentModalToDisplay = null
    ): RedirectResponse {

            $otp = OTP::firstWhere(['user_id' => auth()->user()->id, $identifier => $value, 'code' => $code]);

            if (!$otp) {
                return \Redirect::back()->withErrors(['otp' => 'Invalid OTP.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }
            if (Carbon::now()->isAfter($otp->expires_at)) {
                return \Redirect::back()->withErrors(['otp' => 'OTP has expired.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }
            
            $otp->delete();

            Session::forget('verifyType');

            return redirect()->back()
                ->with(['modalToShow' => config('settings.modal.forgot_password_three')]);
                
    }

    public function verifyForUserRegistration(
        string $identifier,
        string $value,
        string $code,
        ?string $currentModalToDisplay = null)
    {
        $otp = OTP::firstWhere([$identifier => $value, 'code' => $code]);

        if (!$otp) {
            return \Redirect::back()->withErrors(['otp' => 'Invalid OTP.'])
                ->with('modalToShow', $currentModalToDisplay);
        }
        if (Carbon::now()->isAfter($otp->expires_at)) {
            return \Redirect::back()->withErrors(['otp' => 'OTP has expired.'])
                ->with('modalToShow', $currentModalToDisplay);
        }
        $user = User::where($identifier, $value)->firstOrFail();

        \DB::transaction(function () use ($identifier, $user, $otp) {
            if ($identifier === 'email') {
                $user->markEmailAsVerified();
            }
            if ($identifier === 'phone') {
                $user->markPhoneAsVerified();
            }
            $otp->delete();
        });
        return \Redirect::back()
        ->with(['modalToShow' => config('settings.modal.user_basic_details')]);

    }

    /**
     * Verify the OTP for a user
     *
     * @param string $identifier
     * @param string $value
     * @param string $code
     * @param string|null $currentModalToDisplay
     * @return RedirectResponse
     */
    public function verifyForEmailUpdate(
        string $identifier,
        string $value,
        string $code,
        ?string $currentModalToDisplay = null
    ): RedirectResponse {

            $user = auth()->user();
            $contactHistory = $user->user_contact_history()->orderBy('id', 'desc')->first();
            $otp = OTP::firstWhere(['user_id' => auth()->user()->id, $identifier => $value, 'code' => $code]);
            if (!$otp) {
                $contactHistory->status = 'failed';
                $contactHistory->failure_reason = 'Invalid OTP';
                $contactHistory->save();
                return \Redirect::back()->withErrors(['otp' => 'Invalid OTP.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }
            if (Carbon::now()->isAfter($otp->expires_at)) {
                $contactHistory->status = 'failed';
                $contactHistory->failure_reason = 'OTP has expired';
                $contactHistory->save();
                return \Redirect::back()->withErrors(['otp' => 'OTP has expired.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }

            Session::forget('verifyType');
            \DB::transaction(function () use ($identifier, $user, $otp, $value) {

                $otp->delete();
            
                if ($identifier === 'email') {
                    $user->markEmailAsVerified();
                }
                if ($identifier === 'phone') {
                    $user->markPhoneAsVerified();
                }

                $user->update([
                    'email' => $value
                ]);

                $contactHistory = $user->user_contact_history()->orderBy('id', 'desc')->first();
                $contactHistory->status = 'successful';
                $contactHistory->processed_at = now();
                $contactHistory->save();
            });

            \Auth::logout();

            return redirect()->route('frontend.index')
            ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Your email has been successfully updated. If you did not request this change, please secure your account immediately.']);
        
    }

    /**
     * Verify the OTP for a user
     *
     * @param string $identifier
     * @param string $value
     * @param string $code
     * @param string|null $currentModalToDisplay
     * @return RedirectResponse
     */
    public function verifyForCompanyEmailUpdate(
        string $identifier,
        string $value,
        string $code,
        ?string $currentModalToDisplay = null
    ): RedirectResponse {

            $company = auth()->user()->company;
            $companyContactHistory = $company->company_contact_history()->orderBy('id', 'desc')->first();
            $otp = OTP::firstWhere(['user_id' => auth()->user()->id, $identifier => $value, 'code' => $code]);
            if (!$otp) {
                $companyContactHistory->status = 'failed';
                $companyContactHistory->failure_reason = 'Invalid OTP';
                $companyContactHistory->save();
                return \Redirect::back()->withErrors(['otp' => 'Invalid code. Please try again.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }
            if (Carbon::now()->isAfter($otp->expires_at)) {
                $companyContactHistory->status = 'failed';
                $companyContactHistory->failure_reason = 'OTP has expired';
                $companyContactHistory->save();
                return \Redirect::back()->withErrors(['otp' => 'Verification code expired. Request a new code.'])
                    ->with('modalToShow', $currentModalToDisplay);
            }

            Session::forget('verifyType');
            \DB::transaction(function () use ($identifier, $company, $otp, $value) {

                $otp->delete();
                $mailboxLayerService = new MailboxLayerService;
                $domainVerifyStatus = $mailboxLayerService->validateEmail($value);
                $company->update([
                    'email' => $value,
                    'show_workplace_verfification' => !$domainVerifyStatus['role'] && !$domainVerifyStatus['disposable'] && !$domainVerifyStatus['free']
                ]);

                $companyContactHistory = $company->company_contact_history()->orderBy('id', 'desc')->first();
                $companyContactHistory->status = 'successful';
                $companyContactHistory->processed_at = now();
                $companyContactHistory->save();
            });

            return redirect()->route('frontend.company.dashboard')
            ->with(['modalToShow' => config('settings.modal.confirmation_modal'), 'message' => 'Your company email has been successfully updated.']);
        
    }
}
