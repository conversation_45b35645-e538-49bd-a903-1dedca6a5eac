<?php

namespace App\Providers;

use App\Models\AdminConversation;
use App\Models\AdminNotification;
use App\Models\Area;
use App\Models\User;
use App\Models\Country;
use App\Models\Category;
use App\Models\UserCompany;
use App\Models\QueryCategory;
use OpenSearch\ClientBuilder;
use Laravel\Scout\EngineManager;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Cache;
use App\ScoutEngines\OpenSearchEngine;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\ServiceProvider;
use App\View\Composers\AdminGlobalComposer;
use App\View\Composers\AdminLayoutComposer;
use App\Repositories\BaseRepositoryInterface;
use PHPUnit\Framework\Constraint\Count;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Gate::before(function ($user, $ability) {
            return ($user->hasPermissionTo('super user')) ? true : null;
        });

        View::composer('admin.layouts.app', AdminLayoutComposer::class);
        View::composer('admin.layouts.guest', AdminLayoutComposer::class);
        View::composer('admin.layouts.app', AdminGlobalComposer::class);

        $countries = Cache::remember('countries', now()->addDays(30), function () {
            return Country::orderByRaw("FIELD(code, 'GB', 'UK', 'US') DESC") // Prioritize UK first
                // ->orderBy('name', 'ASC')
                ->get();
        });

        $permissions = Cache::remember('permissions', now()->addHours(8), function () {
            return Permission::where('type', 'supplier')->get();
        });





        $catQueryTable = (new QueryCategory)->getTable();
        $queryCategories = [];
        if (Schema::hasTable($catQueryTable)) {
            $queryCategories = Cache::remember('queryCategories', now()->addDays(30), function () {
                return QueryCategory::orderBy('id', 'ASC')->get();
            });
        }

        $categories = Cache::remember('categories', now()->addMinutes(60), function () {
            return Category::where('level', 4)->get();
        });

        View::composer('*', function ($view) {
            $user = '';
            $modalId = '';
            if (auth()->check() && empty(auth()->user()->username)) {
                Session::put('registeredUserId', auth()->user()->id);
            }
            if (Session::has('registeredUserId')) {
                $userId = Session::get('registeredUserId');
                $user = User::where('id', $userId)->first();
                $userCompany = UserCompany::where('user_id', $userId)->first();
                $user->city = $user->area;
                $user->country = $user->area ? $user->area->country : null;
                $phonePrefix = Country::where('code', $user->city ? $user->country->code : '')->first();
                $user->phone = str_replace($user->phone_prefix, '', $user->phone);
                $user->phone_prefix = $phonePrefix ? $phonePrefix->phone_prefix : '';
                $user->company = $userCompany->company_name ?? '';
                $user->occupation = $userCompany->position ?? '';
                $user->categoryData = $user->categories()->pluck('category_id')->toArray();
                if (!Session::has('modalToShow')) {
                    if (empty($user->email_verified_at)) {
                        $modalId = config('settings.modal.signup');
                    } else if (empty($user->first_name) || empty($user->last_name) || empty($user->country_id)) {
                        $modalId = config('settings.modal.user_basic_details');
                    } else if (empty($user->company)) {
                        $modalId = config('settings.modal.occupation_details');
                    } else if (empty($user->phone_verified_at)) {
                        $modalId = config('settings.modal.phone_number_prompt');

                    }

                }
                $user->modalId = $modalId;

            }
            $view->with('signUpUser', $user);
        });
        View::share([
            'countries' => $countries,
            'categories' => $categories,
            'queryCategories' => $queryCategories,
            'permissions' => $permissions,
        ]);

        resolve(EngineManager::class)->extend('opensearch', function ($app) {


            if (env('OPENSEARCH_USER') && env('OPENSEARCH_PASS')) {
                $client = ClientBuilder::create()
                    ->setHosts([
                        [
                            'host' => parse_url(env('OPENSEARCH_HOST'), PHP_URL_HOST),
                            'port' => env('OPENSEARCH_PORT'),
                            'scheme' => env('OPENSEARCH_SCHEME', 'https'),
                            'user' => env('OPENSEARCH_USER'),
                            'pass' => env('OPENSEARCH_PASS'),
                        ]
                    ])
                    ->setSSLVerification(false) // IMPORTANT for localhost + self-signed
                    ->build();
            } else {
                $client = ClientBuilder::create()
                    ->setHosts([env('OPENSEARCH_HOST', 'http://127.0.0.1:9200')])
                    ->build();
            }



            //return new OpenSearchEngine($client, 'products'); // Use 'products' as index
            return new OpenSearchEngine($client);
        });

        View::composer('admin.layouts.app', function ($view) {
            $myInboxCount = 0;
            $allInboxCount = 0;
            $chatOpenCount = 0;

            if (Schema::hasTable('admin_notifications')) {
                $adminId = auth()->id();

                $myInboxCount = AdminNotification::where('assigned_to', $adminId)
                    ->where('status', 'open')
                    ->count();

                $allInboxCount = AdminNotification::where('status', 'open')->count();
            }

            if (Schema::hasTable('admin_conversations')) {
                $chatOpenCount = AdminConversation::where('status', 'open')->count();
            }

            $view->with([
                'myInboxCount' => $myInboxCount,
                'allInboxCount' => $allInboxCount,
                'chatOpenCount' => $chatOpenCount,
            ]);
        });


        Blade::if('feature', function ($slug) {
            return userHasFeatureAccess($slug);
        });

        Blade::if('feature_limit', function ($slug, $operator = null, $value = null) {
            $limit = userFeatureLimit($slug);

            if (is_null($operator) || is_null($value)) {
                return !is_null($limit);
            }

            return match ($operator) {
                '>' => $limit > $value,
                '>=' => $limit >= $value,
                '<' => $limit < $value,
                '<=' => $limit <= $value,
                '==' => $limit == $value,
                '===' => $limit === $value,
                '!=' => $limit != $value,
                default => false,
            };
        });
    }
}
