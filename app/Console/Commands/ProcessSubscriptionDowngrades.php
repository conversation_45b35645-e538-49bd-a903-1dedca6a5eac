<?php
namespace App\Console\Commands;

use App\Models\StripeSubscription;
use Stripe\Stripe;
use Stripe\Subscription;
use Illuminate\Support\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Models\SubscriptionDowngrade;
use App\Models\SubscriptionActive;

class ProcessSubscriptionDowngrades extends Command
{
    protected $signature = 'subscriptions:process-downgrades';
    protected $description = 'Processes pending subscription downgrades based on downgrade_date';

    public function handle()
{
    Log::info("Running subscription downgrade process...");

    $today = Carbon::now()->startOfDay();

    $downgrades = SubscriptionDowngrade::whereDate('downgrade_date', '<=', $today)
        ->where('downgrade_status', 'pending')
        ->get();

    if ($downgrades->isEmpty()) {
        Log::info("No pending subscription downgrades found.");
        return;
    }

    Stripe::setApiKey(env('STRIPE_SECRET'));

    foreach ($downgrades as $downgrade) {
        try {
            $user = SubscriptionActive::where('stripe_customer_id', $downgrade->stripe_customer_id)->first();
            $userId = $user ? $user->user_id : null;

            if ($downgrade->new_subscription_id == 'free') {
                // Create a free subscription on Stripe
                $stripeSubscription = Subscription::create([
                    'customer' => $downgrade->stripe_customer_id,
                    'items' => [['price' => env('STRIPE_FREE_PLAN_ID')]],
                    'trial_period_days' => 0,
                ]);

                $periodEnd = Carbon::createFromTimestamp($stripeSubscription->current_period_end);

                $freeProduct = env('STRIPE_FREE_PRODUCT_ID');
                $stripeProduct = StripeSubscription::where('stripe_product_id',$freeProduct)->first();

                //$plan = $stripeSubscription->items->data[0]->plan ?? null;

                // SubscriptionActive::create([
                //     'company_id' => $downgrade->company_id,
                //     'stripe_subscription_id' => $stripeProduct->id,
                //     'subscription_id' => $stripeSubscription->id,
                //     'stripe_customer_id' => $downgrade->stripe_customer_id,
                //     'user_id' => $userId,
                //     'billing_cycle' => 'free',
                //     'currency' => $plan->currency ?? null,
                //     'cost' => 0,
                //     'last_payment_status' => 'paid',
                //     'last_payment_date' => Carbon::now(),
                // ]);

                SubscriptionActive::updateOrCreate(
                    ['company_id' => $downgrade->company_id],
                    [
                        'stripe_subscription_id' => $stripeProduct->id,
                        'subscription_id' => $stripeSubscription->id,
                        'stripe_customer_id' => $downgrade->stripe_customer_id,
                        'user_id' => $userId,
                        'billing_cycle' => 'free',
                        'currency' => 'usd',//$plan->currency ?? null,
                        'cost' => 0,
                        'last_payment_status' => 'paid',
                        'last_payment_date' => Carbon::now(),
                        'ended_at' => $periodEnd,
                    ]
                );

                // Update downgrade record status
                $downgrade->update([
                    'downgrade_status' => 'applied',
                    'new_subscription_id' => $stripeSubscription->id,
                ]);

                Log::info("Downgraded to free plan for company ID: {$downgrade->company_id}");
            } else {
                // Retrieve new subscription from Stripe
                $stripeSubscription = Subscription::retrieve($downgrade->new_subscription_id);
                $plan = $stripeSubscription->items->data[0]->plan ?? null;

                if ($plan) {
                    // SubscriptionActive::create([
                    //     'company_id' => $downgrade->company_id,
                    //     'stripe_subscription_id' => $downgrade->new_subscription_id,
                    //     'subscription_id' => $downgrade->subscription_id,
                    //     'stripe_customer_id' => $downgrade->stripe_customer_id,
                    //     'user_id' => $userId,
                    //     'billing_cycle' => $plan->interval ?? null,
                    //     'currency' => $plan->currency ?? null,
                    //     'cost' => $plan->amount / 100 ?? null,
                    //     'last_payment_status' => $stripeSubscription->status === 'active' ? 'paid' : 'failed',
                    //     'last_payment_date' => Carbon::createFromTimestamp($stripeSubscription->current_period_start),
                    // ]);


                    SubscriptionActive::updateOrCreate(
                        ['company_id' => $downgrade->company_id],
                        [
                            'stripe_subscription_id' => $downgrade->new_subscription_id,
                            'subscription_id' => $downgrade->subscription_id,
                            'stripe_customer_id' => $downgrade->stripe_customer_id,
                            'user_id' => $userId,
                            'billing_cycle' => $plan->interval ?? null,
                            'currency' => $plan->currency ?? null,
                            'cost' => isset($plan->amount) ? $plan->amount / 100 : null,
                            'last_payment_status' => $stripeSubscription->status === 'active' ? 'paid' : 'failed',
                            'last_payment_date' => Carbon::createFromTimestamp($stripeSubscription->current_period_start),
                            'ended_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                        ]
                    );

                    // Update downgrade record status
                    $downgrade->update(['downgrade_status' => 'applied']);

                    Log::info("Subscription downgraded for company ID: {$downgrade->company_id}");
                } else {
                    Log::error("Error processing downgrade for company ID: {$downgrade->company_id}, Error: Plan not found");
                    $downgrade->update(['downgrade_status' => 'cancelled']);
                }
            }
        } catch (\Exception $e) {
            Log::error("Error processing downgrade for company ID: {$downgrade->company_id}, Error: " . $e->getMessage());
        }
    }

    Log::info("Subscription downgrade process completed.");
}

}
