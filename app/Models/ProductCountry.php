<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductCountry extends Model
{
    public $timestamps = false;
    protected $fillable = ['product_id', 'country_id', 'product_continent_id'];

    public function country()
    {
        return $this->belongsTo(GeoCountry::class, 'country_id');
    }

    public function product_country_regions()
    {
        return $this->hasMany(ProductRegion::class, 'product_country_id');
    }
}
