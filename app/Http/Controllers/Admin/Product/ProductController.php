<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;

class ProductController extends Controller
{
    public function storeProductDocuments(Request $request, $productId)
    {
        $request->validate([
            'documents.*.title' => 'required|string',
            'documents.*.file' => 'required|file|mimes:pdf,doc,docx,jpg,png|max:2048',
        ]);

        $product = Product::findOrFail($productId);

        foreach ($request->documents as $document) {
            $product->addMedia($document['file'])
                    ->usingName($document['title'])
                    ->toMediaCollection('documents');
        }

        return response()->json(['message' => 'Documents uploaded successfully']);
    }

    public function storeProductImages(Request $request, $productId)
    {
        $request->validate([
            'main_image' => 'image|mimes:jpg,jpeg,png|max:2048',
            'other_images.*' => 'image|mimes:jpg,jpeg,png|max:2048',
        ]);

        $product = Product::findOrFail($productId);

        // Store the main image
        if ($request->hasFile('main_image')) {
            $product->addMedia($request->file('main_image'))
                    ->toMediaCollection('main_image');
        }

        // Store other images
        if ($request->hasFile('other_images')) {
            foreach ($request->file('other_images') as $image) {
                $product->addMedia($image)
                        ->toMediaCollection('other_images');
            }
        }

        return response()->json(['message' => 'Images uploaded successfully']);
    }

    public function storeProductShippings(Request $request, $productId)
    {
        $request->validate([
            'shippings' => 'required|array',
            'shippings.*.destination' => 'required|string',
            'shippings.*.shipping_method' => 'required|string',
            'shippings.*.estimated_delivery_time' => 'required|string',
            'shippings.*.shipping_rate' => 'required|numeric|min:0',
            'shippings.*.notes' => 'nullable|string',
        ]);

        $product = Product::findOrFail($productId);

        foreach ($request->shippings as $shippingData) {
            $product->shippings()->create($shippingData);
        }

        return response()->json(['message' => 'Product shipping details saved successfully']);
    }

    public function storeProductAttributes(Request $request, $productId)
    {
        $request->validate([
            'attributes' => 'required|array',
            'attributes.*.attribute' => 'required|string',
            'attributes.*.characteristic' => 'required|string',
        ]);

        $product = Product::findOrFail($productId);

        foreach ($request->attributes as $attributeData) {
            $product->attributes()->create($attributeData);
        }

        return response()->json(['message' => 'Product attributes saved successfully']);
    }

    public function storeProductCertifications(Request $request, $productId)
    {
        $request->validate([
            'certifications' => 'required|array',
            'certifications.*.certificate_title' => 'required|string',
            'certifications.*.certification_body' => 'required|string',
            'certifications.*.certification_number' => 'nullable|string',
            'certifications.*.notes' => 'nullable|string',
        ]);

        $product = Product::findOrFail($productId);

        foreach ($request->certifications as $certificationData) {
            $product->certifications()->create($certificationData);
        }

        return response()->json(['message' => 'Product certifications saved successfully']);
    }

    public function storeProductStandardsCompliances(Request $request, $productId)
    {
        $request->validate([
            'standards_compliances' => 'required|array',
            'standards_compliances.*.standard_name' => 'required|string',
            'standards_compliances.*.notes' => 'nullable|string',
        ]);

        $product = Product::findOrFail($productId);

        foreach ($request->standards_compliances as $standardData) {
            $product->standardsCompliances()->create($standardData);
        }

        return response()->json(['message' => 'Product standards compliance saved successfully']);
    }

}
