<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('product_shippings', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('product_id'); // Foreign key to products table
            $table->string('destination'); // Destination (e.g., country, region, or city)
            $table->string('shipping_method'); // Shipping method (e.g., Air, Sea, Express)
            $table->string('estimated_delivery_time'); // Estimated delivery time (e.g., 5-7 business days)
            $table->decimal('shipping_rate', 10, 2); // Shipping rate (e.g., $10.50)
            $table->text('notes')->nullable(); // Additional notes
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_shippings');
    }
};
