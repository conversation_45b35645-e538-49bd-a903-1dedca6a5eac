<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            $table->dropForeign(['country_id']);
            $table->dropColumn('country_id');
            $table->double('latitude')->nullable()->after('slug');
            $table->double('longitude')->nullable()->after('latitude');
            $table->foreignId('geo_region_id')
                ->after('longitude')
                ->constrained('geo_regions')
                ->cascadeOnDelete()
                ->default(0);
            $table->foreignId('geo_country_id')
                ->after('geo_region_id')
                ->constrained('geo_countries')
                ->cascadeOnDelete()
                ->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            //
        });
    }
};
