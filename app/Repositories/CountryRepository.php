<?php

namespace App\Repositories;

use App\Models\Country;

/**
 * Class CountryRepository
 *
 * Handles data operations for the Country model.
 */
class CountryRepository extends BaseRepository
{
    protected $model;

    /**
     * CountryRepository constructor.
     *
     * @param \App\Models\Country $country
     */
    public function __construct(Country $country)
    {
        $this->model = $country;
    }

    /**
     * Pluck records from the model.
     *
     * @param string $field_one
     * @param string|null $field_two
     * @return array
     */
    public function getPlucked(string $field_one, string $field_two = null)
    {
        if ($field_two) {
            return $this->model->pluck($field_one, $field_two);
        } else {
            return $this->model->pluck($field_one);
        }        
    }
}
