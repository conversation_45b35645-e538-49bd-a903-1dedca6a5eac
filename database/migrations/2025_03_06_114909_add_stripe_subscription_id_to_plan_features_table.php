<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {

    public function up(): void
    {
        Schema::table('plan_features', function (Blueprint $table) {
            // Step 1: Add column as nullable
            $table->foreignId('stripe_subscription_id')
                ->nullable()
                ->after('id')
                ->constrained('stripe_subscriptions')
                ->onDelete('cascade');
        });

    }

    public function down(): void
    {
        Schema::table('plan_features', function (Blueprint $table) {
            $table->dropForeign(['stripe_subscription_id']);
            $table->dropColumn('stripe_subscription_id');
        });
    }
};

