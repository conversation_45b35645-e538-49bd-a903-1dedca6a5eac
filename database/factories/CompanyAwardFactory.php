<?php

namespace Database\Factories;

use App\Models\CompanyAward;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyAward>
 */
class CompanyAwardFactory extends Factory
{
    protected $model = CompanyAward::class;

    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'image' => $this->faker->optional()->imageUrl(200, 200, 'awards', true, 'award'),
            'title' => $this->faker->sentence(3),
            'year' => $this->faker->year,
            'description' => $this->faker->optional()->paragraph,
        ];
    }
}
