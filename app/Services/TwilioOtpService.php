<?php

namespace App\Services;
use Twilio\Rest\Client;

class TwilioOtpService
{
    protected $twilio;

    public function __construct()
    {
        $this->twilio = new Client(env('TWILIO_SID'), env('TWILIO_AUTH_TOKEN'));
    }

    public function sendOtp($phoneNumber)
    {
        try {
            $this->twilio->verify->v2->services(env('TWILIO_VERIFY_SID'))
                ->verifications
                ->create($phoneNumber, "sms");
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function verifyOtp($phoneNumber, $otp)
    {
        try {
            $verification = $this->twilio->verify->v2->services(env('TWILIO_VERIFY_SID'))
                ->verificationChecks
                ->create([
                    'to' => $phoneNumber,
                    'code' => $otp
                ]);

            return $verification->status === 'approved';
        } catch (\Exception $e) {
            return false;
        }
    }
}