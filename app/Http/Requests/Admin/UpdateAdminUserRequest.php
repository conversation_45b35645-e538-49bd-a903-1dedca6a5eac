<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAdminUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('admin_user')->id;

        return [
            'username' => ['required', 'string', 'max:25','unique:users,username,' . $id],
            'status' => ['required', 'integer', Rule::in([
                $this->admin_user::STATUS_ACTIVE,
                $this->admin_user::STATUS_INACTIVE,
                $this->admin_user::STATUS_BLOCKED,
                $this->admin_user::STATUS_SUSPENDED,
            ])],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => 'required|email|unique:users,email,' . $id,
            'password' => 'nullable|min:8|confirmed',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,name',
            'admin_user_role' => ['required', 'string', 'max:255'],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * Provides user-friendly error messages for common validation errors,
     *
     * @return array<string, string> Array of validation error messages.
     */
    public function messages(): array
    {
        return [
            'permissions.required' => 'Please select permission for the user',
            'permissions.array' => 'Please select permission for the user',
            'admin_user_role.required' => 'User role field is required',
        ];
    }
}
