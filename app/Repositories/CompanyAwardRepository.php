<?php

namespace App\Repositories;

use App\Models\CompanyAward;

/**
 * Class CompanyAwardRepository
 *
 * Handles data operations for the CompanyAward model.
 */
class CompanyAwardRepository extends BaseRepository
{
    /**
     * The CompanyAward model instance.
     *
     * @var \App\Models\CompanyAward
     */
    protected $model;

    /**
     * CompanyAwardRepository constructor.
     *
     * @param \App\Models\CompanyAward $companyAward
     */
    public function __construct(CompanyAward $companyAward)
    {
        $this->model = $companyAward;
    }
}
