<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CompanyAssessmentAnswer
 *
 * @property int $id
 * @property int $assessment_id
 * @property int $question_id
 * @property int $answered_by
 * @property string $answer
 */
class CompanyAssessmentAnswer extends Model
{
    protected $fillable = [
        'assessment_id',
        'question_id',
        'answered_by',
        'answer',
    ];

    public function assessment()
    {
        return $this->belongsTo(CompanyAssessment::class);
    }

    public function question()
    {
        return $this->belongsTo(AssessmentQuestion::class);
    }

    public function answeredBy()
    {
        return $this->belongsTo(User::class, 'answered_by');
    }
}
