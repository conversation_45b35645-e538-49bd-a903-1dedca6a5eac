<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\CompanyRepository as Repository;
use App\Traits\DBService;
use Illuminate\Auth\Authenticatable;
use Illuminate\Support\Facades\Auth;
use App\Models\Company;

class CompanyService
{
    /**
     * DB service Trait
     *
     * @var DBService
     */
    use DBService;

     /*
     * User Repository Obj
     *
     * @var Repository $repository
     */
    public $repository;

    /**
     * Create a new instance
     *
     * @param Repository $repository
     * @return void
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }
    
    /**
     * Get current loggedin user
     *
     * @return User
     */
    public function current(): User
    {
        return Auth::user();
    }

    public function getListForAdmin(string $status = null, $company=null)
    {
        $conditions = [];
        if (!is_null($status)) {
            $conditions['status'] = $status;
        }
       
            $conditions['id'] = $company;
        

        return $this->repository->getByMixedCondition($conditions);
    }
    

    /**
     * Get list of all the statuses
     *
     * @return array
     */
    public function getStatuses()
    {
        return $this->repository->statuses();
    }
}
