<?php

namespace Database\Seeders;

use App\Models\GeoContinent;
use App\Models\GeoCountry;
use App\Models\GeoRegion;
use App\Models\GeoRegions;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;

class ProcessMapSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $processedMaps = DB::table('processed_map')->get();
        foreach($processedMaps as $map){
            $continent = GeoContinent::where('name', $map->new_global_region)->first();
            if(empty($continent->name)){
                GeoContinent::create([
                    'name' => $map->new_global_region
                ]);
            }
            $continent = GeoContinent::where('name', $map->new_global_region)->first();
            $country = GeoCountry::where('country_name', $map->new_country_name)->first();
            if(empty($country->country_name)){
                GeoCountry::create([
                    'continent_id' => $continent->id,
                    'code' => $map->code,
                    'country_name' => $map->new_country_name
                ]);
            }
            $country = GeoCountry::where('country_name', $map->new_country_name)->first();
            $region = GeoRegion::where('region', $map->region)->first();
            if(empty($region->region)){
                GeoRegion::create([
                    'country_id' => $country->id,
                    'iso_a2' => $map->iso_a2,
                    'region' => $map->region,
                    'shape' => $map->SHAPE,
                    'latitude' => $map->latitude,
                    'longitude' => $map->longitude,
                    'ogr_fid' => $map->OGR_FID
                ]);
            }
            
        }
    }
}
