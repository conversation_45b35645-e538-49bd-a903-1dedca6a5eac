<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_contact_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')
                ->constrained('companies')
                ->cascadeOnDelete();
            $table->string('contact_type');
            $table->string('old_value');
            $table->string('new_value');
            $table->string('failure_reason')->nullable();
            $table->enum('status', ['pending', 'successful', 'failed']);
            $table->string('user_ip_address', 45);
            $table->timestamp('requested_at')->useCurrent();
            $table->timestamp('processed_at')->useCurrent()->useCurrentOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_contact_history');
    }
};
