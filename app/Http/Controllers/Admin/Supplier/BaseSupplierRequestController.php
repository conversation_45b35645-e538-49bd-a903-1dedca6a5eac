<?php

namespace App\Http\Controllers\Admin\Supplier;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateUserRequest;
use App\Models\Company;
use App\Models\CompanyRegistration;
use App\Models\CompanyRegistrationStatus;
use App\Models\User;
use App\Notifications\SupplierRequestStatusChanged;
use App\Services\MailService;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Services\SupplierRequestService;
use Illuminate\Support\Facades\Gate;

class BaseSupplierRequestController extends Controller
{
    /**
     * User service
     *
     * @var SupplierRequestService
     */
    public $service;



    /**
     * Page title based on the role of users
     *
     * string $title
     */
    public $title;

    /**
     * Route prefix based on the role of users
     *
     * @var $routePrefix
     */
    public $routePrefix;

    /**
     * Create a new controller instance.
     *
     * @param SupplierRequestService $service
     * @param string $routePrefix
     * @param string $title
     * @return void
     */
    public function __construct(
        SupplierRequestService $service,
        string $routePrefix,
        string $title
    ) {
        $this->service = $service;
        $this->routePrefix = $routePrefix;
        $this->title = $title;
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }
        $status = $request->status ?? null;
        $title = $this->title;
        $routePrefix = $this->routePrefix;

        return view('admin.suppliers.requests.index', compact('status', 'title', 'routePrefix'));
    }

    protected function list($request)
    {
        $status = $request->status;
        $user = $this->service->current();
        $entrys = $this->service->getListForAdmin($status);
        $statuses = $this->service->getStatuses();

        return DataTables::of($entrys)
            ->addColumn('created_at', function ($entry) {
                return $entry->created_at;
            })
            ->addColumn('name', function ($entry) {
                return $entry->supplier->name;
            })
            ->addColumn('email', function ($entry) {
                return $entry->email;
            })
            ->addColumn('company', function ($entry) {
                return $entry->company_name;
            })
            ->addColumn('status', function ($entry) {
                $status = '';
                if ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_PENDING) {
                    $status = '<div style="background-color:#d35757;"  class="custom-rounded">Pending</div>';
                } elseif ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_ACTIVE) {
                    $status = '<div style="background-color:#358b00;" class="custom-rounded">Active</div>';
                } elseif ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_REJECTED) {
                    $status = '<div style="background-color:#ff0404;" class="custom-rounded">Rejected</div>';
                } elseif ($entry->registrationStatus->status == CompanyRegistrationStatus::STATUS_APPROVED) {
                    $status = '<div style="background-color:#007bff;" class="custom-rounded">Approved</div>';
                }

                return $status;
            })
            ->addColumn('action', function ($entry) use ($user, $statuses) {
                $action = '';
                if ($user->can('manage suppliers requests') || $user->can('view suppliers requests')) {
                    $action .= '<a href="' . route('admin.suppliers.requests.show', $entry) . '" class="btn btn-primary">View</a> ';
                }

                if ($user->can('manage supplier requests')) {
                    $action .= ' <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    Action
                                </button>
                                <ul class="dropdown-menu" style="">';


                    foreach ($statuses as $name => $value) {
                        // Skip STATUS_PENDING unless the current status is STATUS_PENDING
                        if ($entry->registrationStatus->status != CompanyRegistrationStatus::STATUS_PENDING && $value == CompanyRegistrationStatus::STATUS_PENDING) {
                            continue;
                        }

                        // Skip STATUS_ACTIVE unless the current status is STATUS_APPROVED
                        if ($value == CompanyRegistrationStatus::STATUS_ACTIVE && $entry->registrationStatus->status != CompanyRegistrationStatus::STATUS_APPROVED) {
                            continue;
                        }


                        $disabled = ($entry->registrationStatus->status == $value) ? 'disabled' : '';

                        $class = ($value == CompanyRegistrationStatus::STATUS_REJECTED) ? 'btn btn-warning' : 'btn-primary';

                        $action .= '<li class="dropdown-item">';
                        $action .= '    <button class="btn btn-sm ' . $class . ' change-status-btn ' . $disabled . '" 
                                                        data-id="' . $entry->id . '" ' . $disabled . ' 
                                                        data-statusname="' . $name . '" 
                                                        data-status="' . $value . '">' . $name . '</button>';
                        $action .= '</li>';
                    }

                    $action .= '</ul>';
                }
                return $action;
            })
            ->addColumn('phone', function ($entry) {
                return $entry->full_phone;
            })
            ->addColumn('country', function ($entry) {
                return $entry->country->name ?? 'N/A';
            })
            ->rawColumns(['status', 'action'])
            ->toJson();
    }

    public function parseShow(CompanyRegistration $supplier)
    {
        $title = $this->title;
        $routePrefix = $this->routePrefix;
        $userListLink = route($routePrefix . 'index');

        return view('admin.suppliers.requests.show', compact('supplier', 'userListLink', 'title', 'routePrefix'));
    }



    public function changeStatus(Request $request)
    {
        $status = $request->status;
        $id = $request->id;
        $comment = $request->admin_comments;

        $registrationRequest = $this->service->find($id);

        if ($status == CompanyRegistrationStatus::STATUS_APPROVED) {
            $this->service->createCompany($registrationRequest);
            //$registrationRequest->delete();

        }

        if (!$request->ajax()) {
            if (!$registrationRequest) {
                return back()->withErrors('No record found');
            }

            $registrationRequest->registrationStatus->update([
                'status' => $status,
                'admin_comments' => $comment,
                'reviewed_at' => \Carbon\Carbon::now(),
            ]);

            $registrationRequest->supplier->notify(new SupplierRequestStatusChanged($status == CompanyRegistrationStatus::STATUS_APPROVED ? 'approved' : 'rejected'));
            
            // MailService::send(new SupplierRequestStatusChanged($status == CompanyRegistrationStatus::STATUS_APPROVED ? 'approved' : 'rejected'), $registrationRequest->supplier->email);
            
            if ($status === CompanyRegistrationStatus::STATUS_ACTIVE) {
                return redirect()->route('admin.suppliers.index')->with('status', 'Status changed successfully.');
            }

            return back()->with('status', 'Status changed successfully');
        }

        if (!$registrationRequest) {
            return response()->json(['message' => 'No record Found']);
        }

        $registrationRequest->registrationStatus->update([
            'status' => $status,
            'admin_comments' => $comment,
            'reviewed_at' => \Carbon\Carbon::now(),
        ]);

        $registrationRequest->supplier->notify(new SupplierRequestStatusChanged($status == CompanyRegistrationStatus::STATUS_APPROVED ? 'approved' : 'rejected'));

        // MailService::send(new SupplierRequestStatusChanged($status == CompanyRegistrationStatus::STATUS_APPROVED ? 'approved' : 'rejected'), $registrationRequest->supplier->email);


        return response()->json(['message' => 'Status changed successfully']);
    }
}
