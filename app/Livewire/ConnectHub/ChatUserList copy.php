<?php

namespace App\Livewire\ConnectHub;

use App\Models\UserConversation;
use Livewire\Component;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ChatUserList extends Component
{
    public $search = '';
    public $filter = 'all';
    public string $conversationView = 'live'; // 'live' or 'archived'


    protected $listeners = ['chat-updated' => '$refresh'];


    public function updatedFilter()
    {
        // Optionally reset pagination or refresh UI
    }

    public function getFilteredUsersProperty()
    {
        return $this->getUserConversationList($this->filter === 'unread');
    }

    public function render()
    {
        $search = $this->search;

        $searchableUsers = collect();

        if (!empty($search)) {
            $searchableUsers = User::query()
                ->where('id', '!=', auth()->id())
                ->whereHas('roles', function ($query) {
                    $query->where('name', User::ROLE_USER);
                })
                ->where(function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                })
                ->get();
        }

        $users = $this->filteredUsers;


        return view('livewire.connect-hub.chat-user-list', compact('users', 'searchableUsers'));
    }


    protected function getUserConversationList(bool $unreadOnly = false)
    {
        $loggedInUserId = auth()->id();

        $conversations = UserConversation::where(function ($query) use ($loggedInUserId) {
            $query->where('user_one_id', $loggedInUserId)
                ->orWhere('user_two_id', $loggedInUserId);
        })->get();

        $partnerIds = [];
        $conversationMap = [];

        foreach ($conversations as $conversation) {
            if ($conversation->user_one_id === $conversation->user_two_id) {
                continue;
            }

            $partnerId = $conversation->user_one_id == $loggedInUserId
                ? $conversation->user_two_id
                : $conversation->user_one_id;

            $partnerIds[] = $partnerId;
            $conversationMap[$partnerId] = $conversation->id;
        }

        $conversationIds = array_values($conversationMap);

        $unreadCounts = DB::table('user_messages')
            ->select('conversation_id', DB::raw('COUNT(*) as unread'))
            ->whereIn('conversation_id', $conversationIds)
            ->where('sender_id', '!=', $loggedInUserId)
            ->whereNull('read_at')
            ->groupBy('conversation_id')
            ->get()
            ->keyBy('conversation_id');

        // If "Unread" filter is active, reduce to only those
        if ($unreadOnly) {
            $partnerIds = collect($conversationMap)->filter(fn($cid, $pid) => isset($unreadCounts[$cid]))->keys();
        }

        $users = User::whereIn('id', $partnerIds)->with('company')->get();

        $lastMessages = DB::table('user_messages')
            ->select('conversation_id', DB::raw('MAX(created_at) as latest_time'))
            ->whereIn('conversation_id', $conversationIds)
            ->groupBy('conversation_id');

        $messages = DB::table('user_messages as m')
            ->joinSub($lastMessages, 'last_messages', function ($join) {
                $join->on('m.conversation_id', '=', 'last_messages.conversation_id')
                    ->on('m.created_at', '=', 'last_messages.latest_time');
            })
            ->select('m.conversation_id', 'm.message', 'm.created_at')
            ->get()
            ->keyBy('conversation_id');

        $users->transform(function ($user) use ($conversationMap, $messages, $unreadCounts) {
            $conversationId = $conversationMap[$user->id] ?? null;

            $user->last_message = $conversationId && isset($messages[$conversationId])
                ? $messages[$conversationId]->message
                : null;

            $user->last_message_time = $conversationId && isset($messages[$conversationId])
                ? $messages[$conversationId]->created_at
                : null;

            $user->unread_count = $conversationId && isset($unreadCounts[$conversationId])
                ? $unreadCounts[$conversationId]->unread
                : 0;

            return $user;
        });

        return $users;

    }


    public function markMessagesAsRead($userId)
    {
        $loggedInUserId = auth()->id();

        $conversation = UserConversation::where(function ($query) use ($loggedInUserId, $userId) {
            $query->where('user_one_id', $loggedInUserId)
                ->where('user_two_id', $userId);
        })->orWhere(function ($query) use ($loggedInUserId, $userId) {
            $query->where('user_one_id', $userId)
                ->where('user_two_id', $loggedInUserId);
        })->first();

        if ($conversation) {
            DB::table('user_messages')
                ->where('conversation_id', $conversation->id)
                ->where('sender_id', '!=', $loggedInUserId)
                ->whereNull('read_at')
                ->update(['read_at' => now()]);
        }

        //$this->dispatch('chat-updated');
    }




    // public function setFilter($type)
    // {
    //     $this->filter = $type;
    // }
}
