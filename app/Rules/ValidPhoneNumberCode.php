<?php

namespace App\Rules;

use App\Models\Country;
use Illuminate\Contracts\Validation\Rule;
use libphonenumber\PhoneNumberUtil;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\NumberParseException;

class ValidPhoneNumberCode implements Rule
{
    
    protected $region;

    public function __construct($region) // Default to GB
    {
        $countryCode = explode('@', $region);
        $this->region = $countryCode[0];
    }

    public function passes($attribute, $value)
    {
        $phoneUtil = PhoneNumberUtil::getInstance();

        try {
            $numberProto = $phoneUtil->parse($value, $this->region);
            return $phoneUtil->isValidNumber($numberProto); // Check if the number is valid
        } catch (NumberParseException $e) {
            return false;
        }
    }

    public function message()
    {
        return 'The :attribute must be a valid phone number for the specified region.';
    }
}
