<?php

namespace App\Repositories;

use App\Models\CompanyAssessmentReason;

/**
 * Class CompanyAssessmentReasonRepository
 *
 * Handles data operations for the CompanyAssessmentReason model.
 */
class CompanyAssessmentReasonRepository extends BaseRepository
{
    /**
     * The CompanyAssessmentReason model instance.
     *
     * @var \App\Models\CompanyAssessmentReason
     */
    protected $model;

    /**
     * CompanyAssessmentReasonRepository constructor.
     *
     * @param \App\Models\CompanyAssessmentReason $companyAssessmentReason
     * @return void
     */
    public function __construct(CompanyAssessmentReason $companyAssessmentReason)
    {
        $this->model = $companyAssessmentReason;
    }
}
