<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('supplier_requests');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('supplier_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('website_url')->nullable();
            $table->foreignId('organisation_type_id')->constrained('organisation_types')->onDelete('cascade');
            $table->foreignId('organisation_size_id')->constrained('organisation_sizes')->onDelete('cascade');
            $table->foreignId('bussiness_sector_id')->constrained('business_sectors')->onDelete('cascade');
            $table->foreignId('location_type_id')->constrained('location_types')->onDelete('cascade');
            $table->foreignId('company_country_id')->constrained('countries')->onDelete('cascade');
            $table->string('city');
            $table->string('postal_code');
            $table->string('address_line_1');
            $table->string('address_line_2')->nullable();
            $table->string('phone_prefix');
            $table->string('company_phone');
            $table->string('company_email');
            $table->tinyInteger('status')->default(0);

            $table->timestamps();
        });
    }
};
