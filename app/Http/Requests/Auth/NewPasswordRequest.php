<?php

namespace App\Http\Requests\Auth;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Hash;

class NewPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'password' => ['required',
                'max:64',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
                'confirmed'
            ]
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (auth()->check() && Hash::check($this->password, $this->user()->password)) {
                $validator->errors()->add('password', "You cannot reuse a previously used password. Please choose a new password.");
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function failedValidation(Validator $validator): void
    {
        throw new ValidationException(
            $validator,
            \Redirect::back()
                ->withErrors($validator)
                ->with('modalToShow', $this->currentModalToDisplay())
        );
    }

    /**
     * Get the ID of the modal that should be shown after validation failure.
     *
     * @return string
     */
    public function currentModalToDisplay(): string
    {
        return config('settings.modal.forgot_password_three');
    }

    /**
     * Get the ID of the modal that should be shown after the request is successful.
     *
     * @return string
     */
    public function nextModalToDisplay(): string
    {
        return config('settings.modal.password_update');
    }
}
