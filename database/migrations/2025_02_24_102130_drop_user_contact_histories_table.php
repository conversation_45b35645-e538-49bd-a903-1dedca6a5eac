<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_contact_histories', function (Blueprint $table) {
            Schema::dropIfExists('user_contact_histories');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('user_contact_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')
                ->constrained('users')
                ->cascadeOnDelete();
            $table->string('contact_type');
            $table->string('old_value');
            $table->string('new_value');
            $table->tinyInteger('status')->default(0)->comment('pending => 0, successful => 1');
            $table->timestamp('requested_at')->useCurrent();
            $table->timestamp('processed_at')->useCurrent()->useCurrentOnUpdate();
        });
    }
};
