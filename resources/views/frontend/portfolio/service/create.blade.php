@extends('layouts.app-frontend-company')

@section('subcontent')
<section class="mob-fixed-footer">
  <div class="container-fluid">
    <form class="adm-forms" id="portfolio-service" method="post" action="{{ route('frontend.portfolio.portfolio-service.store') }}">
      <div class="row product-and-services">
        <div class="mb3 mt-3 col-md-12">
          <div class="row">
            <div class="col-12 create-title-field">
              <div class="fitter-field">
                <div class="label-area">
                  <img src="{{ Vite::asset('resources/images/icons/product.svg') }}" alt="icon">
                  <label for="inputCname" class="col-form-label font-18">Service
                    setup:</label>
                </div>
                <div class="input-area">
                  <div class="input-text">
                    <input type="text" class="form-control" id="name" maxlength="80"
                      placeholder="Enter service name">
                  </div>
                  <div class="listing-button-new">
                    <a href="{{ route('frontend.portfolio.portfolio-service.index') }}">
                      <button class="btn btn-secondary w-100" type="button">
                        <i class="fa fa-eye d-md-none" aria-hidden="true"></i>
                        <span class="d-none d-md-block">View Listing</span>
                      </button>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <div class="adm_tabs">
            <ul class="nav nav-tabs " id="profile-tabs" role="tablist">
              <li class="nav-item" role="presentation">
                <a class="nav-link active" id="about-tab" data-bs-toggle="tab"
                  data-bs-target="#about-tab-pane" type="button" role="tab"
                  aria-controls="about-tab-pane" aria-selected="true">*About</a>
              </li>
              <li class="nav-item" role="presentation">
                <a class="nav-link" id="images-tab" data-bs-toggle="tab"
                  data-bs-target="#images-tab-pane" type="button" role="tab"
                  aria-controls="images" aria-selected="false">*Photos & Documents</a>
              </li>
              <li class="nav-item" role="presentation">
                <a class="nav-link" id="price-tab" data-bs-toggle="tab"
                  data-bs-target="#price-tab-pane" type="button" role="tab"
                  aria-controls="price" aria-selected="false">Pricing & Supply</a>
              </li>
              <li class="nav-item" role="presentation">
                <a class="nav-link" id="specification-tab" data-bs-toggle="tab"
                  data-bs-target="#specification-tab-pane" type="button" role="tab"
                  aria-controls="specification" aria-selected="false">Specification</a>
              </li>
              <li class="nav-item" role="presentation">
                <a class="nav-link" id="documents-tab" data-bs-toggle="tab"
                  data-bs-target="#documents-tab-pane" type="button" role="tab"
                  aria-controls="documents" aria-selected="false">Listing Settings</a>
              </li>
            </ul>

            <div class="tab-content padding-b-100" id="myTabContent">
              <!--1st tab body-->
              <x-portfolio.service.create.add-tab-1 :company="$company" :groupedCategories="$groupedCategories" :categories="$categories" :afterSaleSupport="$afterSaleSupport" :regions="$regions" />
              <!--1st tab body end-->
              <!--2st tab body-->
              <x-portfolio.service.create.add-tab-2 />
              <!--2st tab body end-->
              <!--3st tab body-->
              <x-portfolio.service.create.add-tab-3 :unit="$unit" />
              <!--3st tab body end-->
              <!--4st tab body-->
              <x-portfolio.service.create.add-tab-4 />
              <!--4st tab body end-->
              <!--5st tab body-->
              <x-portfolio.service.create.add-tab-5 />
              <!--5st tab body end-->
            </div>

            <div class="form-footer row freezed-bottom">
              <div class="mb3-btn-group flex-row justify-content-between w-75-5">
                <div class="text-left">
                  <button type="button" class="btn-typepre"><i class="fa fa-angle-left"></i></button>
                  <a href="{{ route('frontend.portfolio.portfolio-product.index') }}"><button type="button" class="btn btn-link" id="cancel-product">Cancel</button></a>
                </div>
                <p class="error mb-0 d-none">
                  Portfolio capacity reached. Consider refining or <a href="#">upgrading
                    your plan</a>.
                </p>
                <div class="text-right">
                  <div class="button-type-group align-items-center">
                    <button type="button" class="btn btn-link" id="save-service">Save</button>
                    <button type="button" class="btn-type1" id="publish-service">Publish</button>
                    <button type="button" class="btn-typetwo"><i class="fa fa-angle-right"></i></button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</section>
@endsection
<style>
  .btn-typepre {
    position: relative;
    background-color: transparent !important;
    border: none !important;
    color: #038799;
  }

  .hidden {
    display: none;
  }

  .step {
    margin-bottom: 20px;
  }

  ul {
    list-style-type: none;
    padding: 0;
  }

  li {
    margin: 5px 0;
  }

  button {
    margin-top: 10px;
    padding: 10px 15px;
    cursor: pointer;
  }

  .next-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .back-btn {
    background-color: #f0f0f0;
  }
</style>
<script>
  const menuRightIconUrl = "{{ Vite::asset('resources/images/icons/menu-right-icon.png') }}";
</script>