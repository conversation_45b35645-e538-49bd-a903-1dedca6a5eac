<?php

namespace App\Http\Controllers\Stripe;

use App\Http\Controllers\Controller;
use App\Models\StripeSubscription;
use Illuminate\Http\Request;
use App\Services\StripeService;
use Stripe\Stripe;
use Stripe\BillingPortal\Session as BillingPortalSession;
use Stripe\Customer;
use Stripe\Checkout\Session;
use Stripe\Price;

class StripeController extends Controller
{
    public $stripeService;
    /**
     * The Stripe service instance.
     *
     * @var \App\Services\StripeService
     */
    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Handle Stripe webhook events.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        
        $signature = $request->header('Stripe-Signature');

        try {
            $event = $this->stripeService->validateWebhook($payload, $signature);

            $this->stripeService->handleEvent($event);
        } catch (\UnexpectedValueException $e) {
            return response()->json(['message' => 'Invalid payload'], 400);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            return response()->json(['message' => 'Invalid signature'], 400);
        }

        return response()->json(['message' => 'Webhook handled successfully'], 200);
    }

    /**
     * redirectToPortal
     *
     * @param  mixed $request
     */
    public function redirectToPortal(Request $request)
    {
        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $user = $request->user();

            $stripe_customer_id = $user->stripe_customer_id;

            if (!$stripe_customer_id) {
                try {
                    $customer = Customer::create([
                        'email' => $user->email,
                        'name' => $user->name,
                    ]);
                    $stripe_customer_id = $customer->id;

                    $user->update(['stripe_customer_id' => $stripe_customer_id]);
                } catch (\Exception $e) {
                    \Log::error('Stripe Customer Creation Error: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Unable to create Stripe customer. Please try again later.');
                }
            }

            try {
                $session = BillingPortalSession::create([
                    'customer' => $stripe_customer_id,
                    'return_url' => route('frontend.user-profile-setting'),
                ]);
            } catch (\Exception $e) {
                \Log::error('Stripe Billing Portal Session Error: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Unable to create billing portal session. Please try again later.');
            }

            return redirect($session->url);

        } catch (\Exception $e) {
            \Log::error('General Error in redirectToPortal: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An unexpected error occurred. Please try again later.');
        }
    }


    
    /**
     * createCheckoutSession
     *
     */
    public function createCheckoutSession(Request $request)
    {
        try {
            
            \Session::forget('stripeSubscription');

            $stripeSubscription = StripeSubscription::where('id', $request->plan_id)->first();
            if (!$stripeSubscription) {
                return redirect()->back()->with('error', 'Invalid subscription plan.');
            }
            $planProduct = $stripeSubscription->stripe_product_id;

            Stripe::setApiKey(config('services.stripe.secret'));
            $prices = Price::all(['product' => $planProduct]);
            \Log::debug($prices);
            $planPrice = $prices->data[0]->id;
            $user = auth()->user();

            $stripe_customer_id = $user->stripe_customer_id;

            if (!$stripe_customer_id) {
                try {
                    $customer = Customer::create([
                        'email' => $user->email,
                        'name' => $user->name,
                    ]);
                    $stripe_customer_id = $customer->id;

                    $user->update(['stripe_customer_id' => $stripe_customer_id]);
                } catch (\Exception $e) {
                    \Log::error('Stripe Customer Creation Error: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Unable to create Stripe customer. Please try again later.');
                }
            }
            try {
                $session = Session::create([
                    'payment_method_types' => ['card'],
                    'mode' => 'subscription',
                    'line_items' => [
                        [
                            'price' => $planPrice,
                            'quantity' => 1,
                        ],
                        
                    ],
                    'currency' => 'GBP',
                    'customer' => $stripe_customer_id,
                    'success_url' => route('frontend.company.dashboard') . '?session_id={CHECKOUT_SESSION_ID}',
                    'cancel_url' => route('frontend.available_plans'),
                ]);
            } catch (\Exception $e) {
                \Log::error('Stripe Checkout Session Creation Error: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Unable to create checkout session. Please try again later.');
            }

            \Session::put('stripeSubscription', $stripeSubscription);
            return redirect($session->url);

        } catch (\Exception $e) {
            \Log::error('General Error in createCheckoutSession: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An unexpected error occurred. Please try again later.');
        }
    }

}
