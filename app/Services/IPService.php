<?php

namespace App\Services;

use App\Models\GeoCountry;
use Illuminate\Support\Facades\Http;
use App\Services\CalculateRegionService;
Class IPService{
    
    protected $regionService;

    /**
     * Create a new instance of the controller.
     *
     * @param IpService $ipService
     */
    public function __construct(CalculateRegionService $regionService)
    {
        $this->regionService = $regionService;
    }

    public function getIpLocation($ip = null)
    {
        //$accessKey = config('services.ip_api.access_key');
        $ip = $ip ?? request()->ip(); // Use current user IP if none provided
        if ($ip === '127.0.0.1' || $ip === '::1' || $ip === '************') {
            $ip = '**************'; // fallback to a public IP for testing
        }
        // $response = Http::get("http://api.ipapi.com/api/{$ip}", [
        //     'access_key' => $accessKey,
        // ]);
        $response = Http::timeout(5)->get("http://ip-api.com/json/{$ip}");
        if ($response->successful()) {
            return $response->json(); // Contains country, city, region, etc.
        } else {
            return response()->json(['error' => 'Unable to fetch IP data'], 500);
        }
    }
    public function getSearchLocation()
    {
        $queryParams = request()->query();
        $location = [
            'city' => '',
            'latitude' => '',
            'longitude' => '',
            'country_code' => ''
        ];
        if (isset($queryParams['city'])) {
            $location['city'] = $queryParams['city'] . ', ' . $queryParams['country_code'];
            $location['latitude'] = $queryParams['latitude'];
            $location['longitude'] = $queryParams['longitude'];
            $location['country_code'] = $queryParams['country_code'];
            $country = GeoCountry::where('code', $queryParams['country_code'])->first();
            $queryParams['country_id'] = $country->id;
            $queryParams['region_id'] = $this->regionService->calculateRegion($country->id, $queryParams['latitude'], $queryParams['longitude']);
            // $queryParams['region_id'] = 1;
        } else {
            if (auth()->check()) {
                $location['city'] = auth()->user()->area->name . ', ' . auth()->user()->area->country->code;
                $location['latitude'] = auth()->user()->area->latitude;
                $location['longitude'] = auth()->user()->area->longitude;
                $location['country_code'] = auth()->user()->area->country->code;
                $queryParams['country_id'] = auth()->user()->area->geo_country_id;
                $queryParams['region_id'] = auth()->user()->area->geo_region_id;
            } else {
                $ipData = $this->getIpLocation();
                if ($ipData['city']) {
                    $location['city'] = $ipData['city'] . ', ' . $ipData['countryCode']; //$ipData['country_code'];
                    $location['latitude'] = $ipData['lat'];//$ipData['latitude'];
                    $location['longitude'] = $ipData['lon'];//$ipData['longitude'];
                    $location['country_code'] = $ipData['countryCode']; //$ipData['country_code'];
                    $country = GeoCountry::where('code', $ipData['countryCode'])->first(); //$ipData['country_code'];
                    $queryParams['country_id'] = $country->id;
                    $queryParams['region_id'] = $this->regionService->calculateRegion($country->id, $ipData['lat'], $ipData['lon']);//$ipData['latitude'];//$ipData['longitude'];
                    // $queryParams['region_id'] = 1;
                }
            }
        }
        return [
            'location' => $location,
            'queryParams' => $queryParams
        ];
    }
}

