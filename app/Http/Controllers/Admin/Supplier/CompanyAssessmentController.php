<?php

namespace App\Http\Controllers\Admin\Supplier;


use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\Request;
use App\Services\SupplierRequestService;
use Illuminate\Support\Facades\Gate;

class CompanyAssessmentController extends BaseCompanyAssessmentController
{
       /**
     * Create a new controller instance.
     *  
     * @param SupplierRequestService $service
     * @return void
     */
    public function __construct(SupplierRequestService $service)
    {

        $routePrefix = 'admin.suppliers.assessements.';
        $title = 'Company Assessements/Verifications';
        
        parent::__construct($service,  $routePrefix, $title);
    }

    public function show(Company $supplier)
    {
        if (!Gate::any(['manage company assessements', 'view company assessements'])) {
            abort(403);
        }

        return $this->parseShow($supplier);
    }
}
