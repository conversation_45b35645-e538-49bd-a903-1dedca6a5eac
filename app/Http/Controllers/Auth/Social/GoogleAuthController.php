<?php

namespace App\Http\Controllers\Auth\Social;

use App\Http\Actions\SocialAuthAction;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Laravel\Socialite\Facades\Socialite;

class GoogleAuthController extends Controller
{
    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function __invoke(): RedirectResponse
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle the callback from Google.
     *
     * @param SocialAuthAction $action
     * @return \Illuminate\Http\RedirectResponse
     */
    public function callback(SocialAuthAction $action): RedirectResponse
    {
        $google = Socialite::driver('google')->user();
        $user = $action->execute($google, 'google');

        return $user->isEmailVerified() && $user->isPhoneVerified()
            ? \Redirect::route('frontend.index')
            : \Redirect::route('frontend.index')
            ->with('modalToShow', config('settings.modal.user_basic_details'));
    }
}
