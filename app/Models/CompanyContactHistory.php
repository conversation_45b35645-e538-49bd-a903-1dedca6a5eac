<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyContactHistory extends Model
{

    protected $table = 'company_contact_history';
    public $timestamps = false;
    
    protected $fillable = [
        'company_id',
        'contact_type',
        'old_value',
        'new_value',
        'status',
        'user_ip_address',
        'requested_at',
        'processed_at'
    ];

    /**
     * Get the user associated with the profile.
    */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
