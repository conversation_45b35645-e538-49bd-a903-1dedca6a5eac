<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_conversations', function (Blueprint $table) {
            $table->enum('status', ['open', 'in_progress', 'resolved', 'escalated'])
            ->default('open')
            ->after('assigned_to');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_conversations', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
