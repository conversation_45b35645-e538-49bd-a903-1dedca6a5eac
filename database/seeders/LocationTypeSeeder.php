<?php

namespace Database\Seeders;

use App\Models\LocationType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LocationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $locationTypes = [
            'Main Location (One per company)',
            'Headquarters',
            'Warehouse / Distribution Center',
            'Manufacturing Facility',
            'Retail / Customer Location',
            'Service & Support Hub',
            'Field / Operational Site',
            'R&D / Innovation Center',
            'Training & Education Center',
            'Partner / Affiliate Location',
            'Other'
        ];
        foreach($locationTypes as $type){
            LocationType::create([
                'name' => $type
            ]);
        }
    }
}
