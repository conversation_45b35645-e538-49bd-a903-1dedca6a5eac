[{"id": "1", "code": "AD", "name": "Andorra", "currency_code": "EUR", "fips_code": "AN", "iso_numeric": "020", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+376"}, {"id": "2", "code": "AE", "name": "United Arab Emirates", "currency_code": "AED", "fips_code": "AE", "iso_numeric": "784", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+971"}, {"id": "3", "code": "AF", "name": "Afghanistan", "currency_code": "AFN", "fips_code": "AF", "iso_numeric": "004", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+93"}, {"id": "4", "code": "AG", "name": "Antigua and Barbuda", "currency_code": "XCD", "fips_code": "AC", "iso_numeric": "028", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "5", "code": "AI", "name": "<PERSON><PERSON><PERSON>", "currency_code": "XCD", "fips_code": "AV", "iso_numeric": "660", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "6", "code": "AL", "name": "Albania", "currency_code": "ALL", "fips_code": "AL", "iso_numeric": "008", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+355"}, {"id": "7", "code": "AM", "name": "Armenia", "currency_code": "AMD", "fips_code": "AM", "iso_numeric": "051", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+374"}, {"id": "8", "code": "AO", "name": "Angola", "currency_code": "AOA", "fips_code": "AO", "iso_numeric": "024", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+244"}, {"id": "9", "code": "AQ", "name": "Antarctica", "currency_code": "", "fips_code": "AY", "iso_numeric": "010", "continent_name": "Antarctica", "continent": "AN", "phone_prefix": "+672"}, {"id": "10", "code": "AR", "name": "Argentina", "currency_code": "ARS", "fips_code": "AR", "iso_numeric": "032", "continent_name": "South America", "continent": "SA", "phone_prefix": "+54"}, {"id": "11", "code": "AS", "name": "American Samoa", "currency_code": "USD", "fips_code": "AQ", "iso_numeric": "016", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "******"}, {"id": "12", "code": "AT", "name": "Austria", "currency_code": "EUR", "fips_code": "AU", "iso_numeric": "040", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+43"}, {"id": "13", "code": "AU", "name": "Australia", "currency_code": "AUD", "fips_code": "AS", "iso_numeric": "036", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+61"}, {"id": "14", "code": "AW", "name": "Aruba", "currency_code": "AWG", "fips_code": "AA", "iso_numeric": "533", "continent_name": "North America", "continent": "NA", "phone_prefix": "+297"}, {"id": "15", "code": "AX", "name": "Åland Islands", "currency_code": "EUR", "fips_code": "", "iso_numeric": "248", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+358"}, {"id": "16", "code": "AZ", "name": "Azerbaijan", "currency_code": "AZN", "fips_code": "AJ", "iso_numeric": "031", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+994"}, {"id": "17", "code": "BA", "name": "Bosnia and Herzegovina", "currency_code": "BAM", "fips_code": "BK", "iso_numeric": "070", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+387"}, {"id": "18", "code": "BB", "name": "Barbados", "currency_code": "BBD", "fips_code": "BB", "iso_numeric": "052", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "19", "code": "BD", "name": "Bangladesh", "currency_code": "BDT", "fips_code": "BG", "iso_numeric": "050", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+880"}, {"id": "20", "code": "BE", "name": "Belgium", "currency_code": "EUR", "fips_code": "BE", "iso_numeric": "056", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+32"}, {"id": "21", "code": "BF", "name": "Burkina Faso", "currency_code": "XOF", "fips_code": "UV", "iso_numeric": "854", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+226"}, {"id": "22", "code": "BG", "name": "Bulgaria", "currency_code": "BGN", "fips_code": "BU", "iso_numeric": "100", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+359"}, {"id": "23", "code": "BH", "name": "Bahrain", "currency_code": "BHD", "fips_code": "BA", "iso_numeric": "048", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+973"}, {"id": "24", "code": "BI", "name": "Burundi", "currency_code": "BIF", "fips_code": "BY", "iso_numeric": "108", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+257"}, {"id": "25", "code": "BJ", "name": "Benin", "currency_code": "XOF", "fips_code": "BN", "iso_numeric": "204", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+229"}, {"id": "26", "code": "BL", "name": "<PERSON>", "currency_code": "EUR", "fips_code": "TB", "iso_numeric": "652", "continent_name": "North America", "continent": "NA", "phone_prefix": "+590"}, {"id": "27", "code": "BM", "name": "Bermuda", "currency_code": "BMD", "fips_code": "BD", "iso_numeric": "060", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "28", "code": "BN", "name": "Brunei Darussalam", "currency_code": "BND", "fips_code": "BX", "iso_numeric": "096", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+673"}, {"id": "29", "code": "BO", "name": "Bolivia", "currency_code": "BOB", "fips_code": "BL", "iso_numeric": "068", "continent_name": "South America", "continent": "SA", "phone_prefix": "+591"}, {"id": "30", "code": "BQ", "name": "Bonaire, Sint Eustatius and Saba", "currency_code": "USD", "fips_code": "", "iso_numeric": "535", "continent_name": "North America", "continent": "NA", "phone_prefix": "+599"}, {"id": "31", "code": "BR", "name": "Brazil", "currency_code": "BRL", "fips_code": "BR", "iso_numeric": "076", "continent_name": "South America", "continent": "SA", "phone_prefix": "+55"}, {"id": "32", "code": "BS", "name": "Bahamas", "currency_code": "BSD", "fips_code": "BF", "iso_numeric": "044", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "33", "code": "BT", "name": "Bhutan", "currency_code": "BTN", "fips_code": "BT", "iso_numeric": "064", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+975"}, {"id": "34", "code": "BV", "name": "Bouvet Island", "currency_code": "NOK", "fips_code": "BV", "iso_numeric": "074", "continent_name": "Antarctica", "continent": "AN", "phone_prefix": "+47"}, {"id": "35", "code": "BW", "name": "Botswana", "currency_code": "BWP", "fips_code": "BC", "iso_numeric": "072", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+267"}, {"id": "36", "code": "BY", "name": "Belarus", "currency_code": "BYR", "fips_code": "BO", "iso_numeric": "112", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+375"}, {"id": "37", "code": "BZ", "name": "Belize", "currency_code": "BZD", "fips_code": "BH", "iso_numeric": "084", "continent_name": "North America", "continent": "NA", "phone_prefix": "+501"}, {"id": "38", "code": "CA", "name": "Canada", "currency_code": "CAD", "fips_code": "CA", "iso_numeric": "124", "continent_name": "North America", "continent": "NA", "phone_prefix": "+1"}, {"id": "39", "code": "CC", "name": "Cocos [Keeling] Islands", "currency_code": "AUD", "fips_code": "CK", "iso_numeric": "166", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+61"}, {"id": "40", "code": "CD", "name": "Democratic Republic of the Congo", "currency_code": "COD", "fips_code": "CD", "iso_numeric": "180", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+243"}, {"id": "41", "code": "CF", "name": "Central African Republic", "currency_code": "XAF", "fips_code": "CT", "iso_numeric": "140", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+236"}, {"id": "42", "code": "CG", "name": "Republic of the Congo", "currency_code": "XAF", "fips_code": "CF", "iso_numeric": "178", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+242"}, {"id": "43", "code": "CH", "name": "Switzerland", "currency_code": "CHF", "fips_code": "SZ", "iso_numeric": "756", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+41\t"}, {"id": "44", "code": "CI", "name": "Ivory Coast", "currency_code": "XOF", "fips_code": "IV", "iso_numeric": "384", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+225"}, {"id": "45", "code": "CK", "name": "Cook Islands", "currency_code": "NZD", "fips_code": "CW", "iso_numeric": "184", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+682"}, {"id": "46", "code": "CL", "name": "Chile", "currency_code": "CLP", "fips_code": "CI", "iso_numeric": "152", "continent_name": "South America", "continent": "SA", "phone_prefix": "+56"}, {"id": "47", "code": "CM", "name": "Cameroon", "currency_code": "XAF", "fips_code": "CM", "iso_numeric": "120", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+237"}, {"id": "48", "code": "CN", "name": "China", "currency_code": "CNY", "fips_code": "CH", "iso_numeric": "156", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+86"}, {"id": "49", "code": "CO", "name": "Colombia", "currency_code": "COP", "fips_code": "CO", "iso_numeric": "170", "continent_name": "South America", "continent": "SA", "phone_prefix": "+57"}, {"id": "50", "code": "CR", "name": "Costa Rica", "currency_code": "CRC", "fips_code": "CS", "iso_numeric": "188", "continent_name": "North America", "continent": "NA", "phone_prefix": "+506"}, {"id": "51", "code": "CU", "name": "Cuba", "currency_code": "CUP", "fips_code": "CU", "iso_numeric": "192", "continent_name": "North America", "continent": "NA", "phone_prefix": "+53"}, {"id": "52", "code": "CV", "name": "Cape Verde", "currency_code": "CVE", "fips_code": "CV", "iso_numeric": "132", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+238"}, {"id": "53", "code": "CW", "name": "Curaçao", "currency_code": "ANG", "fips_code": "UC", "iso_numeric": "531", "continent_name": "North America", "continent": "NA", "phone_prefix": "+599"}, {"id": "54", "code": "CX", "name": "Christmas Island", "currency_code": "AUD", "fips_code": "KT", "iso_numeric": "162", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+61"}, {"id": "55", "code": "CY", "name": "Cyprus", "currency_code": "EUR", "fips_code": "CY", "iso_numeric": "196", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+357"}, {"id": "56", "code": "CZ", "name": "Czech Republic", "currency_code": "CZK", "fips_code": "EZ", "iso_numeric": "203", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+420"}, {"id": "57", "code": "DE", "name": "Germany", "currency_code": "EUR", "fips_code": "GM", "iso_numeric": "276", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+49"}, {"id": "58", "code": "DJ", "name": "Djibouti", "currency_code": "DJF", "fips_code": "DJ", "iso_numeric": "262", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+253"}, {"id": "59", "code": "DK", "name": "Denmark", "currency_code": "DKK", "fips_code": "DA", "iso_numeric": "208", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+45"}, {"id": "60", "code": "DM", "name": "Dominica", "currency_code": "XCD", "fips_code": "DO", "iso_numeric": "212", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "61", "code": "DO", "name": "Dominican Republic", "currency_code": "DOP", "fips_code": "DR", "iso_numeric": "214", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "62", "code": "DZ", "name": "Algeria", "currency_code": "DZD", "fips_code": "AG", "iso_numeric": "012", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+213"}, {"id": "63", "code": "EC", "name": "Ecuador", "currency_code": "USD", "fips_code": "EC", "iso_numeric": "218", "continent_name": "South America", "continent": "SA", "phone_prefix": "+593"}, {"id": "64", "code": "EE", "name": "Estonia", "currency_code": "EUR", "fips_code": "EN", "iso_numeric": "233", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+372"}, {"id": "65", "code": "EG", "name": "Egypt", "currency_code": "EGP", "fips_code": "EG", "iso_numeric": "818", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+20"}, {"id": "66", "code": "EH", "name": "Western Sahara", "currency_code": "MAD", "fips_code": "WI", "iso_numeric": "732", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+212"}, {"id": "67", "code": "ER", "name": "Eritrea", "currency_code": "ERN", "fips_code": "ER", "iso_numeric": "232", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+291"}, {"id": "68", "code": "ES", "name": "Spain", "currency_code": "EUR", "fips_code": "SP", "iso_numeric": "724", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+34"}, {"id": "69", "code": "ET", "name": "Ethiopia", "currency_code": "ETB", "fips_code": "ET", "iso_numeric": "231", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+251"}, {"id": "70", "code": "FI", "name": "Finland", "currency_code": "EUR", "fips_code": "FI", "iso_numeric": "246", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+358"}, {"id": "71", "code": "FJ", "name": "Fiji", "currency_code": "FJD", "fips_code": "FJ", "iso_numeric": "242", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+679"}, {"id": "72", "code": "FK", "name": "Falkland Islands", "currency_code": "FKP", "fips_code": "FK", "iso_numeric": "238", "continent_name": "South America", "continent": "SA", "phone_prefix": "+500"}, {"id": "73", "code": "FM", "name": "Micronesia", "currency_code": "USD", "fips_code": "FM", "iso_numeric": "583", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+691"}, {"id": "74", "code": "FO", "name": "Faroe Islands", "currency_code": "DKK", "fips_code": "FO", "iso_numeric": "234", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+298"}, {"id": "75", "code": "FR", "name": "France", "currency_code": "EUR", "fips_code": "FR", "iso_numeric": "250", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+33"}, {"id": "76", "code": "GA", "name": "Gabon", "currency_code": "XAF", "fips_code": "GB", "iso_numeric": "266", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+241"}, {"id": "77", "code": "GB", "name": "United Kingdom of Great Britain and Northern Ireland", "currency_code": "GBP", "fips_code": "UK", "iso_numeric": "826", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+44"}, {"id": "78", "code": "GD", "name": "Grenada", "currency_code": "XCD", "fips_code": "GJ", "iso_numeric": "308", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "79", "code": "GE", "name": "Georgia", "currency_code": "GEL", "fips_code": "GG", "iso_numeric": "268", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+995"}, {"id": "80", "code": "GF", "name": "French Guiana", "currency_code": "EUR", "fips_code": "FG", "iso_numeric": "254", "continent_name": "South America", "continent": "SA", "phone_prefix": "+594"}, {"id": "81", "code": "GG", "name": "Guernsey", "currency_code": "GBP", "fips_code": "GK", "iso_numeric": "831", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+44-1481"}, {"id": "82", "code": "GH", "name": "Ghana", "currency_code": "GHS", "fips_code": "GH", "iso_numeric": "288", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+233"}, {"id": "83", "code": "GI", "name": "Gibraltar", "currency_code": "GIP", "fips_code": "GI", "iso_numeric": "292", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+350"}, {"id": "84", "code": "GL", "name": "Greenland", "currency_code": "DKK", "fips_code": "GL", "iso_numeric": "304", "continent_name": "North America", "continent": "NA", "phone_prefix": "+299"}, {"id": "85", "code": "GM", "name": "Gambia", "currency_code": "GMD", "fips_code": "GA", "iso_numeric": "270", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+220"}, {"id": "86", "code": "GN", "name": "Guinea", "currency_code": "GNF", "fips_code": "GV", "iso_numeric": "324", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+224"}, {"id": "87", "code": "GP", "name": "Guadeloupe", "currency_code": "EUR", "fips_code": "GP", "iso_numeric": "312", "continent_name": "North America", "continent": "NA", "phone_prefix": "+590"}, {"id": "88", "code": "GQ", "name": "Equatorial Guinea", "currency_code": "XAF", "fips_code": "EK", "iso_numeric": "226", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+240"}, {"id": "89", "code": "GR", "name": "Greece", "currency_code": "EUR", "fips_code": "GR", "iso_numeric": "300", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+30"}, {"id": "90", "code": "GS", "name": "South Georgia and the South Sandwich Islands", "currency_code": "GBP", "fips_code": "SX", "iso_numeric": "239", "continent_name": "Antarctica", "continent": "AN", "phone_prefix": "+500"}, {"id": "91", "code": "GT", "name": "Guatemala", "currency_code": "GTQ", "fips_code": "GT", "iso_numeric": "320", "continent_name": "North America", "continent": "NA", "phone_prefix": "+502"}, {"id": "92", "code": "GU", "name": "Guam", "currency_code": "USD", "fips_code": "GQ", "iso_numeric": "316", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "******"}, {"id": "93", "code": "GW", "name": "Guinea-Bissau", "currency_code": "XOF", "fips_code": "PU", "iso_numeric": "624", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+245"}, {"id": "94", "code": "GY", "name": "Guyana", "currency_code": "GYD", "fips_code": "GY", "iso_numeric": "328", "continent_name": "South America", "continent": "SA", "phone_prefix": "+592"}, {"id": "95", "code": "HK", "name": "Hong Kong", "currency_code": "HKD", "fips_code": "HK", "iso_numeric": "344", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+852"}, {"id": "96", "code": "HM", "name": "Heard Island and McDonald Islands", "currency_code": "AUD", "fips_code": "HM", "iso_numeric": "334", "continent_name": "Antarctica", "continent": "AN", "phone_prefix": "+672"}, {"id": "97", "code": "HN", "name": "Honduras", "currency_code": "HNL", "fips_code": "HO", "iso_numeric": "340", "continent_name": "North America", "continent": "NA", "phone_prefix": "+504"}, {"id": "98", "code": "HR", "name": "Croatia", "currency_code": "HRK", "fips_code": "HR", "iso_numeric": "191", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+385"}, {"id": "99", "code": "HT", "name": "Haiti", "currency_code": "HTG", "fips_code": "HA", "iso_numeric": "332", "continent_name": "North America", "continent": "NA", "phone_prefix": "+509"}, {"id": "100", "code": "HU", "name": "Hungary", "currency_code": "HUF", "fips_code": "HU", "iso_numeric": "348", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+36"}, {"id": "101", "code": "ID", "name": "Indonesia", "currency_code": "IDR", "fips_code": "ID", "iso_numeric": "360", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+62"}, {"id": "102", "code": "IE", "name": "Ireland", "currency_code": "EUR", "fips_code": "EI", "iso_numeric": "372", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+353"}, {"id": "103", "code": "IL", "name": "Israel", "currency_code": "ILS", "fips_code": "IS", "iso_numeric": "376", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+972"}, {"id": "104", "code": "IM", "name": "Isle of Man", "currency_code": "GBP", "fips_code": "IM", "iso_numeric": "833", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+44-1624"}, {"id": "105", "code": "IN", "name": "India", "currency_code": "INR", "fips_code": "IN", "iso_numeric": "356", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+91"}, {"id": "106", "code": "IO", "name": "British Indian Ocean Territory", "currency_code": "USD", "fips_code": "IO", "iso_numeric": "086", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+246"}, {"id": "107", "code": "IQ", "name": "Iraq", "currency_code": "IQD", "fips_code": "IZ", "iso_numeric": "368", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+964"}, {"id": "108", "code": "IR", "name": "Iran", "currency_code": "IRR", "fips_code": "IR", "iso_numeric": "364", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+98"}, {"id": "109", "code": "IS", "name": "Iceland", "currency_code": "ISK", "fips_code": "IC", "iso_numeric": "352", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+354"}, {"id": "110", "code": "IT", "name": "Italy", "currency_code": "EUR", "fips_code": "IT", "iso_numeric": "380", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+39"}, {"id": "111", "code": "JE", "name": "Jersey", "currency_code": "GBP", "fips_code": "JE", "iso_numeric": "832", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+44-1534"}, {"id": "112", "code": "JM", "name": "Jamaica", "currency_code": "JMD", "fips_code": "JM", "iso_numeric": "388", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "113", "code": "JO", "name": "Jordan", "currency_code": "JOD", "fips_code": "JO", "iso_numeric": "400", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+962"}, {"id": "114", "code": "JP", "name": "Japan", "currency_code": "JPY", "fips_code": "JA", "iso_numeric": "392", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+81"}, {"id": "115", "code": "KE", "name": "Kenya", "currency_code": "KES", "fips_code": "KE", "iso_numeric": "404", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+254"}, {"id": "116", "code": "KG", "name": "Kyrgyzstan", "currency_code": "KGS", "fips_code": "KG", "iso_numeric": "417", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+996"}, {"id": "117", "code": "KH", "name": "Cambodia", "currency_code": "KHR", "fips_code": "CB", "iso_numeric": "116", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+855"}, {"id": "118", "code": "KI", "name": "Kiribati", "currency_code": "AUD", "fips_code": "KR", "iso_numeric": "296", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+686"}, {"id": "119", "code": "KM", "name": "Comoros", "currency_code": "KMF", "fips_code": "CN", "iso_numeric": "174", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+269"}, {"id": "120", "code": "KN", "name": "Saint Kitts and Nevis", "currency_code": "XCD", "fips_code": "SC", "iso_numeric": "659", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "121", "code": "KP", "name": "North Korea", "currency_code": "KPW", "fips_code": "KN", "iso_numeric": "408", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+850"}, {"id": "122", "code": "KR", "name": "South Korea", "currency_code": "KRW", "fips_code": "KS", "iso_numeric": "410", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+82"}, {"id": "123", "code": "KW", "name": "Kuwait", "currency_code": "KWD", "fips_code": "KU", "iso_numeric": "414", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+965"}, {"id": "124", "code": "KY", "name": "Cayman Islands", "currency_code": "KYD", "fips_code": "CJ", "iso_numeric": "136", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "125", "code": "KZ", "name": "Kazakhstan", "currency_code": "KZT", "fips_code": "KZ", "iso_numeric": "398", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+7"}, {"id": "126", "code": "LA", "name": "Laos", "currency_code": "LAK", "fips_code": "LA", "iso_numeric": "418", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+856"}, {"id": "127", "code": "LB", "name": "Lebanon", "currency_code": "LBP", "fips_code": "LE", "iso_numeric": "422", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+961"}, {"id": "128", "code": "LC", "name": "Saint Lucia", "currency_code": "XCD", "fips_code": "ST", "iso_numeric": "662", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "129", "code": "LI", "name": "Liechtenstein", "currency_code": "CHF", "fips_code": "LS", "iso_numeric": "438", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+423"}, {"id": "130", "code": "LK", "name": "Sri Lanka", "currency_code": "LKR", "fips_code": "CE", "iso_numeric": "144", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+94"}, {"id": "131", "code": "LR", "name": "Liberia", "currency_code": "LRD", "fips_code": "LI", "iso_numeric": "430", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+231"}, {"id": "132", "code": "LS", "name": "Lesotho", "currency_code": "LSL", "fips_code": "LT", "iso_numeric": "426", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+266"}, {"id": "133", "code": "LT", "name": "Lithuania", "currency_code": "EUR", "fips_code": "LH", "iso_numeric": "440", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+370"}, {"id": "134", "code": "LU", "name": "Luxembourg", "currency_code": "EUR", "fips_code": "LU", "iso_numeric": "442", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+352"}, {"id": "135", "code": "LV", "name": "Latvia", "currency_code": "EUR", "fips_code": "LG", "iso_numeric": "428", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+371"}, {"id": "136", "code": "LY", "name": "Libya", "currency_code": "LYD", "fips_code": "LY", "iso_numeric": "434", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+218"}, {"id": "137", "code": "MA", "name": "Morocco", "currency_code": "MAD", "fips_code": "MO", "iso_numeric": "504", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+212"}, {"id": "138", "code": "MC", "name": "Monaco", "currency_code": "EUR", "fips_code": "MN", "iso_numeric": "492", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+377"}, {"id": "139", "code": "MD", "name": "Moldova", "currency_code": "MDL", "fips_code": "MD", "iso_numeric": "498", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+373"}, {"id": "140", "code": "ME", "name": "Montenegro", "currency_code": "EUR", "fips_code": "MJ", "iso_numeric": "499", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+382"}, {"id": "141", "code": "MF", "name": "Saint <PERSON>", "currency_code": "EUR", "fips_code": "RN", "iso_numeric": "663", "continent_name": "North America", "continent": "NA", "phone_prefix": "+590"}, {"id": "142", "code": "MG", "name": "Madagascar", "currency_code": "MGA", "fips_code": "MA", "iso_numeric": "450", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+261"}, {"id": "143", "code": "MH", "name": "Marshall Islands", "currency_code": "USD", "fips_code": "RM", "iso_numeric": "584", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+692"}, {"id": "144", "code": "MK", "name": "North Macedonia", "currency_code": "MKD", "fips_code": "MK", "iso_numeric": "807", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+389"}, {"id": "145", "code": "ML", "name": "Mali", "currency_code": "XOF", "fips_code": "ML", "iso_numeric": "466", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+223"}, {"id": "146", "code": "MM", "name": "Myanmar [Burma]", "currency_code": "MMK", "fips_code": "BM", "iso_numeric": "104", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+95"}, {"id": "147", "code": "MN", "name": "Mongolia", "currency_code": "MNT", "fips_code": "MG", "iso_numeric": "496", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+976"}, {"id": "148", "code": "MO", "name": "Macao", "currency_code": "MOP", "fips_code": "MC", "iso_numeric": "446", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+853"}, {"id": "149", "code": "MP", "name": "Northern Mariana Islands", "currency_code": "USD", "fips_code": "CQ", "iso_numeric": "580", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "******"}, {"id": "150", "code": "MQ", "name": "Martinique", "currency_code": "EUR", "fips_code": "MB", "iso_numeric": "474", "continent_name": "North America", "continent": "NA", "phone_prefix": "+596"}, {"id": "151", "code": "MR", "name": "Mauritania", "currency_code": "MRO", "fips_code": "MR", "iso_numeric": "478", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+222"}, {"id": "152", "code": "MS", "name": "Montserrat", "currency_code": "XCD", "fips_code": "MH", "iso_numeric": "500", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "153", "code": "MT", "name": "Malta", "currency_code": "EUR", "fips_code": "MT", "iso_numeric": "470", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+356"}, {"id": "154", "code": "MU", "name": "Mauritius", "currency_code": "MUR", "fips_code": "MP", "iso_numeric": "480", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+230"}, {"id": "155", "code": "MV", "name": "Maldives", "currency_code": "MVR", "fips_code": "MV", "iso_numeric": "462", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+960"}, {"id": "156", "code": "MW", "name": "Malawi", "currency_code": "MWK", "fips_code": "MI", "iso_numeric": "454", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+265"}, {"id": "157", "code": "MX", "name": "Mexico", "currency_code": "MXN", "fips_code": "MX", "iso_numeric": "484", "continent_name": "North America", "continent": "NA", "phone_prefix": "+52"}, {"id": "158", "code": "MY", "name": "Malaysia", "currency_code": "MYR", "fips_code": "MY", "iso_numeric": "458", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+60"}, {"id": "159", "code": "MZ", "name": "Mozambique", "currency_code": "MZN", "fips_code": "MZ", "iso_numeric": "508", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+258"}, {"id": "160", "code": "NA", "name": "Namibia", "currency_code": "NAD", "fips_code": "WA", "iso_numeric": "516", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+264"}, {"id": "161", "code": "NC", "name": "New Caledonia", "currency_code": "XPF", "fips_code": "NC", "iso_numeric": "540", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+687"}, {"id": "162", "code": "NE", "name": "Niger", "currency_code": "XOF", "fips_code": "NG", "iso_numeric": "562", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+227"}, {"id": "163", "code": "NF", "name": "Norfolk Island", "currency_code": "AUD", "fips_code": "NF", "iso_numeric": "574", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+672"}, {"id": "164", "code": "NG", "name": "Nigeria", "currency_code": "NGN", "fips_code": "NI", "iso_numeric": "566", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+234"}, {"id": "165", "code": "NI", "name": "Nicaragua", "currency_code": "NIO", "fips_code": "NU", "iso_numeric": "558", "continent_name": "North America", "continent": "NA", "phone_prefix": "+505"}, {"id": "166", "code": "NL", "name": "Netherlands", "currency_code": "EUR", "fips_code": "NL", "iso_numeric": "528", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+31"}, {"id": "167", "code": "NO", "name": "Norway", "currency_code": "NOK", "fips_code": "NO", "iso_numeric": "578", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+47"}, {"id": "168", "code": "NP", "name": "Nepal", "currency_code": "NPR", "fips_code": "NP", "iso_numeric": "524", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+977"}, {"id": "169", "code": "NR", "name": "Nauru", "currency_code": "AUD", "fips_code": "NR", "iso_numeric": "520", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+674"}, {"id": "170", "code": "NU", "name": "Niue", "currency_code": "NZD", "fips_code": "NE", "iso_numeric": "570", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+683"}, {"id": "171", "code": "NZ", "name": "New Zealand", "currency_code": "NZD", "fips_code": "NZ", "iso_numeric": "554", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+64"}, {"id": "172", "code": "OM", "name": "Oman", "currency_code": "OMR", "fips_code": "MU", "iso_numeric": "512", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+968"}, {"id": "173", "code": "PA", "name": "Panama", "currency_code": "PAB", "fips_code": "PM", "iso_numeric": "591", "continent_name": "North America", "continent": "NA", "phone_prefix": "+507"}, {"id": "174", "code": "PE", "name": "Peru", "currency_code": "PEN", "fips_code": "PE", "iso_numeric": "604", "continent_name": "South America", "continent": "SA", "phone_prefix": "+51"}, {"id": "175", "code": "PF", "name": "French Polynesia", "currency_code": "XPF", "fips_code": "FP", "iso_numeric": "258", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+689"}, {"id": "176", "code": "PG", "name": "Papua New Guinea", "currency_code": "PGK", "fips_code": "PP", "iso_numeric": "598", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+675"}, {"id": "177", "code": "PH", "name": "Philippines", "currency_code": "PHP", "fips_code": "RP", "iso_numeric": "608", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+63"}, {"id": "178", "code": "PK", "name": "Pakistan", "currency_code": "PKR", "fips_code": "PK", "iso_numeric": "586", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+92"}, {"id": "179", "code": "PL", "name": "Poland", "currency_code": "PLN", "fips_code": "PL", "iso_numeric": "616", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+48"}, {"id": "180", "code": "PM", "name": "Saint Pierre and Miquelon", "currency_code": "EUR", "fips_code": "SB", "iso_numeric": "666", "continent_name": "North America", "continent": "NA", "phone_prefix": "+508"}, {"id": "181", "code": "PN", "name": "Pitcairn Islands", "currency_code": "NZD", "fips_code": "PC", "iso_numeric": "612", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+64"}, {"id": "182", "code": "PR", "name": "Puerto Rico", "currency_code": "USD", "fips_code": "RQ", "iso_numeric": "630", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "183", "code": "PS", "name": "Palestine", "currency_code": "ILS", "fips_code": "WE", "iso_numeric": "275", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+970"}, {"id": "184", "code": "PT", "name": "Portugal", "currency_code": "EUR", "fips_code": "PO", "iso_numeric": "620", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+351"}, {"id": "185", "code": "PW", "name": "<PERSON><PERSON>", "currency_code": "USD", "fips_code": "PS", "iso_numeric": "585", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+680"}, {"id": "186", "code": "PY", "name": "Paraguay", "currency_code": "PYG", "fips_code": "PA", "iso_numeric": "600", "continent_name": "South America", "continent": "SA", "phone_prefix": "+595"}, {"id": "187", "code": "QA", "name": "Qatar", "currency_code": "QAR", "fips_code": "QA", "iso_numeric": "634", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+974"}, {"id": "188", "code": "RE", "name": "Réunion", "currency_code": "EUR", "fips_code": "RE", "iso_numeric": "638", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+262"}, {"id": "189", "code": "RO", "name": "Romania", "currency_code": "RON", "fips_code": "RO", "iso_numeric": "642", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+40"}, {"id": "190", "code": "RS", "name": "Serbia", "currency_code": "RSD", "fips_code": "RI", "iso_numeric": "688", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+381"}, {"id": "191", "code": "RU", "name": "Russia", "currency_code": "RUB", "fips_code": "RS", "iso_numeric": "643", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+7"}, {"id": "192", "code": "RW", "name": "Rwanda", "currency_code": "RWF", "fips_code": "RW", "iso_numeric": "646", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+250"}, {"id": "193", "code": "SA", "name": "Saudi Arabia", "currency_code": "SAR", "fips_code": "SA", "iso_numeric": "682", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+966"}, {"id": "194", "code": "SB", "name": "Solomon Islands", "currency_code": "SBD", "fips_code": "BP", "iso_numeric": "090", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+677"}, {"id": "195", "code": "SC", "name": "Seychelles", "currency_code": "SCR", "fips_code": "SE", "iso_numeric": "690", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+248"}, {"id": "196", "code": "SD", "name": "Sudan", "currency_code": "SDG", "fips_code": "SU", "iso_numeric": "729", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+249"}, {"id": "197", "code": "SE", "name": "Sweden", "currency_code": "SEK", "fips_code": "SW", "iso_numeric": "752", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+46"}, {"id": "198", "code": "SG", "name": "Singapore", "currency_code": "SGD", "fips_code": "SN", "iso_numeric": "702", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+65"}, {"id": "199", "code": "SH", "name": "Saint Helena", "currency_code": "SHP", "fips_code": "SH", "iso_numeric": "654", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+290"}, {"id": "200", "code": "SI", "name": "Slovenia", "currency_code": "EUR", "fips_code": "SI", "iso_numeric": "705", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+386"}, {"id": "201", "code": "SJ", "name": "Svalbard and <PERSON>", "currency_code": "NOK", "fips_code": "SV", "iso_numeric": "744", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+47"}, {"id": "202", "code": "SK", "name": "Slovakia", "currency_code": "EUR", "fips_code": "LO", "iso_numeric": "703", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+421"}, {"id": "203", "code": "SL", "name": "Sierra Leone", "currency_code": "SLL", "fips_code": "SL", "iso_numeric": "694", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+232"}, {"id": "204", "code": "SM", "name": "San Marino", "currency_code": "EUR", "fips_code": "SM", "iso_numeric": "674", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+378"}, {"id": "205", "code": "SN", "name": "Senegal", "currency_code": "XOF", "fips_code": "SG", "iso_numeric": "686", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+221"}, {"id": "206", "code": "SO", "name": "Somalia", "currency_code": "SOS", "fips_code": "SO", "iso_numeric": "706", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+252"}, {"id": "207", "code": "SR", "name": "Suriname", "currency_code": "SRD", "fips_code": "NS", "iso_numeric": "740", "continent_name": "South America", "continent": "SA", "phone_prefix": "+597"}, {"id": "208", "code": "SS", "name": "South Sudan", "currency_code": "SSP", "fips_code": "OD", "iso_numeric": "728", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+211"}, {"id": "209", "code": "ST", "name": "São Tomé and Príncipe", "currency_code": "STD", "fips_code": "TP", "iso_numeric": "678", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+239"}, {"id": "210", "code": "SV", "name": "El Salvador", "currency_code": "USD", "fips_code": "ES", "iso_numeric": "222", "continent_name": "North America", "continent": "NA", "phone_prefix": "+503"}, {"id": "211", "code": "SX", "name": "Sint Maarten", "currency_code": "ANG", "fips_code": "NN", "iso_numeric": "534", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "212", "code": "SY", "name": "Syria", "currency_code": "SYP", "fips_code": "SY", "iso_numeric": "760", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+963"}, {"id": "213", "code": "SZ", "name": "<PERSON><PERSON><PERSON><PERSON>", "currency_code": "SZL", "fips_code": "WZ", "iso_numeric": "748", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+268"}, {"id": "214", "code": "TC", "name": "Turks and Caicos Islands", "currency_code": "USD", "fips_code": "TK", "iso_numeric": "796", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "215", "code": "TD", "name": "Chad", "currency_code": "XAF", "fips_code": "CD", "iso_numeric": "148", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+235"}, {"id": "216", "code": "TF", "name": "French Southern Territories", "currency_code": "EUR", "fips_code": "FS", "iso_numeric": "260", "continent_name": "Antarctica", "continent": "AN", "phone_prefix": "+262"}, {"id": "217", "code": "TG", "name": "Togo", "currency_code": "XOF", "fips_code": "TO", "iso_numeric": "768", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+228"}, {"id": "218", "code": "TH", "name": "Thailand", "currency_code": "THB", "fips_code": "TH", "iso_numeric": "764", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+66"}, {"id": "219", "code": "TJ", "name": "Tajikistan", "currency_code": "TJS", "fips_code": "TI", "iso_numeric": "762", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+992"}, {"id": "220", "code": "TK", "name": "Tokelau", "currency_code": "NZD", "fips_code": "TL", "iso_numeric": "772", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+690"}, {"id": "221", "code": "TL", "name": "East Timor", "currency_code": "USD", "fips_code": "TT", "iso_numeric": "626", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+670"}, {"id": "222", "code": "TM", "name": "Turkmenistan", "currency_code": "TMT", "fips_code": "TX", "iso_numeric": "795", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+993"}, {"id": "223", "code": "TN", "name": "Tunisia", "currency_code": "TND", "fips_code": "TS", "iso_numeric": "788", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+216"}, {"id": "224", "code": "TO", "name": "Tonga", "currency_code": "TOP", "fips_code": "TN", "iso_numeric": "776", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+676"}, {"id": "225", "code": "TR", "name": "Turkey", "currency_code": "TRY", "fips_code": "TU", "iso_numeric": "792", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+90"}, {"id": "226", "code": "TT", "name": "Trinidad and Tobago", "currency_code": "TTD", "fips_code": "TD", "iso_numeric": "780", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "227", "code": "TV", "name": "Tuvalu", "currency_code": "AUD", "fips_code": "TV", "iso_numeric": "798", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+688"}, {"id": "228", "code": "TW", "name": "Taiwan", "currency_code": "TWD", "fips_code": "TW", "iso_numeric": "158", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+886"}, {"id": "229", "code": "TZ", "name": "Tanzania", "currency_code": "TZS", "fips_code": "TZ", "iso_numeric": "834", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+255"}, {"id": "230", "code": "UA", "name": "Ukraine", "currency_code": "UAH", "fips_code": "UP", "iso_numeric": "804", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+380"}, {"id": "231", "code": "UG", "name": "Uganda", "currency_code": "UGX", "fips_code": "UG", "iso_numeric": "800", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+256"}, {"id": "232", "code": "UM", "name": "U.S. Minor Outlying Islands", "currency_code": "USD", "fips_code": "", "iso_numeric": "581", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+1"}, {"id": "233", "code": "US", "name": "United States", "currency_code": "USD", "fips_code": "US", "iso_numeric": "840", "continent_name": "North America", "continent": "NA", "phone_prefix": "+1"}, {"id": "234", "code": "UY", "name": "Uruguay", "currency_code": "UYU", "fips_code": "UY", "iso_numeric": "858", "continent_name": "South America", "continent": "SA", "phone_prefix": "+598"}, {"id": "235", "code": "UZ", "name": "Uzbekistan", "currency_code": "UZS", "fips_code": "UZ", "iso_numeric": "860", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+998"}, {"id": "236", "code": "VA", "name": "Vatican City", "currency_code": "EUR", "fips_code": "VT", "iso_numeric": "336", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+379"}, {"id": "237", "code": "VC", "name": "Saint Vincent and the Grenadines", "currency_code": "XCD", "fips_code": "VC", "iso_numeric": "670", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "238", "code": "VE", "name": "Venezuela", "currency_code": "VEF", "fips_code": "VE", "iso_numeric": "862", "continent_name": "South America", "continent": "SA", "phone_prefix": "+58"}, {"id": "239", "code": "VG", "name": "British Virgin Islands", "currency_code": "USD", "fips_code": "VI", "iso_numeric": "092", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "240", "code": "VI", "name": "U.S. Virgin Islands", "currency_code": "USD", "fips_code": "VQ", "iso_numeric": "850", "continent_name": "North America", "continent": "NA", "phone_prefix": "******"}, {"id": "241", "code": "VN", "name": "Vietnam", "currency_code": "VND", "fips_code": "VM", "iso_numeric": "704", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+84"}, {"id": "242", "code": "VU", "name": "Vanuatu", "currency_code": "VUV", "fips_code": "NH", "iso_numeric": "548", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+678"}, {"id": "243", "code": "WF", "name": "Wallis and Futuna", "currency_code": "XPF", "fips_code": "WF", "iso_numeric": "876", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+681"}, {"id": "244", "code": "WS", "name": "Samoa", "currency_code": "WST", "fips_code": "WS", "iso_numeric": "882", "continent_name": "Oceania", "continent": "OC", "phone_prefix": "+685"}, {"id": "245", "code": "XK", "name": "Kosovo", "currency_code": "EUR", "fips_code": "KV", "iso_numeric": "0", "continent_name": "Europe", "continent": "EU", "phone_prefix": "+383"}, {"id": "246", "code": "YE", "name": "Yemen", "currency_code": "YER", "fips_code": "YM", "iso_numeric": "887", "continent_name": "Asia", "continent": "AS", "phone_prefix": "+967"}, {"id": "247", "code": "YT", "name": "Mayotte", "currency_code": "EUR", "fips_code": "MF", "iso_numeric": "175", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+262"}, {"id": "248", "code": "ZA", "name": "South Africa", "currency_code": "ZAR", "fips_code": "SF", "iso_numeric": "710", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+27"}, {"id": "249", "code": "ZM", "name": "Zambia", "currency_code": "ZMW", "fips_code": "ZA", "iso_numeric": "894", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+260"}, {"id": "250", "code": "ZW", "name": "Zimbabwe", "currency_code": "ZWL", "fips_code": "ZI", "iso_numeric": "716", "continent_name": "Africa", "continent": "AF", "phone_prefix": "+263"}]