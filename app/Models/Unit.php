<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Unit extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',        // Unit name
        'description', // Unit description
    ];

    /**
     * Define a relationship with the ProductPricing model.
     */
    public function productPricings()
    {
        return $this->hasMany(ProductPricing::class, 'per_unit');
    }
}
