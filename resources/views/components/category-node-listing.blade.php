@php
    $slug = $category['slug'];
@endphp
@if(!empty($category['children']))
<li style="width: 100%;">
    <div class="supplier-item" onclick="toggleCategories('{{ $slug }}', '{{ $slug }}-icon')">
        <h5>{{ $category['name'] }}</h5>
        <p>
            <span>({{ $category['products_count'] }})</span>
            <i id="{{ $slug }}-icon" class="fa fa-angle-right"></i>
        </p>
    </div>

    <ul id="{{ $slug }}" class="category-children product-categories" style="width: 100%; padding-left: 10px;">
        @foreach($category['children'] as $childCategory)
            @include('components.category-node', ['category' => $childCategory])
        @endforeach
    </ul>
</li>
@else
<li style="width: 100%;">
    <div class="supplier-item">
        <div class="checkbox-container">
            <label for="{{ $slug }}">{{ $category['name'] }}</label>
        </div>
        <input type="checkbox" id="{{ $slug }}" name="product_categories[]" value="{{ $category['name'] }}" {{ in_array($category['name'], $queryParamsFilter ?? []) ? 'checked' : '' }}>
    </div>
</li>
@endif
