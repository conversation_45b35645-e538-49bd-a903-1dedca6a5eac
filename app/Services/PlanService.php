<?php

namespace App\Services;

use App\Enums\PlanType;
use App\Models\StripeSubscription;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Product;
use Stripe\Price;


class PlanService{

     /**
     * Retrieve all setting grouped by section
     *
     * @return array
     */
    public function getAllStripePlans_old()
    {
         // Set the Stripe API key
        Stripe::setApiKey(env('STRIPE_SECRET'));

        // Fetch all products
        $products = Product::all(['limit' => 100]); // Adjust limit as needed

        $productData = [];
        $i = 1;
        foreach ($products->data as $product) {
            // Fetch prices for the current product
            $prices = Price::all(['product' => $product->id]);
            $prices = $prices->toArray();
            $recurringType = $prices['data'][0]['recurring']['interval'];
            // Build product information with prices
            $productData[] = [
                'id' => $i,
                'product_id' => $product->id,
                'name' => $product->name,
                'description' => $product->description,
                'active' => $product->active,
                'created' => $product->created,
                'price_id' => $prices['data'][0]['id'], // Attach all price details
                'currency' => $prices['data'][0]['currency'],
                'pricing' => round($prices['data'][0]['unit_amount']/100, 2), // Attach all price details
                'type' => $prices['data'][0]['recurring']['interval'] == 'month' ? PlanType::month->value : PlanType::year->value,
                'interval' => $prices['data'][0]['recurring']['interval_count']
            ];
            $i ++;
        }

        return $productData;
    }


    public function getAllStripePlans()
    {
        return  StripeSubscription::orderBy('subscription_rank','asc')->get();
    }

    /**
     * Get current loggedin user
     *
     * @return App\Models\User
     */
    public function current()
    {
        return Auth::user();
    }
}