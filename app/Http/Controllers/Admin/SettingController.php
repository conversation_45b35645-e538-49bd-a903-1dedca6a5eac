<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\SettingService;

class SettingController extends Controller
{
    /**
     * Setting service
     *
     * @var SettingService
     */
    public $service;

    /**
     * Create a new controller instance.
     *
     * @param SettingService $service
     * @return void
     */
    public function __construct(SettingService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the settings grouped by section.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $settings = $this->service->groupBySection();
        $sections = $this->service->getSections();
        $timezones = timezone_identifiers_list();
        $activeTab = $request->tab ?? 'site';

        return view('admin.settings.index', compact(
            'user', 
            'settings', 
            'timezones', 
            'sections', 
            'activeTab'
        ));
    }

    /**
     * Save the updated settings.
     *
     * Iterates through the request data and updates settings based on the provided key-value pairs. 
     * Handles file uploads for specific keys (`logo` and `favicon`) and updates their values accordingly.
     * Redirects back with a success or error message based on the operation outcome.
     *
     * @param \Illuminate\Http\Request $request The HTTP request instance containing the settings data.
     * @return \Illuminate\Http\RedirectResponse Redirect response to the previous page with a success or error message.
     */
    public function save(Request $request)
    {
        try {
            $activeTab = $request->tab;
            $data = $request->except('_token', 'tab');

            foreach ($data as $key => $value) {
                if (in_array($key, ['logo', 'favicon', 'logo_dark', 'favicon_dark'])) {
                    $value = $this->service->uploadFile($request, $key);
                }

                $this->service->update($key, $value);
            }

            return redirect()->to(route('admin.settings.index') . '?tab=' . $activeTab)
                ->with('success', 'Settings updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }    
}