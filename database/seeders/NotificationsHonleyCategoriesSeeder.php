<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NotificationsHonleyCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
         DB::table('notifications_honley_categories')->insert([
            ['name' => 'Platform Updates', 'description' => 'System changes, downtime, new features'],
            ['name' => 'Account Notifications', 'description' => 'User-specific actions: verification, flags, policy issues'],
            ['name' => 'Subscription & Billing', 'description' => 'Invoices, plan changes, reminders'],
            ['name' => 'Company Profile Management', 'description' => 'Warnings, edits needed, compliance issues'],
            ['name' => 'Support Follow-up', 'description' => 'General customer service contact'],
            ['name' => 'Partnership / Promotion', 'description' => 'Collaboration, endorsement invites, offers'],
            ['name' => 'General Announcement', 'description' => 'Catch-all for anything not covered above'],
        ]);
    }
}
