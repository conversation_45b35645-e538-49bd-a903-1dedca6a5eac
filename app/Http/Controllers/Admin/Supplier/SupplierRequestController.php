<?php

namespace App\Http\Controllers\Admin\Supplier;


use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\CompanyRegistration;
use Illuminate\Http\Request;
use App\Services\SupplierRequestService;
use Illuminate\Support\Facades\Gate;

class SupplierRequestController extends BaseSupplierRequestController
{
       /**
     * Create a new controller instance.
     *  
     * @param SupplierRequestService $service
     * @return void
     */
    public function __construct(SupplierRequestService $service)
    {

        $routePrefix = 'admin.suppliers.requests.';
        $title = 'Supplier Request';
        
        parent::__construct($service,  $routePrefix, $title);
    }

    public function show(CompanyRegistration $supplier)
    {
        if (!Gate::any(['manage suppliers requests', 'view suppliers requests'])) {
            abort(403);
        }

        return $this->parseShow($supplier);
    }
}
