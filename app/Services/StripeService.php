<?php

namespace App\Services;

use App\Repositories\StripeRepository as Repository;
use Illuminate\Support\Facades\Log;
use App\Traits\DBService;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class StripeService
{
    protected $repository;

    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Validate the webhook signature.
     *
     * @param  string  $payload
     * @param  string  $signature
     * @return \Stripe\Event
     */
    public function validateWebhook($payload, $signature)
    {
        return \Stripe\Webhook::constructEvent(
            $payload,
            $signature,
            config('services.stripe.webhook_secret')
        );
    }

    /**
     * Handle the Stripe event.
     *
     * @param  \Stripe\Event  $event
     * @return void
     */
    public function handleEvent($event)
    {

        switch ($event->type) {

            case 'customer.subscription.created':
                //$this->repository->handleSubscriptionCreated($event->data->object);
                break;

            case 'customer.subscription.updated':
                //$this->repository->handleSubscriptionUpdated($event->data->object);
                break;

            case 'customer.subscription.deleted':
                //$this->repository->handleSubscriptionDeleted($event->data->object);
                break;

            case 'invoice.payment_succeeded':
                $this->repository->handlePaymentSucceeded($event->data->object);
                break;

            case 'invoice.payment_failed':
                $this->repository->handlePaymentFailed($event->data->object);
                break;

            case 'product.created':
                $this->repository->handleProductCreated($event->data->object);
                break;

            case 'product.updated':
                $this->repository->handleProductUpdated($event->data->object);
                break;

            case 'product.deleted':
                $this->repository->handleProductDeleted($event->data->object);
                break;

            case 'price.updated':
                //$this->repository->handlePriceUpdated($event->data->object);
                break;

            default:
                Log::info("Unhandled event type: {$event->type}");
        }
    }

}