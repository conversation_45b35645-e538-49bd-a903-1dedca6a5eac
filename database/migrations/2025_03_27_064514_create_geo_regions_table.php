<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('geo_regions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('country_id')
                ->constrained('geo_countries')
                ->cascadeOnDelete();
            $table->text('iso_a2');
            $table->text('region');
            $table->geometry('shape');
            $table->double('latitude');
            $table->double('longitude');
            $table->unsignedBigInteger('ogr_fid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geo_regions');
    }
};
