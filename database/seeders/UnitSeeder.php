<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = [
            ['name' => 'Product', 'description' => 'Individual item or unit. E.g., chair, car.'],
            ['name' => 'Bundle', 'description' => 'Group of items sold together. Clarify bundle size in notes.'],
            ['name' => 'Weight', 'description' => 'Measured by weight. E.g., kg, lb.'],
            ['name' => 'Volume', 'description' => 'Measured by volume. E.g., liters, gallons.'],
            ['name' => 'Length', 'description' => 'Measured by length. E.g., meters, feet.'],
            ['name' => 'Area', 'description' => 'Measured by area. E.g., square meters, square feet.'],
            ['name' => 'Transaction', 'description' => 'Single transaction or event. E.g., digital product, service fee.'],
            ['name' => 'Service', 'description' => 'Non-physical service. E.g., consultation, installation.'],
            ['name' => 'Session', 'description' => 'Single session. E.g., tutoring, coaching.'],
            ['name' => 'Hour', 'description' => 'Measured by hour. E.g., hourly consultancy.'],
            ['name' => 'Day', 'description' => 'Measured by day. E.g., daily rental, service.'],
            ['name' => 'Month', 'description' => 'Measured by month. E.g., subscription, service.'],
            ['name' => 'Year', 'description' => 'Measured by year. E.g., yearly subscription.'],
        ];

        DB::table('units')->insert($units);
    }
}
