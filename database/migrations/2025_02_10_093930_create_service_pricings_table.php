<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('service_pricings', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('service_id'); // Foreign key to services table
            $table->decimal('price', 10, 2); // Price
            $table->string('per_unit'); // Per unit description (e.g., per kg, per item)
            $table->integer('min_order')->default(1); // Minimum order quantity
            $table->string('part_number')->nullable(); // Part number (optional)
            $table->string('payment_method')->nullable(); // Payment method
            $table->text('notes')->nullable(); // Additional notes
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('service_pricings');
    }
};
