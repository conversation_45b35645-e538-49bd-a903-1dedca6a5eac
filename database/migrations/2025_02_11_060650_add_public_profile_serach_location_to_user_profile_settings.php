<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_profile_settings', function (Blueprint $table) {
            $table->tinyInteger('preferred_search_location')->default(0)->after('instagram_link');
            $table->tinyInteger('public_profile_email')->default(0)->after('preferred_search_location');
            $table->tinyInteger('public_profile_number')->default(0)->after('public_profile_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_profile_user_profile_settings', function (Blueprint $table) {
            //
        });
    }
};
