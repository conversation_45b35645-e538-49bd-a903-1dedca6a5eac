<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

trait DBService
{
    /**
     * Retrieve all.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function all(): Collection
    {
        return $this->repository->getAll();
    }

    /**
     * Find a record by ID.
     *
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function find(int $id): ?Model
    {
        return $this->repository->findById($id);
    }

    /**
     * Create a record
     *
     * @param array $data
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function create(array $data): ?Model
    {
        return $this->repository->create($data);
    }

    /**
     * Updte a record
     *
     * @param integer $id
     * @param array $data
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function update(int $id, array $data): ?Model
    {
        return $this->repository->update($id, $data);
    }

    /**
     * Delete a record by id
     *
     * @param integer $id
     * @return bool
     */
    public function delete(int $id)
    {
        return $this->repository->delete($id);
    }
}
