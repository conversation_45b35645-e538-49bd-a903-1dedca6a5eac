<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
      
        Schema::table('user_messages', function (Blueprint $table) {
            $table->timestamp('read_at')->nullable()->after('created_at'); // for unread tracking
            $table->boolean('is_deleted')->default(0)->after('read_at');   // 1 = deleted by sender
            $table->timestamp('deleted_at')->nullable()->after('is_deleted'); // deletion timestamp
            $table->boolean('blocked')->default(0)->after('deleted_at');   // 1 = blocked at send time
            $table->timestamp('blocked_at')->nullable()->after('blocked'); // when block happened
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_messages', function (Blueprint $table) {
            $table->dropColumn(['read_at', 'is_deleted', 'deleted_at', 'blocked', 'blocked_at']);
        });
    }
};
