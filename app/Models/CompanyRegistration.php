<?php

namespace App\Models;

use App\Traits\CompanyRelations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;


/**
 * Class CompanyRegistration
 *
 * @package App\Models
 * 
 */
class CompanyRegistration extends Model
{
    use HasFactory, CompanyRelations;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'user_id',
        'registered_location',
        'organisation_type_id',
        'organisation_size_id',
        'business_sector_id',
        'country_id',
        'company_name',
        'email',
        'phone',
        'phone_prefix',
        'website_url',
        'position',
        'address_line_1',
        'address_line_2',
        'city',
        'postcode',
        'latitude',
        'longitude',
        'is_main'
    ];




    /**
     * Accessor for phone number with prefix.
     *
     *
     * @return string
     */
    public function getFullPhoneAttribute(): string
    {
        return "{$this->phone_prefix} {$this->phone}";
    }

    /**
     * registrationStatus
     *
     * @return HasOne
     */
    public function registrationStatus(): HasOne
    {
        return $this->hasOne(CompanyRegistrationStatus::class, 'registration_id');
    }


    /**
     * Get the level 2 business category associated with the supplier request.
     */
    public function level2Category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'level_2_category_id', 'id');
    }

    /**
     * Get the level 3 business category associated with the supplier request.
     */
    public function level3Category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'level_3_category_id', 'id');
    }


    /**
     * Get the associated Country model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }


}
