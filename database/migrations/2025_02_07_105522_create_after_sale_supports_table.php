<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('after_sale_supports', function (Blueprint $table) {
            $table->id();
            $table->string('support_type'); // e.g., Warranty, Repair Service
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('after_sale_supports');
    }
};
