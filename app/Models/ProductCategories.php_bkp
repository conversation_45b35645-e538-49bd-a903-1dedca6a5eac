<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductCategories extends Model
{
    use HasFactory;
    protected $table = 'products_categories';
    protected $guarded = [
        'id'
    ];

    public function UpdatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by', 'id');
    }

    public function CreatedBy()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function BusinessCategory()
    {
        return $this->belongsTo(BusinessCategory::class, 'business_category_id', 'id');
    }
    public function Parent()
    {
        return $this->belongsTo(ProductCategories::class, 'parent', 'id');
    }

    public function getRouteKeyName()
    {
        return 'id';
    }

    public function children()
    {
        return $this->hasMany(ProductCategories::class, 'parent', 'id');
    }

    public static function getNestedCategories($businessCategoryId, $parentId = null)
    {
        return self::with('children')
            ->where('business_category_id', $businessCategoryId)
            ->where('parent', $parentId)
            ->get()
            ->map(function ($category) {
                $category->children = self::getNestedCategories($category->business_category_id, $category->id);
                return $category;
            });
    }

    public function products()
    {
        return $this->hasMany(Products::class, 'product_categories_id', 'id');
    }
}
