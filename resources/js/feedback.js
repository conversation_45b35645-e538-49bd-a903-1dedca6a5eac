// Feedback.js
if (currentRoute == "frontend.product.show") {
    let feedbackType = "like";
    let feedbackContainer = document.querySelector("#feedback-list");
    const todayDate = new Date().toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "2-digit",
    });
    // Function to extract product ID from the URL
    function getProductIdFromUrl() {
        const urlSegments = window.location.pathname.split("/"); // Split the URL by '/'
        return urlSegments[urlSegments.length - 1]; // Assume the product ID is the last segment
    }

    const productId = getProductIdFromUrl();

    function loadFeedback() {
        // Get active sort from UI
        const sort = $('.feedback-sort-link.active').data('sort') || 'new_to_old';
        
        // Build query string only for sorting
        let query = [];
        if (sort) query.push('feedback_sort=' + encodeURIComponent(sort));
        let queryString = query.length ? '?' + query.join('&') : '';

        $.get(baseUrl + "/products/feedback/" + productId + queryString, function (data) {
            $("#feedback-list").html("");
            let fbLike = 0;
            let fbDislike = 0;
            let fbQuestion = 0;

            data.forEach((fb) => {
                if (fb.type == "like") {
                    fbLike++;
                } else if (fb.type == "dislike") {
                    fbDislike++;
                } else {
                    fbQuestion++;
                }
                renderFeedback(fb);
            });
            document.querySelector("#fbLike").innerHTML = fbLike;
            document.querySelector("#fbDislike").innerHTML = fbDislike;
            document.querySelector("#fbQuestion").innerHTML = fbQuestion;
            document.querySelector("#fbDetailsLike").innerHTML = fbLike;
            document.querySelector("#fbDetailsDislike").innerHTML = fbDislike;
            document.querySelector("#fbDetailsQuestion").innerHTML = fbQuestion;
            handleReplyClick(data);
            applyClientSideFilters();
        });
    }

    // console.log("baseUrl", baseUrl);
    loadFeedback();
    function renderFeedback(fb) {
        // Convert created_at to a readable date format
        const isUpdated = fb.created_at !== fb.updated_at;
        const dateObj = new Date(fb.created_at);
        const hours = String(dateObj.getHours()).padStart(2, '0');
        const minutes = String(dateObj.getMinutes()).padStart(2, '0');
        const year = dateObj.getFullYear();
        const month = dateObj.toLocaleString('en-US', { month: 'short' });
        const day = String(dateObj.getDate()).padStart(2, '0');
        const createdAt = `${hours}:${minutes}  ${year}-${month}-${day}`;
        const dateDisplay = isUpdated ? `Updated ${createdAt}` : createdAt;
        let typeHtml = "";
        if (fb.type == "like") {
            typeHtml = `<i class="bi bi-hand-thumbs-up-fill"></i>`;
        } else if (fb.type == "dislike") {
            typeHtml = `<i class="bi bi-hand-thumbs-down"></i>`;
        } else {
            typeHtml = `<i><span class="question-btn">Q</span></i>`;
        }
        let feedbackHTML = `
        <div class="feedback-item" data-type="${fb.type}">
            <div class="users">
                <div class="users-comments">
                    <div class="user-info d-flex justify-content-start mb-3 align-items-center">
                        ${typeHtml}
                        <h6 class="h6 m-0 px-2">${fb.user.username || "Anonymous"}</h6>
                        <span>${dateDisplay || "Unknown Date"}</span>
                    </div>
                    <div class="w-100 comments-box p-2" data-fb-id="${fb.uuid}">${fb.comment}</div>
                    <textarea class="w-100 write-box p-2 feedback-comment d-none" rows="3" data-fb-id="${fb.uuid}" rows="2" cols="100" placeholder="Type your comment here...">${fb.comment}</textarea>
                    <span class="characters-left d-none">${(1000 -fb.comment.length)} characters left</span>
                    <div class="like-btn-group justify-content-end">
                        <div class="like-btn-group-inner mt-2 d-none">`;
        if (isloggedIn && fb.user.id == isloggedInUserId) {
            feedbackHTML += `
                                            <a href="javascript:void(0)" type="button" class="users-comments-cancel px-2 border-black rounded-10 mx-2">Cancel</a>
                                            <a href="javascript:void(0)" type="button" class="users-comments-btn btn-bordered" data-user-id="${fb.user.id}" data-fb-id="${fb.uuid}">Update</a>`;
        } else {
            feedbackHTML += `
                                            <a href="javascript:void(0)" type="button" class="users-comments-btn btn-bordered">Reply</a>`;
        }
        feedbackHTML += `
                                        </div>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="dropdown-toggle rounded-dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fa-solid fa-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu">`;
        if (isloggedIn && fb.user.id == isloggedInUserId) {
            feedbackHTML += `
                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}">Edit</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}" data-user-id="${fb.user.id}">Delete</a></li>`;
        } else {
            feedbackHTML += `
                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}" data-bs-toggle="modal" data-bs-target="#contactHonleyModal">Flag Issue</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}">Reply</a></li>`;
        }
        feedbackHTML += `
                                </ul>
                            </div>
                        </div>
                    `;
            // Check if the feedback is a reply
            // if (fb.replies && fb.replies.length > 0) {
            feedbackHTML += `<div class="accordion reply-accord" id="accordionExample${fb.id}">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingOne${fb.id}">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne${fb.id}" aria-expanded="false" aria-controls="collapseOne${fb.id}">`;
                                if (fb.replies && fb.replies.length > 0) {
                                    feedbackHTML += `Replies (${fb.replies.length})`;
                                } else {
                                    feedbackHTML += `Replies (0)`;
                                }
                                feedbackHTML += `
                                </button>
                                </h2>
                                <div id="collapseOne${fb.id}" class="accordion-collapse collapse" aria-labelledby="headingOne${fb.id}" data-bs-parent="#accordionExample${fb.id}" style="">
                                <div class="accordion-body">`;
                                    if (fb.replies && fb.replies.length > 0) {
                                        fb.replies.forEach((fb) => {
                                            const isUpdated = fb.created_at !== fb.updated_at;
                                            const dateObj = new Date(fb.created_at);
                                            const hours = String(dateObj.getHours()).padStart(2, '0');
                                            const minutes = String(dateObj.getMinutes()).padStart(2, '0');
                                            const year = dateObj.getFullYear();
                                            const month = dateObj.toLocaleString('en-US', { month: 'short' });
                                            const day = String(dateObj.getDate()).padStart(2, '0');
                                            const createdAt = `${hours}:${minutes}  ${year}-${month}-${day}`;
                                            const dateDisplay = isUpdated ? `Updated ${createdAt}` : createdAt;
                                            feedbackHTML += `
                                                        <div class="users">
                                                            <div class="users-comments">
                                                                <div class="user-info d-flex justify-content-start mb-3 align-items-center">
                                                                    <h6 class="h6 m-0 px-2">${fb.user.username || "Anonymous"}</h6>
                                                                    <span>${dateDisplay || "Unknown Date"}</span>
                                                                </div>
                                                                <div class="w-100 comments-box p-2" data-fb-id="${fb.uuid}">${fb.comment}</div>
                                                                <textarea class="w-100 write-box p-2 feedback-comment d-none" rows="3" data-fb-id="${fb.uuid}" rows="2" cols="100" placeholder="Type your comment here...">${fb.comment}</textarea>
                                                                    <span class="characters-left d-none">${(1000 -fb.comment.length)} characters left</span>
                                                                <div class="like-btn-group justify-content-end">
                                                                    <div class="like-btn-group-inner mt-2 d-none">`;
                                                    if (isloggedIn && fb.user.id == isloggedInUserId) {
                                                        feedbackHTML += `
                                                                        <a href="javascript:void(0)" type="button" class="users-comments-cancel px-2 border-black rounded-10 mx-2">Cancel</a>
                                                                        <a href="javascript:void(0)" type="button" class="users-comments-btn btn-bordered" data-user-id="${fb.user.id}" data-fb-id="${fb.uuid}">Update</a>`;
                                                    } else {
                                                        feedbackHTML += `
                                                                        <a href="javascript:void(0)" type="button" class="users-comments-btn btn-bordered">Reply</a>`;
                                                    }
                                                    feedbackHTML += `
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="dropdown">
                                                                <button class=" dropdown-toggle rounded-dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="fa-solid fa-ellipsis-h"></i>
                                                                </button>
                                                                <ul class="dropdown-menu">`;
                                        if (isloggedIn && fb.user.id == isloggedInUserId) {
                                            feedbackHTML += `
                                                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}">Edit</a></li>
                                                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}" data-user-id="${fb.user.id}">Delete</a></li>`;
                                        } else {
                                            feedbackHTML += `
                                                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}" data-bs-toggle="modal" data-bs-target="#contactHonleyModal">Flag Issue</a></li>
                                                                    <li><a class="dropdown-item" href="javascript:void(0)" data-fb-id="${fb.uuid}">Reply</a></li>`;
                                        }
                                        feedbackHTML += `
                                                                </ul>
                                                            </div>
                                                        </div>`;
                                        });
                                    }                                    
                                        
                                        feedbackHTML +=`<div class="users-comments-reply">
                                                            <div class="users pr-40">
                                                                <div class="users-comments">
                                                                    <div class="user-info d-flex justify-content-start mb-3">
                                                                        <h6 class="h6 m-0 px-2">${isloggedInUserUsername}</h6>
                                                                        <span>${todayDate}</span>
                                                                    </div>
                                                                    <textarea class="w-100 write-box p-2 reply-comment" rows="2" cols="100" placeholder="Write Feedback"></textarea>
                                                                    <span class="characters-left d-none">1000 characters left</span>
                                                                    <div class="like-btn-group d-none">
                                                                        <a href="javascript:void(0)" type="button" class="users-comments-reply-cancel px-2 border-black rounded-10">Cancel</a>
                                                                        <div class="like-btn-group-inner">
                                                                            <a href="javascript:void(0)" type="button" class="users-comments-btn btn-bordered" data-fb-id="${fb.uuid}" data-user-id="${isloggedInUserId}">Post Reply</a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>`;

        // Append the feedback HTML to the container
        if (feedbackContainer) {
            feedbackContainer.insertAdjacentHTML("beforeend", feedbackHTML); // Append without overwriting
        } else {
            console.error("Feedback container not found.");
        }
    }

    document.addEventListener("DOMContentLoaded", function () {
        const feedbackComment = document.getElementById("feedback_comment");
        const likeBtnGroupContainer = document.querySelector(
            ".like-btn-group-container"
        );
        const likeBtnGroupCountContainer = document.querySelector(
            ".like-btn-group-count-container"
        );
        const charactersLeftSpan = document.querySelector(".characters-left");

        if (feedbackComment) {
            feedbackComment.addEventListener("input", function () {
                const maxCharacters = 1000;
                const currentLength = feedbackComment.value.length;
                let remainingCharacters = maxCharacters - currentLength;

                if(remainingCharacters <= 0){
                    feedbackComment.value = feedbackComment.value.substring(0, maxCharacters);
                    remainingCharacters = 0;
                    console.log(remainingCharacters);
                }
                // Update the character count
                charactersLeftSpan.textContent = `${remainingCharacters} characters left`;

                // Show or hide the like-btn-group-container based on input
                if (currentLength > 0) {
                    likeBtnGroupContainer.classList.remove("d-none");
                    likeBtnGroupCountContainer.classList.remove("d-none");
                    charactersLeftSpan.classList.remove("d-none");
                } else {
                    likeBtnGroupContainer.classList.add("d-none");
                    likeBtnGroupCountContainer.classList.add("d-none");
                    charactersLeftSpan.classList.add("d-none");
                }
            });
        }
    });

    document.addEventListener("DOMContentLoaded", function () {
        const buttons = document.querySelectorAll(
            ".btn-bordered.question-type"
        );

        // Add a click event listener to each button
        buttons.forEach((button) => {
            button.addEventListener("click", function () {
                // Get the data-value attribute of the clicked button
                const dataValue = this.getAttribute("data-value");
                feedbackType = dataValue; // Update the feedbackType variable
                // console.log("Data value:", dataValue);

                // Add a selected class to the clicked button (optional)
                buttons.forEach((btn) => btn.classList.remove("selected")); // Remove "selected" class from all buttons
                this.classList.add("selected"); // Add "selected" class to the clicked button
            });
        });
    });

    document.addEventListener("DOMContentLoaded", function () {
        const submitButtons = document.querySelectorAll(".submit-question");
        const feedbackComment = document.getElementById("feedback_comment");
        submitButtons.forEach((submitButton) => {
            if (submitButton && feedbackComment) {
                submitButton.addEventListener("click", function (event) {
                    event.preventDefault(); // Prevent the default button action
                    const feedbackText = feedbackComment.value.trim();
                    const feedbackType = event.currentTarget.getAttribute("data-value");
                    
                    const productId = getProductIdFromUrl(); // Assuming this function is already defined

                    if (!feedbackText) {
                        alert("Please enter your feedback before submitting.");
                        return;
                    }

                    if (!feedbackType) {
                        alert("Please select a feedback type (like or dislike).");
                        return;
                    }

                    // AJAX request to submit feedback
                    $.ajax({
                        url: `${baseUrl}/products/feedback`,
                        method: "POST",
                        data: {
                            comment: feedbackText,
                            type: feedbackType,
                            product_id: productId,
                            _token: document
                                .querySelector('meta[name="csrf-token"]')
                                .getAttribute("content"), // CSRF token
                        },
                        success: function (response) {
                            alert("Feedback submitted successfully!");
                            feedbackComment.value = ""; // Clear the feedback input
                            document
                                .querySelector(".characters-left")
                                .classList.add("d-none");
                            document
                                .querySelector(".like-btn-group-container")
                                .classList.add("d-none");
                            loadFeedback();
                        },
                        error: function (xhr) {
                            alert(
                                "An error occurred while submitting your feedback. Please try again."
                            );
                            console.error(xhr.responseText);
                        },
                    });
                });
            }
        });
    });

    function handleReplyClick(allFeedback) {
        // Add event listener to all "Reply" dropdown items
        document.querySelectorAll(".dropdown-item").forEach((item) => {
            item.addEventListener("click", function (event) {
                // Prevent default behavior
                document
                    .querySelectorAll(".like-btn-group-inner")
                    .forEach((eachitem) => {
                        eachitem.classList.add("d-none");
                    });
                event.preventDefault();
                // Check if the clicked item is the "Reply" option
                if (this.textContent.trim() === "Edit") {
                    const parentCommentsDiv = this.closest(".users");
                    document.querySelectorAll(".users").forEach((item) => {
                        if(item !== parentCommentsDiv) {
                            const commentsBox = item.querySelector(".comments-box");
                            const feedbackComment = item.querySelector(".feedback-comment");
                            const feedbackCharactersLeft = item.querySelector(".characters-left");
                            // Show the "comments-box" and hide the "feedback-comment" textarea
                            if(commentsBox && feedbackComment) {
                            commentsBox.classList.remove("d-none");
                            feedbackComment.classList.add("d-none");
                            }
                            if(feedbackCharactersLeft) {
                                feedbackCharactersLeft.classList.add("d-none");
                            }
                        }
                    });
                    // Find the closest parent with the class "users"

                    if (parentCommentsDiv) {
                        // Find the "comments-box" within the parent div
                        const commentsBox = parentCommentsDiv.querySelector(".comments-box");
                        const feedbackComment = parentCommentsDiv.querySelector(".feedback-comment");
                        const feedbackCharactersLeft = parentCommentsDiv.querySelector(".characters-left");
                        // Hide the "comments-box" and show the "feedback-comment" textarea
                        commentsBox.classList.add("d-none");
                        feedbackComment.classList.remove("d-none");
                        feedbackCharactersLeft.classList.remove("d-none");
                        // Find the "like-btn-group-inner" within the parent div
                        const likeBtnGroupInner =
                            parentCommentsDiv.querySelector(
                                ".like-btn-group-inner"
                            );

                        if (likeBtnGroupInner) {
                            // Remove the "d-none" class to make it visible
                            likeBtnGroupInner.classList.remove("d-none");
                        }
                    }
                }
                // Check if the button text is "Delete"
                if (this.textContent.trim() === "Delete") {
                    // Get the data attributes
                    const feedbackId = this.getAttribute("data-fb-id");
                    const userId = this.getAttribute("data-user-id");

                    // Confirm deletion
                    if (confirm("Are you sure you want to delete this feedback?")) {
                        // AJAX request to delete the feedback
                        $.ajax({
                            url: `${baseUrl}/products/feedback/${feedbackId}`,
                            method: "DELETE", // Use DELETE for deletion
                            data: {
                                user_id: userId,
                                _token: document
                                    .querySelector('meta[name="csrf-token"]')
                                    .getAttribute("content"), // CSRF token
                            },
                            success: function (response) {
                                alert("Feedback deleted successfully!");
                                loadFeedback(); // Reload the feedback list
                            },
                            error: function (xhr) {
                                const response = JSON.parse(xhr.responseText);
                                const message = response.message;
                                alert(message);
                            },
                        });
                    }
                }
                // check if the button text is "Reply"
                if (this.textContent.trim() === "Reply") {
                    // Get the data attributes
                    const feedbackId = this.getAttribute("data-fb-id");
                    const userId = isloggedInUserId;

                    // Find the closest parent with the class "users"
                    const parentCommentsDiv = this.closest(".feedback-item");
                    if (parentCommentsDiv) {
                        console.log("parentCommentsDiv", parentCommentsDiv);
                        console.log("feedbackId", feedbackId);
                        console.log("userId", userId);
                        // Find the accordion section within the parent div
                        const replyAccordion = parentCommentsDiv.querySelector(".reply-accord");

                        if (replyAccordion) {
                            // Open the accordion by adding the "show" class
                            const collapseSection = replyAccordion.querySelector(".accordion-collapse");
                            if (collapseSection) {
                                collapseSection.classList.add("show");
                            }

                            // Ensure the button reflects the expanded state
                            const accordionButton = replyAccordion.querySelector(".accordion-button");
                            if (accordionButton) {
                                accordionButton.classList.remove("collapsed");
                                accordionButton.setAttribute("aria-expanded", "true");
                            }
                            parentCommentsDiv.querySelector('.reply-comment').focus();
                        }
                    }
                }
                // Check if the button text is "Flag Issue"
                if (this.textContent.trim() === "Flag Issue") {
                    // Get the data attributes
                    const feedbackId = this.getAttribute("data-fb-id");
                    const userId = isloggedInUserId;
                    // document.querySelector("#query_subject").value = "Flagged Feedback";
                    
                    // Get the feedback object
                    let currentFeedback = null;
                    if (typeof allFeedback !== "undefined" && allFeedback.length) {
                        currentFeedback = allFeedback.find(f => f.uuid === feedbackId);
                    }
                    let user = "Anonymous";
                    let createdAt = "";
                    if (currentFeedback) {
                        user = currentFeedback.user?.username || "Anonymous";
                        createdAt = new Date(currentFeedback.created_at).toLocaleString();
                    }
                    const entity = "Feedback";
                    const url = window.location.href;
                    const subject = `${entity}, ${user}, ${createdAt}, ${url}`;
                    // Set modal fields
                    document.querySelector("#query_subject").value = subject;
                    document.querySelector("#query_subject").disabled = true;
                    document.querySelector("#query_name").disabled = true;
                }
            });
        });
        document.querySelectorAll(".users-comments-cancel").forEach((item) => {
            item.addEventListener("click", function (event) {
                if(this.closest(".like-btn-group-inner")){
                    this.closest(".like-btn-group-inner").classList.add("d-none");
                }
                // Find the closest parent with the class "users"
                const parentCommentsDiv = this.closest(".users");
                if (parentCommentsDiv) {
                    // Find the "comments-box" and "feedback-comment" within the parent div
                    const commentsBox = parentCommentsDiv.querySelector(".comments-box");
                    const feedbackComment = parentCommentsDiv.querySelector(".feedback-comment");
                    const feedbackCharactersLeft = parentCommentsDiv.querySelector(".characters-left");
                    // Show the "comments-box" and hide the "feedback-comment" textarea
                    if(commentsBox && feedbackComment) {
                        commentsBox.classList.remove("d-none");
                        feedbackComment.classList.add("d-none");
                    }
                    if(feedbackCharactersLeft) {
                        feedbackCharactersLeft.classList.add("d-none");
                    }
                }
            });
        });
        document.querySelectorAll(".users-comments-reply-cancel").forEach((item) => {
            item.addEventListener("click", function (event) {
                // Find the closest parent with the class "users-comments-reply"
                const parentCommentsDiv = this.closest(".users-comments-reply");
                if (parentCommentsDiv) {
                    parentCommentsDiv.querySelector('.characters-left').classList.add("d-none");
                    parentCommentsDiv.querySelector('.like-btn-group').classList.add("d-none");
                    parentCommentsDiv.querySelector('.like-btn-group-inner').classList.add("d-none");
                    parentCommentsDiv.querySelector('.reply-comment').value = "";
                }
            });
        });
        document.querySelectorAll(".reply-comment").forEach((item) => {
            const feedbackReplyComment = item;
            const likeBtnGroupContainer = feedbackReplyComment.closest(".users-comments").querySelector(".like-btn-group");
            const likeBtnGroupCountContainer = feedbackReplyComment.closest(".users-comments").querySelector(".like-btn-group-inner");
            const charactersLeftSpan = feedbackReplyComment.closest(".users-comments").querySelector(".characters-left");
            feedbackReplyComment.addEventListener("input", function () {
                const maxCharacters = 1000;
                const currentLength = feedbackReplyComment.value.length;
                let remainingCharacters = maxCharacters - currentLength;

                if(remainingCharacters <= 0){
                    feedbackReplyComment.value = feedbackReplyComment.value.substring(0, maxCharacters);
                    remainingCharacters = 0;
                }
                // Update the character count
                charactersLeftSpan.textContent = `${remainingCharacters} characters left`;

                // Show or hide the like-btn-group-container based on input
                if (currentLength > 0) {
                    likeBtnGroupContainer.classList.remove("d-none");
                    likeBtnGroupCountContainer.classList.remove("d-none");
                    charactersLeftSpan.classList.remove("d-none");
                } else {
                    likeBtnGroupContainer.classList.add("d-none");
                    likeBtnGroupCountContainer.classList.add("d-none");
                    charactersLeftSpan.classList.add("d-none");
                }
            });
        });
        document.querySelectorAll(".feedback-comment").forEach((item) => {
            const feedbackComment = item;
            const charactersLeftSpan = feedbackComment.closest(".users-comments").querySelector(".characters-left");
            feedbackComment.addEventListener("input", function () {
                const maxCharacters = 1000;
                const currentLength = feedbackComment.value.length;
                let remainingCharacters = maxCharacters - currentLength;
                if(remainingCharacters <= 0){
                    feedbackComment.value = feedbackComment.value.substring(0, maxCharacters);
                    remainingCharacters = 0;
                }
                // Update the character count
                charactersLeftSpan.textContent = `${remainingCharacters} characters left`;
            });
        });
        // Add event listener to all "users-comments-btn" buttons
        document.querySelectorAll(".users-comments-btn").forEach((button) => {
            button.addEventListener("click", function (event) {
                event.preventDefault(); // Prevent default button behavior

                // Check if the button text is "Update"
                if (this.textContent.trim() === "Update") {
                    // Get the data attributes
                    const feedbackId = this.getAttribute("data-fb-id");
                    const userId = this.getAttribute("data-user-id");

                    // Find the parent "users" div
                    const parentCommentsDiv = this.closest(".users");
                    if (parentCommentsDiv) {
                        // Get the updated feedback comment from the textarea
                        const feedbackComment = parentCommentsDiv
                            .querySelector(".feedback-comment")
                            .value.trim();

                        if (!feedbackComment) {
                            alert("Please enter a comment before updating.");
                            return;
                        }

                        // AJAX request to update the feedback
                        $.ajax({
                            url: `${baseUrl}/products/feedback/${feedbackId}`,
                            method: "PUT", // Use PUT for updates
                            data: {
                                comment: feedbackComment,
                                user_id: userId,
                                _token: document
                                    .querySelector('meta[name="csrf-token"]')
                                    .getAttribute("content"), // CSRF token
                            },
                            success: function (response) {
                                alert("Feedback updated successfully!");
                                // Reload the feedback list
                                loadFeedback();
                            },
                            error: function (xhr) {
                                const response = JSON.parse(xhr.responseText);
                                const message = response.message;
                                alert(
                                    message
                                    // "An error occurred while updating your feedback. Please try again."
                                );
                            },
                        });
                    }
                }
                // Check if the button text is "Post Reply"
                if (this.textContent.trim() === "Post Reply") {
                    // Get the data attributes
                    const feedbackId = this.getAttribute("data-fb-id");
                    const userId = this.getAttribute("data-user-id");
                    // Find the parent "users-comments-reply" div
                    const parentCommentsDiv = this.closest(".users-comments-reply");
                    const productId = getProductIdFromUrl(); // Assuming this function is already defined
                    if (parentCommentsDiv) {
                        // Get the updated feedback comment from the textarea
                        const feedbackComment = parentCommentsDiv
                            .querySelector(".reply-comment")
                            .value.trim();

                        if (!feedbackComment) {
                            alert("Please enter a comment before updating.");
                            return;
                        }

                        // AJAX request to update the feedback
                        $.ajax({
                            url: `${baseUrl}/products/feedback`,
                            method: "POST", // Use PUT for updates
                            data: {
                                comment: feedbackComment,
                                user_id: userId,
                                parent_id: feedbackId,
                                product_id: productId,
                                _token: document
                                    .querySelector('meta[name="csrf-token"]')
                                    .getAttribute("content"), // CSRF token
                            },
                            success: function (response) {
                                alert("Feedback reply added successfully!");
                                // Reload the feedback list
                                loadFeedback();
                            },
                            error: function (xhr) {
                                const response = JSON.parse(xhr.responseText);
                                const message = response.message;
                                alert(
                                    message
                                    // "An error occurred while updating your feedback. Please try again."
                                );
                            },
                        });
                    }
                }
            });
        });
    }

    $(document).on('click', '.feedback-sort-link', function(e) {
        e.preventDefault();
        $('.feedback-sort-link').removeClass('active');
        $(this).addClass('active');
        loadFeedback();
    });

    $(document).on('change', '.feedback-filter-checkbox', function() {
        applyClientSideFilters();
        //loadFeedback();
    });

    function applyClientSideFilters() {
        let filters = [];
        $('.feedback-filter-checkbox:checked').each(function () {
            filters.push($(this).val());
        });

        let fbLike = 0;
        let fbDislike = 0;
        let fbQuestion = 0;

        $('.feedback-item').each(function () {
            const type = $(this).data('type');
            if (filters.length === 0 || filters.includes(type)) {
                $(this).show();
                if (type == "like") {
                    fbLike++;
                } else if (type == "dislike") {
                    fbDislike++;
                } else {
                    fbQuestion++;
                }
            } else {
                $(this).hide();
            }
        });

        document.querySelector("#fbLike").innerHTML = fbLike;
        document.querySelector("#fbDislike").innerHTML = fbDislike;
        document.querySelector("#fbQuestion").innerHTML = fbQuestion;
    }
}
