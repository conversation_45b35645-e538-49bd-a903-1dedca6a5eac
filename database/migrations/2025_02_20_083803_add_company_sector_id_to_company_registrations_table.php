<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_registrations', function (Blueprint $table) {
            $table->foreignId('level_2_category_id')
                ->nullable()
                ->after('organisation_size_id')
                ->constrained('categories')
                ->nullOnDelete();

            $table->foreignId('level_3_category_id')
                ->nullable()
                ->after('level_2_category_id')
                ->constrained('categories')
                ->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_registrations', function (Blueprint $table) {
            Schema::table('company_registrations', function (Blueprint $table) {
                $table->dropForeign(['level_2_category_id']);
                $table->dropForeign(['level_3_category_id']);
                $table->dropColumn(['level_2_category_id', 'level_3_category_id']);
            });
        });
    }
};
