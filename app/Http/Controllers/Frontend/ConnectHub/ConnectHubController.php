<?php

namespace App\Http\Controllers\Frontend\ConnectHub;

use App\Events\UserToSiteAdminChatEvent;
use App\Http\Controllers\Controller;
use App\Models\AdminConversation;
use App\Models\AdminMessage;
use Illuminate\Http\Request;

class ConnectHubController extends Controller
{
    public function sendUserToSiteAdminMessage(Request $request)
    {
        $request->validate([
            'message' => 'required|string',
        ]);

        // Check if the user has an existing conversation
        $conversation = AdminConversation::where('user_id', auth()->id())
            ->where(function ($query) {
                $query->where('status', 'open')
                    ->orWhere('status', 'in_progress');
            })
            ->first();

        if ($conversation) {
            // If conversation exists, create a new message
            $message = AdminMessage::create([
                'conversation_id' => $conversation->id,
                'sender_id' => auth()->id(),
                'message' => $request->message,
            ]);
        } else {
            // If no conversation exists, create a new one
            $conversation = AdminConversation::create([
                'user_id' => auth()->id(),
                'status' => 'open',
            ]);
            $message = AdminMessage::create([
                'conversation_id' => $conversation->id,
                'sender_id' => auth()->id(),
                'message' => $request->message,
            ]);
        }


        broadcast(new UserToSiteAdminChatEvent($message))->toOthers();

        return response()->json(['message' => $message->load('sender')]);
    }

    public function initiateChat(Request $request)
    {
        $conversation = AdminConversation::where('user_id', auth()->id())
            ->where(function ($query) {
                $query->where('status', 'open')
                    ->orWhere('status', 'in_progress');
            })
            ->first();

        if ($conversation) {
            $messages = $conversation->messages()->with('sender')->get();

            $formattedMessages = $messages->map(function ($msg) {
                return [
                    'id' => $msg->id,
                    'conversation_id' => $msg->conversation_id,
                    'sender_id' => $msg->sender_id,
                    'message' => $msg->message,
                    'time' => $msg->created_at->format('H:i-d M'),
                    'sender' => [
                        'id' => $msg->sender->id ?? null,
                        'name' => $msg->sender->name ?? null,
                        'email' => $msg->sender->email ?? null,
                    ],
                ];
            });

            return response()->json([
                'messages' => $formattedMessages,
                'data' => $conversation->id
            ]);
        }


        $conversation = AdminConversation::create(['user_id' => auth()->id()]);


        return response()->json(['messages' => [], 'data' => $conversation->id]);
    }

}
