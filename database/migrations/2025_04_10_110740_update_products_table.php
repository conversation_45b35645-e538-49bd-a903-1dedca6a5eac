<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_pricings', function (Blueprint $table) {
            $table->integer('min_order')->nullable()->default(null)->change(); // Minimum order quantity
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
