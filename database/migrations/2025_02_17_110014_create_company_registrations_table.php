<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_registrations', function (Blueprint $table) {
            // Primary key
            $table->id();
        
            // Foreign keys
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('organisation_type_id')->nullable()->constrained('organisation_types')->nullOnDelete();
            $table->foreignId('organisation_size_id')->nullable()->constrained('organisation_sizes')->nullOnDelete();
            $table->unsignedBigInteger('location_type_id');
            $table->unsignedBigInteger('country_id');
        
            // Company details
            $table->string('company_name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('phone_prefix')->nullable();
            $table->string('website_url')->nullable();
            $table->string('position')->nullable();
        
            // Address details
            $table->string('address_line_1', 255)->nullable();
            $table->string('address_line_2', 255)->nullable();
            $table->string('city', 255)->nullable();
            $table->string('postcode', 255)->nullable();
            $table->string('latitude', 255)->nullable();
            $table->string('longitude', 255)->nullable();
        
            // Timestamps
            $table->timestamps();
        });
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_registrations');
    }
};
