<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('previous_subscription_id')->nullable();
            $table->unsignedBigInteger('new_subscription_id');
            $table->string('stripe_customer_id');
            $table->unsignedBigInteger('user_id');
            $table->enum('change_type', ['upgrade', 'downgrade', 'cancellation']);
            $table->text('change_initiator')->nullable();
            $table->timestamp('change_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_logs');
    }
};
