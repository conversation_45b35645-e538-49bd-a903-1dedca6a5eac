<?php

namespace Database\Factories;

use App\Models\AssessmentSection;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<AssessmentSection>
 */
class AssessmentSectionFactory extends Factory
{
    protected $model = AssessmentSection::class;

    public function definition(): array
    {
        return [
            'parent_id' => null, // Can be updated to `AssessmentSection::factory()` for nested sections
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->optional()->paragraph,
            'reason_required' => $this->faker->boolean,
            'document_required' => $this->faker->boolean,
        ];
    }
}
