<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

/**
 * Class Category
 *
 * Represents a category in a hierarchical structure.
 *
 * @package App\Models
 */
class Category extends Model
{
    use HasFactory, HasSlug;

    /** @var integer Type identifier product category */
    public const TYPE_PRODUCT = 1;

    /** @var integer Type identifier service category */
    public const TYPE_SERVICE = 2;


    /** @var integer Status identifier in-active category */
    public const STATUS_INACTIVE = 0;

    /** @var integer Status identifier active category */
    public const STATUS_ACTIVE = 1;

    /** @var integer Status identifier blocked category */
    public const STATUS_SUSPENDED = 2;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'slug',
        'parent_id',
        'description',
        'level',
        'type',
        'status'
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the parent category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * Get the child categories.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    /**
     * Scope for retrieving root categories (level 0).
     *
     * @param $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRoot($query)
    {
        return $query->where('level', 0);
    }

    /**
     * Scope for retrieving categories with a specific level.
     *
     * @param $query
     * @param int $level
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLevel($query, int $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope for retrieving categories with a specific parent.
     *
     * @param $query
     * @param int $parent
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeParent($query, int $parent)
    {
        return $query->where('parent_id', $parent);
    }

    /**
     * Scope for retrieving categories with a specific type.
     *
     * @param $query
     * @param int $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeType($query, int $type)
    {
        return $query->where('type', $type);
    }

    /**
     * scopeStatus
     *
     * @param  mixed $query
     * @param  mixed $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, int $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Check if the category has any children.
     *
     * @return bool
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * Get the users associated with the category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_categories');
    }
    
    /**
     * Get the products associated with the category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'category_product');
    }
}
