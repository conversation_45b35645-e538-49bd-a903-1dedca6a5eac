<?php

namespace Database\Factories;

use App\Models\CompanyAssessmentDocument;
use App\Models\CompanyAssessment;
use App\Models\AssessmentSection;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyAssessmentDocument>
 */
class CompanyAssessmentDocumentFactory extends Factory
{
    protected $model = CompanyAssessmentDocument::class;

    public function definition(): array
    {
        return [
            'assessment_id' => CompanyAssessment::factory(),
            'section_id' => AssessmentSection::factory(),
            'path' => $this->faker->filePath(),
            'uploaded_by' => User::factory(),
        ];
    }
}
