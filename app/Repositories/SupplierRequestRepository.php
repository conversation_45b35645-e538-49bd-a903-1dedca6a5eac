<?php

namespace App\Repositories;

use App\Models\CompanyRegistration;
use App\Models\CompanyRegistrationStatus as RegistrationStatus;

/**
 * Class CompanyRepository
 *
 * Handles data operations for the Company model.
 */
class SupplierRequestRepository extends BaseRepository
{
    /**
     * The CompanyRegistration model instance.
     *
     * @var \App\Models\CompanyRegistration
     */
    protected $model;

    /**
     * CompanyRepository constructor.
     *
     * @param \App\Models\CompanyRegistration $company
     */
    public function __construct(CompanyRegistration $company)
    {
        $this->model = $company;
    }


    /**
     * Get all statuses array
     *
     * @return array
     */
    public function statuses()
    {
        return [
            'Pending' => RegistrationStatus::STATUS_PENDING,
            'Active' => RegistrationStatus::STATUS_ACTIVE,
            'Approve' => RegistrationStatus::STATUS_APPROVED,
            'Reject' => RegistrationStatus::STATUS_REJECTED,
        ];
    }



    /**
     * Retrieve all records from the model by mixed conditions
     *
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByMixedCondition(array $params)
    {
        $query = $this->model->select()->with('supplier', 'registrationStatus','level2Category','level3Category');

        $query->whereHas('registrationStatus', function ($subQuery) use ($params) {
            $subQuery->whereNull('company_id');
            if (isset($params['status'])) {
                $subQuery->where('status', $params['status']);
            }

            /** Order by status, prioritizing `STATUS_PENDING` first */
            $subQuery->orderByRaw(
                "CASE WHEN status = ? THEN 0 ELSE 1 END",
                [RegistrationStatus::STATUS_PENDING]
            );
        });

        return $query->get();
    }


}
