<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_admins', function (Blueprint $table) {
            $table->enum('status', ['active', 'inactive', 'pending', 'draft'])
            ->default('draft')
            ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_admins', function (Blueprint $table) {
            $table->enum('status', ['active', 'inactive', 'pending'])
            ->default('pending')
            ->change();
        })->after('email');
       
    }
};
