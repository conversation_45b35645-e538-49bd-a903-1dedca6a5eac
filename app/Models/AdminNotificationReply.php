<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AdminNotificationReply extends Model
{
    protected $fillable = ['admin_notification_id', 'sender_id', 'message'];

    public function notification()
    {
        return $this->belongsTo(AdminNotification::class, 'admin_notification_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class);
    }
}

