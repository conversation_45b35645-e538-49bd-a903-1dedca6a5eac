<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Feedback extends Model
{
    protected $fillable = ['uuid', 'product_id', 'user_id', 'type', 'comment', 'parent_id'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = (string) Str::uuid();
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function replies()
    {
        return $this->hasMany(Feedback::class, 'parent_id')->with('replies');
    }

    public function parent()
    {
        return $this->belongsTo(Feedback::class, 'parent_id');
    }
}

