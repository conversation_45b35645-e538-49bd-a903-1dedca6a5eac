<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrganisationTypeLocal extends Model
{
    protected $table = 'organisation_types_local';

    protected $fillable = [
        'organisation_type_id',
        'iso_code',
        'local_code',
        'local_name'
    ];

    public function organisation_type()
    {
        return $this->belongsTo(OrganisationType::class, 'organisation_type_id');
    }
}
