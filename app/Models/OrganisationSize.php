<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class OrganisationSize
 *
 * Represents the size of an organisation with a range.
 *
 * @package App\Models
 */
class OrganisationSize extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'range_from',
        'range_to',
    ];

    public function company()
    {
        return $this->hasMany(Company::class);
    }
}
