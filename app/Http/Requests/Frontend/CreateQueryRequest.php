<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class CreateQueryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'query_name' => ['required', 'string', 'max:255'],
            'query_subject' => ['required', 'string', 'max:255'],
            'query_message' => ['required', 'string', 'max:2000'],
            'address' => ['nullable', 'string', 'max:255'],
            'first_name' => [auth()->check() ? 'nullable' : 'required', 'string', 'max:255'],
            'last_name' => [auth()->check() ? 'nullable' : 'required', 'string', 'max:255'],
            'username' => ['nullable', 'string', 'max:255'],
            'company' => ['nullable', 'string', 'max:255'],
            'your_email' => [auth()->check() ? 'nullable' : 'required', 'string', 'max:255'],
        ];
    }

    /**
     * Custom error messages for validation.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'query_name.required' => 'Please select a query category.',
            'query_subject.required' => 'Please enter a subject for your query.',
            'query_message.required' => 'Your message cannot be empty.',
            'first_name.required' => 'Your first name cannot be empty.',
            'last_name.required' => 'Your last name cannot be empty.',
            'your_email.required' => 'Your email cannot be empty.',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function failedValidation(Validator $validator): void
    {
        throw new ValidationException(
            $validator,
            \Redirect::back()
                ->withInput()
                ->withErrors($validator)
                ->with('modalToShow', $this->currentModalToDisplay())
        );
    }

    /**
     * Get the ID of the modal that should be shown after validation failure.
     *
     * @return string
     */
    public function currentModalToDisplay(): string
    {
        return config('settings.modal.contact_honley_modal');
    }

    /**
     * Get the ID of the modal that should be shown after the request is successful.
     *
     * @return string
     */
    public function nextModalToDisplay(): string
    {
        return config('settings.modal.confirmation_modal');
    }
}
