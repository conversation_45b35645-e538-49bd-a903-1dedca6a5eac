<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class MailboxLayerService
{
    protected $apiKey;

    public function __construct()
    {
        $this->apiKey = config('services.mailboxlayer.key');
    }

    public function validateEmail($email)
    {
        $response = Http::get('https://apilayer.net/api/check', [
            'access_key' => $this->apiKey,
            'email' => $email,
            'disposable' => 1,
            'role' => 1,
            'free' => 1
        ]);

        return $response->json();
    }
}
