<?php

namespace App\Http\Controllers\Admin\ConnectHub;

use App\Events\UserToSiteAdminChatEvent;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AdminConversation;
use App\Models\AdminMessage;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;
use App\Events\NewAdminMessage;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\JsonResponse;

class ChatController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->list($request);
        }
        $routePrefix = 'admin.connecthub.chat.action';
        return view('admin.connecthub.chat.index', [
            'routePrefix' => $routePrefix,
        ]);
    }

    protected function list(Request $request): JsonResponse
    {
        $chats = AdminConversation::with(['user', 'messages'])
            //->whereIn('status', ['open', 'in_progress'])
            ->orderBy('id', 'desc');

        $user = Auth::user();

        return DataTables::of($chats)
            ->addIndexColumn()
            ->addColumn('user', function ($chat) {
                return $chat->user->name . '<br><small>' . $chat->user->email . '</small>';
            })
            ->addColumn('company', function ($chat) {
                return $chat->user->company->name ?? '-';
            })
            ->addColumn('status', function ($chat) {
                $status = ucfirst(str_replace('_', ' ', $chat->status));

                $class = match ($chat->status) {
                    'open' => 'info',
                    'in_progress' => 'warning',
                    'resolved' => 'success',
                    'escalated' => 'danger',
                    default => 'secondary',
                };

                return '<div class="badge bg-' . $class . '">' . $status . '</div>';
            })

            ->addColumn('last_message', function ($chat) {
                return $chat->messages->last()->message ?? '-';
            })
            ->addColumn('updated_at', function ($chat) {
                return $chat->updated_at->format('d M Y, h:i A');
            })

            ->addColumn('action', function ($chat) use ($user) {
                $action = '';

                // Always show the "Reply" button
                if ($user->can('view chats') || $user->can('reply chats')) {
                    $action .= '<a href="' . route('admin.connecthub.chat.show', $chat->id) . '" class="btn btn-primary">View & Reply</a> ';
                }

                // Dropdown with status actions
                if ($user->can('manage chats')) {
                    $action .= '<div class="btn-group">
                        <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                            Action
                        </button>
                        <ul class="dropdown-menu">';

                    // Available statuses
                    $statuses = [
                        'open' => 'Open',
                        'in_progress' => 'In Progress',
                        'resolved' => 'Resolved',
                        'escalated' => 'Escalated',
                    ];

                    foreach ($statuses as $key => $label) {
                        if ($chat->status !== $key) {
                            $action .= '<li class="dropdown-item">
                    <button class="btn btn-primary change-status-btn"
                        data-id="' . $chat->id . '"
                        data-statusname="' . $label . '"
                        data-status="' . $key . '">
                        Mark as ' . $label . '
                    </button>
                </li>';
                        }
                    }

                    $action .= '</ul></div>';
                }

                return $action;
            })


            ->rawColumns(['user', 'status', 'action'])
            ->toJson();
    }



    public function show(AdminConversation $conversation)
    {
        $conversation->load(['user', 'messages.sender']);

        return view('admin.connecthub.chat.show', [
            'conversation' => $conversation,
            'showChatBox' => true,
        ]);
    }


    public function sendMessage(Request $request, $conversationId)
    {
        $request->validate([
            'message' => 'required|string',
        ]);

        $conversation = AdminConversation::findOrFail($conversationId);

        $message = new AdminMessage();
        $message->conversation_id = $conversation->id;
        $message->sender_id = Auth::id(); // Admin sending message
        $message->message = $request->message;
        $message->save();

        // Mark conversation status if needed
        $conversation->status = 'in_progress';
        $conversation->updated_at = now();
        $conversation->save();

        // Trigger Pusher event
        //broadcast(new NewAdminMessage($message))->toOthers();
        broadcast(new UserToSiteAdminChatEvent($message))->toOthers();

        return response()->json(['status' => 'Message Sent']);
    }

    public function updateStatus(Request $request, AdminConversation $conversation)
    {
        $request->validate([
            'status' => 'required|in:open,in_progress,resolved,escalated',
        ]);

        $conversation->status = $request->status;
        $conversation->save();

        return response()->json(['message' => 'Status updated successfully.']);
    }

}
