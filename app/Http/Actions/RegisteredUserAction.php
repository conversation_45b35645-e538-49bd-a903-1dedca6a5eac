<?php

namespace App\Http\Actions;
use App\Models\CompanyAdmin;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Session;

class RegisteredUserAction
{
    /**
     * Handles the registration of a new user.
     *
     * @param array $data The user data for registration.
     * @return void
     */
    public function __invoke(array $data): User
    {

        $data['password'] = \Hash::make($data['password']);

        $user = \DB::transaction(function () use ($data) {
            $data['status'] = 1;
            $user = User::create($data);
            $companyAdmin = CompanyAdmin::where('email', $user->email)->first();
            if ($companyAdmin) {
                $companyAdmin->user_id = $user->id;
                $companyAdmin->status = 'pending';
                $companyAdmin->save();
                $user->assignRole(User::ROLE_SUPPLIER);
                $user->syncPermissions($user, json_decode($companyAdmin->permissions, true));
            } else {
                $user->assignRole(User::ROLE_USER);
            }

            return $user;
        });

        $user->sendEmailVerification();
        Session::put('registeredUserId', $user->id);

        return $user;
    }
}
