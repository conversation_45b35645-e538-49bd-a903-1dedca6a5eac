<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class PlanFeature extends Model
{
    use HasSlug;
    const STATUS_ACTIVE = 1;

    const STATUS_INACTIVE = 0;

    const FEATURE_TYPE_MARKETING = 1;
    const FEATURE_TYPE_PLATFORM = 2;

    protected $fillable = [
        'stripe_subscription_id',
        'name',
        'slug',
        'description',
        'status',
        'feature_type',
        'updated_by',
    ];

    // protected $with = ['stripeSubscription'];


    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    public static function getStatusLabelAndStyle($status)
    {
        $statuses = [
            self::STATUS_ACTIVE => [
                'label' => 'Active',
                'style' => 'background-color:#28a745; color:#fff; text-align:center; border-radius:15px; padding:5px 10px; display:inline-block;'
            ],
            self::STATUS_INACTIVE => [
                'label' => 'Inactive',
                'style' => 'background-color:#dc3545; color:#fff; text-align:center; border-radius:15px; padding:5px 10px; display:inline-block;'
            ],
        ];


        return $statuses[$status] ?? ['label' => 'Unknown', 'style' => 'background-color:#6c757d;'];
    }

    /**
     * stripeSubscription
     *
     * @return BelongsTo
     */
    // public function stripeSubscription(): BelongsTo
    // {
    //     return $this->belongsTo(StripeSubscription::class);
    // }

    public function stripeSubscriptions(): BelongsToMany
    {
        return $this->belongsToMany(StripeSubscription::class, 'subscription_features')
        ->withPivot('limit');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
    public function subscriptionFeatures()
    {
        return $this->hasMany(SubscriptionFeature::class, 'feature_id');
    }
    
}
