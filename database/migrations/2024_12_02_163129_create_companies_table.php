<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organisation_type_id')->nullable()
                ->constrained('organisation_types')->nullOnDelete();
            $table->foreignId('organisation_size_id')->nullable()
                ->constrained('organisation_sizes')->nullOnDelete();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('phone_prefix')->nullable();
            $table->integer('year_founded')->nullable();
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->string('cover_image')->nullable();
            $table->string('website_url')->nullable();
            $table->boolean('workplace_verification')->default(false);
            $table->boolean('display_phone')->default(true);
            $table->string('phone_contact_title')->nullable();
            $table->string('chat_contact_title')->nullable();
            $table->text('compliance_summary')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
