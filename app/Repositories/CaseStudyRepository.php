<?php

namespace App\Repositories;

use App\Models\CaseStudy;

/**
 * Class CaseStudyRepository
 *
 * Handles data operations for the CaseStudy model.
 */
class CaseStudyRepository extends BaseRepository
{
    /**
     * The CaseStudy model instance.
     *
     * @var \App\Models\CaseStudy
     */
    protected $model;

    /**
     * CaseStudyRepository constructor.
     *
     * @param \App\Models\CaseStudy $caseStudy
     */
    public function __construct(CaseStudy $caseStudy)
    {
        $this->model = $caseStudy;
    }
}
